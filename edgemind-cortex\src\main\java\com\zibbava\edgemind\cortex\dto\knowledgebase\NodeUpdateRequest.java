package com.zibbava.edgemind.cortex.dto.knowledgebase;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 更新知识库节点请求的数据传输对象 (DTO)。
 * 支持重命名节点和移动节点到新的父节点下。
 */
@Data
public class NodeUpdateRequest {

    /**
     * 节点的新名称 (必填，有长度限制)。
     * 只允许字母、数字、中文、下划线、横线、空格、小数点和圆括号（包括中文圆括号）。
     */
    @NotBlank(message = "不能为空")
    @Size(max = 200, message = "长度不能超过 200 个字符")
    private String name;

    /**
     * 新的父节点ID。
     * 如果要移动节点到新的父节点下，设置此字段。
     * 如果要移动到根节点，设置为null或空字符串。
     * 如果不需要移动节点，不设置此字段。
     */
    private String parentNodeId;
}