package com.zibbava.edgemind.server.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 许可证工具类
 * 用于生成和验证许可证
 */
@Slf4j
public class LicenseUtils {

    // 加密密钥，实际应用中应该存储在更安全的地方
    private static final String SECRET_KEY = "WeekliesForge2024";
    private static final String ALGORITHM = "AES";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 生成许可证密钥
     *
     * @param hardwareFingerprint 硬件指纹
     * @param systemIdentifier    系统唯一标识
     * @param expireTime          过期时间，null表示永不过期
     * @return 加密的许可证密钥
     */
    public static String generateLicenseKey(String hardwareFingerprint, String systemIdentifier, LocalDateTime expireTime) {
        try {
            // 构建许可证内容
            StringBuilder licenseContent = new StringBuilder();
            licenseContent.append("HW:").append(hardwareFingerprint).append(";");
            licenseContent.append("SYS:").append(systemIdentifier).append(";");
            
            // 添加过期时间（如果有）
            if (expireTime != null) {
                licenseContent.append("EXP:").append(expireTime.format(DATE_FORMATTER)).append(";");
            } else {
                licenseContent.append("EXP:NEVER;");
            }
            
            // 添加创建时间
            licenseContent.append("CRT:").append(LocalDateTime.now().format(DATE_FORMATTER)).append(";");
            
            // 添加校验和
            String checksum = DigestUtils.sha256Hex(licenseContent.toString());
            licenseContent.append("CHK:").append(checksum);
            
            // 加密许可证内容
            return encrypt(licenseContent.toString());
        } catch (Exception e) {
            log.error("生成许可证密钥失败", e);
            return null;
        }
    }

    /**
     * 验证许可证密钥
     *
     * @param licenseKey          许可证密钥
     * @param hardwareFingerprint 当前硬件指纹
     * @param systemIdentifier    当前系统唯一标识
     * @return 验证结果，true表示验证通过
     */
    public static boolean verifyLicenseKey(String licenseKey, String hardwareFingerprint, String systemIdentifier) {
        try {
            // 解密许可证内容
            String decryptedContent = decrypt(licenseKey);
            if (decryptedContent == null) {
                return false;
            }
            
            // 解析许可证内容
            String hwFingerprint = extractValue(decryptedContent, "HW:");
            String sysId = extractValue(decryptedContent, "SYS:");
            String expireTimeStr = extractValue(decryptedContent, "EXP:");
            String checksumFromLicense = extractValue(decryptedContent, "CHK:");
            
            // 验证校验和
            String contentWithoutChecksum = decryptedContent.substring(0, decryptedContent.indexOf("CHK:"));
            String calculatedChecksum = DigestUtils.sha256Hex(contentWithoutChecksum);
            
            if (!calculatedChecksum.equals(checksumFromLicense)) {
                log.warn("许可证校验和不匹配");
                return false;
            }
            
            // 验证硬件指纹
            if (!hardwareFingerprint.equals(hwFingerprint)) {
                log.warn("硬件指纹不匹配: 期望={}, 实际={}", hwFingerprint, hardwareFingerprint);
                return false;
            }
            
            // 验证系统标识
            if (!systemIdentifier.equals(sysId)) {
                log.warn("系统标识不匹配: 期望={}, 实际={}", sysId, systemIdentifier);
                return false;
            }
            
            // 验证过期时间
            if (!"NEVER".equals(expireTimeStr)) {
                LocalDateTime expireTime = LocalDateTime.parse(expireTimeStr, DATE_FORMATTER);
                if (LocalDateTime.now().isAfter(expireTime)) {
                    log.warn("许可证已过期: {}", expireTimeStr);
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("验证许可证失败", e);
            return false;
        }
    }

    /**
     * 从许可证中提取过期时间
     *
     * @param licenseKey 许可证密钥
     * @return 过期时间，如果是永不过期或解析失败则返回null
     */
    public static LocalDateTime getExpireTimeFromLicense(String licenseKey) {
        try {
            String decryptedContent = decrypt(licenseKey);
            if (decryptedContent == null) {
                return null;
            }
            
            String expireTimeStr = extractValue(decryptedContent, "EXP:");
            if ("NEVER".equals(expireTimeStr)) {
                return null;
            }
            
            return LocalDateTime.parse(expireTimeStr, DATE_FORMATTER);
        } catch (Exception e) {
            log.error("从许可证中提取过期时间失败", e);
            return null;
        }
    }

    /**
     * 从许可证内容中提取指定标签的值
     *
     * @param content 许可证内容
     * @param tag     标签
     * @return 标签对应的值
     */
    private static String extractValue(String content, String tag) {
        int startIndex = content.indexOf(tag) + tag.length();
        int endIndex = content.indexOf(";", startIndex);
        if (endIndex == -1) {
            // 可能是最后一个值，没有分号结尾
            return content.substring(startIndex);
        }
        return content.substring(startIndex, endIndex);
    }

    /**
     * 加密字符串
     *
     * @param content 要加密的内容
     * @return 加密后的Base64字符串
     */
    private static String encrypt(String content) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(getKey(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            byte[] encrypted = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(encrypted);
        } catch (Exception e) {
            log.error("加密失败", e);
            return null;
        }
    }

    /**
     * 解密字符串
     *
     * @param encryptedContent 加密的Base64字符串
     * @return 解密后的内容
     */
    private static String decrypt(String encryptedContent) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(getKey(), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            byte[] decoded = Base64.decodeBase64(encryptedContent);
            byte[] decrypted = cipher.doFinal(decoded);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("解密失败", e);
            return null;
        }
    }

    /**
     * 获取加密密钥
     *
     * @return 密钥字节数组
     */
    private static byte[] getKey() {
        // 确保密钥长度为16字节（128位）
        return DigestUtils.md5(SECRET_KEY.getBytes(StandardCharsets.UTF_8));
    }
}
