package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.ChatConversation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 聊天会话Mapper接口
 */
@Mapper
public interface ChatConversationMapper extends BaseMapper<ChatConversation> {

    /**
     * 根据用户ID查询所有有效会话，按更新时间倒序排列
     * 
     * @param userId 用户ID
     * @return 会话列表
     */
    @Select("SELECT * FROM chat_conversation WHERE user_id = #{userId} AND status = 1 ORDER BY update_time DESC")
    List<ChatConversation> selectActiveByUserId(@Param("userId") Long userId);
    
    /**
     * 根据用户ID查询最近的N条有效会话，按更新时间倒序排列
     * 
     * @param userId 用户ID
     * @param limit 限制条数
     * @return 会话列表
     */
    @Select("SELECT * FROM chat_conversation WHERE user_id = #{userId} AND status = 1 ORDER BY update_time DESC LIMIT #{limit}")
    List<ChatConversation> selectRecentActiveByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 分页查询用户的有效会话，按更新时间倒序排列
     * 
     * @param userId 用户ID
     * @param offset 偏移量
     * @param limit 限制条数
     * @return 会话列表
     */
    @Select("SELECT * FROM chat_conversation WHERE user_id = #{userId} AND status = 1 ORDER BY update_time DESC LIMIT #{limit} OFFSET #{offset}")
    List<ChatConversation> selectPagedActiveByUserId(
        @Param("userId") Long userId, 
        @Param("offset") Integer offset, 
        @Param("limit") Integer limit);
} 