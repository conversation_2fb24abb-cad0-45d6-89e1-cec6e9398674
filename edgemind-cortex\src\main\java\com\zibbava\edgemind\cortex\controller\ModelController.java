package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.zibbava.edgemind.cortex.service.ModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 模型控制器，提供模型信息相关的API
 */
@RestController
@RequestMapping("/api")
public class ModelController {
    
    @Autowired
    private ModelService modelService;
    
    /**
     * 获取所有可用的模型列表
     * @return 模型列表，包含模型ID和名称
     */
    @SaIgnore
    @GetMapping("/models")
    public ResponseEntity<?> getModels() {
        List<Map<String, String>> modelInfoList = modelService.getModelsForFrontend();
        return ResponseEntity.ok(modelInfoList);
    }
} 