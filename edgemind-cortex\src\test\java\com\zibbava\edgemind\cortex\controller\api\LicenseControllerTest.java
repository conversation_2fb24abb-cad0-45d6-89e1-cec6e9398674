package com.zibbava.edgemind.cortex.controller.api;

import com.zibbava.edgemind.cortex.base.BaseControllerTest;
import com.zibbava.edgemind.cortex.service.LicenseService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * LicenseController 单元测试
 * 测试许可证相关接口功能，包括硬件指纹获取、许可证验证等
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
@DisplayName("许可证控制器测试")
class LicenseControllerTest extends BaseControllerTest {

    @MockBean
    private LicenseService licenseService;

    @Test
    @DisplayName("获取硬件指纹 - 成功")
    void getHardwareFingerprint_Success() throws Exception {
        // Given
        when(licenseService.getHardwareFingerprint()).thenReturn("test-fingerprint");

        // When & Then
        mockMvc.perform(withAuth(get("/api/license/fingerprint")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value("test-fingerprint"));
    }

    @Test
    @DisplayName("获取硬件信息 - 成功")
    void getDetailedHardwareInfo_Success() throws Exception {
        // Given
        when(licenseService.getDetailedHardwareInfo()).thenReturn("Test Hardware Info");

        // When & Then
        mockMvc.perform(withAuth(get("/api/license/hardware-info")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value("Test Hardware Info"));
    }

    @Test
    @DisplayName("验证许可证 - 成功")
    void verifyLicense_Success() throws Exception {
        // Given
        when(licenseService.verifyLicense("test-license")).thenReturn(true);

        // When & Then
        mockMvc.perform(withAuth(post("/api/license/verify")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("licenseKey", "test-license")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    @DisplayName("验证许可证 - 失败")
    void verifyLicense_Failed() throws Exception {
        // Given
        when(licenseService.verifyLicense("invalid-license")).thenReturn(false);

        // When & Then
        mockMvc.perform(withAuth(post("/api/license/verify")
                .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                .param("licenseKey", "invalid-license")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(false));
    }
} 