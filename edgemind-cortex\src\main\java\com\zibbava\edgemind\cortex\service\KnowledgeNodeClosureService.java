package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.entity.KnowledgeNode;

import java.util.List;

/**
 * 知识库节点闭包表服务接口
 * 提供对节点树结构的高效查询和管理功能
 */
public interface KnowledgeNodeClosureService {

    /**
     * 初始化节点的闭包表关系
     * 当创建新节点时调用此方法
     *
     * @param nodeId 新创建的节点ID
     */
    void initNodeClosure(String nodeId);

    /**
     * 删除节点的闭包表关系
     * 当删除节点时调用此方法
     *
     * @param nodeId 要删除的节点ID
     */
    void deleteNodeClosure(String nodeId);

    /**
     * 更新节点的闭包表关系
     * 当节点的父节点发生变化时调用此方法
     *
     * @param nodeId       要更新的节点ID
     * @param newParentId  新的父节点ID
     */
    void updateNodeClosure(String nodeId, String newParentId);

    /**
     * 获取指定节点的所有后代节点
     *
     * @param ancestorId 祖先节点ID
     * @return 所有后代节点列表
     */
    List<KnowledgeNode> getDescendants(String ancestorId);

    /**
     * 获取指定节点的所有祖先节点
     *
     * @param descendantId 后代节点ID
     * @return 所有祖先节点列表
     */
    List<KnowledgeNode> getAncestors(String descendantId);

    /**
     * 获取指定节点的直接子节点
     *
     * @param parentId 父节点ID
     * @return 直接子节点列表
     */
    List<KnowledgeNode> getDirectChildren(String parentId);

    /**
     * 获取指定空间的根节点列表
     *
     * @param spaceId 空间ID
     * @return 根节点列表
     */
    List<KnowledgeNode> getRootNodes(String spaceId);

    /**
     * 分页获取指定空间的节点
     *
     * @param spaceId    空间ID
     * @param ancestorId 祖先节点ID（可选，如果提供则只查询其后代）
     * @param offset     偏移量
     * @param limit      限制数量
     * @return 节点列表
     */
    List<KnowledgeNode> getNodesPaged(String spaceId, String ancestorId, int offset, int limit);

    /**
     * 获取指定空间的节点总数
     *
     * @param spaceId    空间ID
     * @param ancestorId 祖先节点ID（可选，如果提供则只计算其后代）
     * @return 节点总数
     */
    int countNodes(String spaceId, String ancestorId);

    /**
     * 删除所有节点闭包关系
     * 在清空知识库数据时使用
     */
    void deleteAllClosures();
}
