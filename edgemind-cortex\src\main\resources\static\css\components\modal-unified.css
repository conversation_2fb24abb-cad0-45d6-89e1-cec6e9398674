/**
 * 统一模态框样式 - 极简风格
 * 适用于AI对话和知识库的重命名/删除弹窗
 */

/* 基础模态框样式 */
.modal-unified .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 模态框头部 - 极简设计 */
.modal-unified .modal-header {
    background: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    padding: 20px 24px 16px 24px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.modal-unified .modal-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
}

.modal-unified .btn-close {
    background: none;
    border: none;
    font-size: 18px;
    opacity: 0.5;
    transition: opacity 0.2s ease;
    padding: 0;
    width: 24px;
    height: 24px;
}

.modal-unified .btn-close:hover {
    opacity: 0.8;
}

.modal-unified .btn-close:focus {
    box-shadow: none;
    opacity: 0.8;
}

/* 模态框主体 */
.modal-unified .modal-body {
    padding: 20px 24px;
    background: #ffffff;
    color: #333333;
    line-height: 1.5;
}

.modal-unified .modal-body p {
    margin-bottom: 12px;
    font-size: 14px;
}

.modal-unified .modal-body p:last-child {
    margin-bottom: 0;
}

.modal-unified .modal-body strong {
    font-weight: 600;
    color: #1a1a1a;
}

.modal-unified .modal-body .text-muted {
    color: #666666 !important;
    font-size: 13px;
}

.modal-unified .modal-body .text-danger {
    color: #dc3545 !important;
    font-size: 13px;
}

/* 表单控件样式 */
.modal-unified .form-control {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.modal-unified .form-control:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
    outline: none;
}

.modal-unified .form-control.is-invalid {
    border-color: #dc3545;
}

.modal-unified .form-label {
    font-size: 13px;
    font-weight: 500;
    color: #555555;
    margin-bottom: 6px;
}

.modal-unified .invalid-feedback {
    font-size: 12px;
    color: #dc3545;
    margin-top: 4px;
}

/* 模态框底部 - 极简按钮设计 */
.modal-unified .modal-footer {
    background: #fafafa;
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px 20px 24px;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 按钮统一样式 */
.modal-unified .btn {
    border-radius: 8px;
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 500;
    border: none;
    transition: all 0.2s ease;
    min-width: 80px;
}

/* 小尺寸按钮样式 - 覆盖全局btn-sm定义 */
.modal-unified .btn-sm {
    padding: 6px 16px;
    font-size: 13px;
    min-width: 70px;
}

.modal-unified .btn-secondary {
    background: #ffffff;
    color: #666666;
    border: 1px solid #e0e0e0;
}

.modal-unified .btn-secondary:hover {
    background: #f5f5f5;
    color: #555555;
    border-color: #d0d0d0;
}

.modal-unified .btn-primary {
    background: #1976d2;
    color: #ffffff;
}

.modal-unified .btn-primary:hover {
    background: #1565c0;
}

.modal-unified .btn-danger {
    background: #dc3545;
    color: #ffffff;
}

.modal-unified .btn-danger:hover {
    background: #c82333;
}

/* 按钮禁用状态 */
.modal-unified .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 加载状态 */
.modal-unified .spinner-border-sm {
    width: 14px;
    height: 14px;
    margin-right: 6px;
}

/* 响应式设计 */
@media (max-width: 576px) {
    .modal-unified .modal-header,
    .modal-unified .modal-body,
    .modal-unified .modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
    
    .modal-unified .modal-footer {
        flex-direction: column;
        gap: 8px;
    }
    
    .modal-unified .btn {
        width: 100%;
        justify-content: center;
    }
}

/* 特殊样式：删除确认弹窗 */
.modal-unified.modal-delete .modal-body {
    padding-top: 24px;
}

.modal-unified.modal-delete .modal-body p:first-child {
    font-size: 15px;
    margin-bottom: 16px;
}

/* 特殊样式：重命名弹窗 */
.modal-unified.modal-rename .modal-body {
    padding-bottom: 24px;
}

.modal-unified.modal-rename .form-control {
    margin-top: 8px;
}