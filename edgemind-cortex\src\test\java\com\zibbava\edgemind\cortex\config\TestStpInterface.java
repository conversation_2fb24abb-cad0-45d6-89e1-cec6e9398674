package com.zibbava.edgemind.cortex.config;

import cn.dev33.satoken.stp.StpInterface;
import org.springframework.boot.test.context.TestComponent;
import org.springframework.context.annotation.Primary;

import java.util.List;

/**
 * 测试环境的StpInterface实现
 * 为所有测试用户提供完整的权限和角色，确保权限检查通过
 */
@TestComponent
@Primary
public class TestStpInterface implements StpInterface {

    @Override
    public List<String> getPermissionList(Object loginId, String loginType) {
        // 返回所有可能需要的权限，确保权限检查通过
        return List.of(
            "*",                    // 通配符权限
            "admin:all",           // 管理员权限
            "user:manage",         // 用户管理权限
            "system:admin",        // 系统管理权限
            "user:read",           // 用户读取权限
            "user:write",          // 用户写入权限
            "role:manage",         // 角色管理权限
            "permission:manage",   // 权限管理权限
            "department:manage",   // 部门管理权限
            "knowledge:manage",    // 知识库管理权限
            "chat:manage",         // 聊天管理权限
            "template:manage",     // 模板管理权限
            "license:manage"       // 许可证管理权限
        );
    }

    @Override
    public List<String> getRoleList(Object loginId, String loginType) {
        // 返回所有可能需要的角色，确保角色检查通过
        return List.of(
            "admin",               // 管理员角色
            "super_admin",         // 超级管理员角色
            "user",                // 普通用户角色
            "manager",             // 管理者角色
            "operator"             // 操作员角色
        );
    }
} 