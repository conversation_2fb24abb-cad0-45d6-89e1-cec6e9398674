package com.zibbava.edgemind.cortex.controller;

import com.zibbava.edgemind.cortex.base.BaseControllerTest;
import com.zibbava.edgemind.cortex.common.enums.NodeType;
import com.zibbava.edgemind.cortex.dto.knowledgebase.KnowledgeNodeDto;
import com.zibbava.edgemind.cortex.dto.knowledgebase.NodeCreateRequest;
import com.zibbava.edgemind.cortex.dto.knowledgebase.NodeUpdateRequest;
import com.zibbava.edgemind.cortex.entity.KnowledgeDocument;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import com.zibbava.edgemind.cortex.service.KnowledgeBaseService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * KnowledgeBaseController 单元测试
 * 测试知识库管理相关接口功能，包括空间管理、节点操作、文档上传等
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class KnowledgeBaseControllerTest extends BaseControllerTest {

    @MockBean
    private KnowledgeBaseService knowledgeBaseService;

    private KnowledgeSpace mockSpace;
    private KnowledgeNode mockNode;
    private KnowledgeDocument mockDocument;
    private List<KnowledgeNodeDto> mockNodeDtos;

    @Override
    protected void setUp() {
        // 初始化测试数据
        mockSpace = new KnowledgeSpace();
        mockSpace.setSpaceId("space-1");
        mockSpace.setName("测试空间");
        mockSpace.setOwnerUserId(getMockUserId());
        mockSpace.setIsPrivate(true);
        mockSpace.setCreateTime(LocalDateTime.now());

        mockNode = new KnowledgeNode();
        mockNode.setNodeId("node-1");
        mockNode.setName("测试节点");
        mockNode.setSpaceId("space-1");
        mockNode.setCreateTime(LocalDateTime.now());

        mockDocument = new KnowledgeDocument();
        mockDocument.setDocumentId("doc-1");
        mockDocument.setNodeId("node-1");
        // KnowledgeDocument没有fileName字段，文件名存储在KnowledgeNode中

        KnowledgeNodeDto nodeDto = new KnowledgeNodeDto();
        nodeDto.setNodeId("node-1");
        nodeDto.setName("测试节点");
        mockNodeDtos = Arrays.asList(nodeDto);
    }

    @Test
    void getSpaces_Success() throws Exception {
        // Given
        List<KnowledgeSpace> spaces = Arrays.asList(mockSpace);
        when(knowledgeBaseService.getAccessibleSpaces(getMockUserId())).thenReturn(spaces);

        // When & Then
        mockMvc.perform(withAuth(get("/api/knowledgebase/spaces")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].id").value("space-1"))
                .andExpect(jsonPath("$.data[0].name").value("测试空间"));

        verify(knowledgeBaseService).getAccessibleSpaces(getMockUserId());
    }

    @Test
    void ensureKnowledgeSpace_Private_Success() throws Exception {
        // Given
        when(knowledgeBaseService.getOrCreateKnowledgeSpace(getMockUserId(), true)).thenReturn(mockSpace);

        // When & Then
        mockMvc.perform(withAuth(get("/api/knowledgebase/spaces/ensure"))
                        .param("type", "private"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.id").value("space-1"))
                .andExpect(jsonPath("$.data.name").value("测试空间"));

        verify(knowledgeBaseService).getOrCreateKnowledgeSpace(getMockUserId(), true);
    }

    @Test
    void ensureKnowledgeSpace_Team_Success() throws Exception {
        // Given
        when(knowledgeBaseService.getOrCreateKnowledgeSpace(getMockUserId(), false)).thenReturn(mockSpace);

        // When & Then
        mockMvc.perform(withAuth(get("/api/knowledgebase/spaces/ensure"))
                        .param("type", "team"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.id").value("space-1"))
                .andExpect(jsonPath("$.data.name").value("测试空间"));

        verify(knowledgeBaseService).getOrCreateKnowledgeSpace(getMockUserId(), false);
    }

    @Test
    void ensureKnowledgeSpace_InvalidType() throws Exception {
        // When & Then
        mockMvc.perform(withAuth(get("/api/knowledgebase/spaces/ensure"))
                        .param("type", "invalid"))
                .andExpect(status().isBadRequest());

        verifyNoInteractions(knowledgeBaseService);
    }

    @Test
    void getSpaceTree_Success() throws Exception {
        // Given
        when(knowledgeBaseService.getSpaceTree("space-1")).thenReturn(mockNodeDtos);

        // When & Then
        mockMvc.perform(withAuth(get("/api/knowledgebase/spaces/space-1/tree")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].nodeId").value("node-1"))
                .andExpect(jsonPath("$.data[0].name").value("测试节点"));

        verify(knowledgeBaseService).getSpaceTree("space-1");
    }

    @Test
    void createNode_Success() throws Exception {
        // Given
        NodeCreateRequest request = new NodeCreateRequest();
        request.setName("新节点");
        request.setParentNodeId("parent-1");
        request.setSpaceId("space-1");
        request.setType(NodeType.FOLDER);

        KnowledgeNodeDto nodeDto = new KnowledgeNodeDto();
        nodeDto.setNodeId("node-1");
        nodeDto.setName("测试节点");
        when(knowledgeBaseService.createNode(any(NodeCreateRequest.class)))
                .thenReturn(nodeDto);

        // When & Then
        mockMvc.perform(withAuth(post("/api/knowledgebase/nodes"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.nodeId").value("node-1"))
                .andExpect(jsonPath("$.data.name").value("测试节点"));

        verify(knowledgeBaseService).createNode(any(NodeCreateRequest.class));
    }

    @Test
    void updateNode_Success() throws Exception {
        // Given
        NodeUpdateRequest request = new NodeUpdateRequest();
        request.setName("更新节点");

        KnowledgeNodeDto updatedNodeDto = new KnowledgeNodeDto();
        updatedNodeDto.setNodeId("node-1");
        updatedNodeDto.setName("测试节点");
        when(knowledgeBaseService.updateNode(eq("node-1"), any(NodeUpdateRequest.class)))
                .thenReturn(updatedNodeDto);

        // When & Then
        mockMvc.perform(withAuth(put("/api/knowledgebase/nodes/node-1"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.nodeId").value("node-1"))
                .andExpect(jsonPath("$.data.name").value("测试节点"));

        verify(knowledgeBaseService).updateNode(eq("node-1"), any(NodeUpdateRequest.class));
    }

    @Test
    void deleteNode_Success() throws Exception {
        // Given
        doNothing().when(knowledgeBaseService).deleteNode("node-1");

        // When & Then
        mockMvc.perform(withAuth(delete("/api/knowledgebase/nodes/node-1")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("节点删除成功"));

        verify(knowledgeBaseService).deleteNode("node-1");
    }

    @Test
    void uploadDocument_Success() throws Exception {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "test.pdf", 
                "application/pdf", 
                "test content".getBytes()
        );
        
        when(knowledgeBaseService.uploadDocument(eq("node-1"), any(), eq(false)))
                .thenReturn(mockDocument);

        // When & Then
        mockMvc.perform(withAuth(multipart("/api/knowledgebase/nodes/node-1/upload")
                        .file(file)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("文件上传成功并已触发混合检索索引"))
                .andExpect(jsonPath("$.data.documentId").value("doc-1"));

        verify(knowledgeBaseService).uploadDocument(eq("node-1"), any(), eq(false));
    }

    @Test
    void uploadDocument_EmptyFile() throws Exception {
        // Given
        MockMultipartFile emptyFile = new MockMultipartFile(
                "file", 
                "empty.txt", 
                "text/plain", 
                new byte[0]
        );

        // When & Then
        mockMvc.perform(withAuth(multipart("/api/knowledgebase/nodes/1/upload")
                        .file(emptyFile)))
                .andExpect(status().isBadRequest());

        verifyNoInteractions(knowledgeBaseService);
    }
} 