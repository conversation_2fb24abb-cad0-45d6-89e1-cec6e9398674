package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.stp.StpUtil;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.dto.LoginRequest;
import com.zibbava.edgemind.cortex.dto.LoginResponse;
import com.zibbava.edgemind.cortex.dto.RegisterRequest;
import com.zibbava.edgemind.cortex.service.UserService;
import com.zibbava.edgemind.cortex.service.PermissionService;
import com.zibbava.edgemind.cortex.service.RoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/auth") // 给所有接口加上 /auth 前缀
public class AuthController {

    @Autowired
    private UserService userService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private RoleService roleService;

    /**
     * 注册接口
     * @param request 包含 username 和 password 的 DTO
     * @return 标准响应体
     */
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<Void>> register(@RequestBody RegisterRequest request) {
        // 可以在这里添加 Bean Validation (@Valid RegisterRequest request)
//        userService.register(request.getUsername(), request.getPassword()); //todo 注册逻辑
        return ResponseEntity.ok(ApiResponse.success("注册成功"));
    }

    /**
     * 登录接口
     * @param request 包含 username 和 password 的 DTO
     * @return 标准响应体，包含 Token 和用户信息
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponse<LoginResponse>> login(@RequestBody LoginRequest request) {
        // 可以在这里添加 Bean Validation (@Valid LoginRequest request)
        LoginResponse loginResponse = userService.login(request.getUsername(), request.getPassword());
        return ResponseEntity.ok(ApiResponse.success("登录成功", loginResponse));
    }

    /**
     * 登出接口
     * @return 标准响应体
     */
    @PostMapping("/logout")
    public ResponseEntity<ApiResponse<Void>> logout() {
         userService.logout(); // Service 层会处理未登录异常
         return ResponseEntity.ok(ApiResponse.success("登出成功"));
    }

     /**
     * 检查登录状态接口
     * @return 标准响应体，包含登录状态和用户信息
     */
    @GetMapping("/check-login")
    public ResponseEntity<ApiResponse<Map<String, Object>>> checkLogin() {
        boolean isLogin = StpUtil.isLogin();
        Map<String, Object> data = new java.util.HashMap<>();
        data.put("isLogin", isLogin);
        if (isLogin) {
            data.put("userId", StpUtil.getLoginIdAsLong());
            // 从 Session 获取用户名
            String username = StpUtil.getSession().getString("username"); 
            data.put("username", username != null ? username : "用户" + StpUtil.getLoginIdAsLong()); // 提供一个回退显示
        }
        String message = isLogin ? "已登录" : "未登录";
        return ResponseEntity.ok(ApiResponse.success(message, data));
    }

    /**
     * 获取当前用户权限信息
     */
    @GetMapping("/current-permissions")
    @SaCheckLogin
    public ResponseEntity<ApiResponse<Map<String, Object>>> getCurrentPermissions() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            // 获取用户权限
            List<String> permissions = permissionService.getUserPermissions(userId);

            // 获取用户角色
            List<String> roles = roleService.getUserRoles(userId);

            Map<String, Object> data = new HashMap<>();
            data.put("userId", userId);
            data.put("permissions", permissions);
            data.put("roles", roles);
            data.put("timestamp", System.currentTimeMillis());

            log.debug("用户 {} 权限查询成功，权限数量: {}, 角色数量: {}",
                    userId, permissions.size(), roles.size());

            return ResponseEntity.ok(ApiResponse.success("获取权限成功", data));
        } catch (Exception e) {
            log.error("获取用户权限失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "获取权限信息失败"));
        }
    }

    /**
     * 验证用户权限
     */
    @PostMapping("/validate-permission")
    @SaCheckLogin
    public ResponseEntity<ApiResponse<Boolean>> validatePermission(@RequestBody Map<String, Object> request) {
        try {
            Long userId = StpUtil.getLoginIdAsLong();
            String permission = (String) request.get("permission");
            String action = (String) request.get("action");

            // 记录权限验证请求
            log.info("权限验证请求 - 用户: {}, 权限: {}, 操作: {}", userId, permission, action);

            // 验证权限
            boolean hasPermission = permissionService.hasPermission(userId, permission);

            // 记录验证结果
            if (!hasPermission) {
                log.warn("权限验证失败 - 用户: {}, 权限: {}, 操作: {}", userId, permission, action);
            }

            return ResponseEntity.ok(ApiResponse.success("权限验证完成", hasPermission));
        } catch (Exception e) {
            log.error("权限验证异常", e);
            return ResponseEntity.ok(ApiResponse.error(500, "权限验证失败"));
        }
    }

    /**
     * 刷新用户权限缓存
     */
    @PostMapping("/refresh-permissions")
    @SaCheckLogin
    public ResponseEntity<ApiResponse<Void>> refreshPermissions() {
        try {
            Long userId = StpUtil.getLoginIdAsLong();

            // 清除权限缓存
            permissionService.clearUserPermissionCache(userId);

            log.info("用户 {} 权限缓存已刷新", userId);

            return ResponseEntity.ok(ApiResponse.success("权限缓存刷新成功"));
        } catch (Exception e) {
            log.error("刷新权限缓存失败", e);
            return ResponseEntity.ok(ApiResponse.error(500, "刷新权限缓存失败"));
        }
    }
}