<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EdgeMind 管理后台</title>
    
    <!-- Bootstrap CSS -->
    <link href="/aistudio/css/vendor/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/aistudio/css/vendor/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="/aistudio/js/vendor/chart.js"></script>
    <!-- 自定义样式 -->
    <link href="/aistudio/css/features/admin/dashboard.css" rel="stylesheet">
    <style>
        .nav-tabs .nav-link {
            color: #495057;
            border: none;
            border-bottom: 3px solid transparent;
            padding: 12px 20px;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            border-bottom-color: #0d6efd;
            background-color: transparent;
        }
        .nav-tabs .nav-link:hover {
            border-bottom-color: #0d6efd;
            color: #0d6efd;
        }
        .tab-content {
            padding-top: 20px;
        }
        .license-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .license-card {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .license-card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 20px;
        }
        .license-card-body {
            padding: 20px;
        }
        .result-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-brain me-2"></i>EdgeMind 管理后台
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <span th:if="${currentAdmin}" th:text="${currentAdmin.nickname}">管理员</span>
                        <span th:unless="${currentAdmin}">管理员</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/aistudio/admin/change-password">
                            <i class="fas fa-key me-2"></i>修改密码
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid py-4">
        <!-- 错误提示 -->
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span th:text="${error}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 导航标签 -->
        <ul class="nav nav-tabs" id="adminTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard" type="button" role="tab">
                    <i class="fas fa-tachometer-alt me-2"></i>数据概览
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="license-tab" data-bs-toggle="tab" data-bs-target="#license" type="button" role="tab">
                    <i class="fas fa-key me-2"></i>许可证生成
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="license-management-tab" data-bs-toggle="tab" data-bs-target="#license-management" type="button" role="tab">
                    <i class="fas fa-list-alt me-2"></i>许可证管理
                </button>
            </li>
        </ul>

        <!-- 标签内容 -->
        <div class="tab-content" id="adminTabsContent">
            <!-- 数据概览标签页 -->
            <div class="tab-pane fade show active" id="dashboard" role="tabpanel">
                <!-- 操作按钮 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>数据概览</h2>
                    <button onclick="refreshData()" class="btn btn-primary">
                        <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status"></span>
                        <i class="fas fa-sync-alt me-2"></i>刷新数据
                    </button>
                </div>

                <!-- 统计卡片 -->
                <div class="row g-4 mb-4">
                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card total h-100">
                            <div class="card-body text-center">
                                <div class="icon text-primary">
                                    <i class="fas fa-download"></i>
                                </div>
                                <div class="number text-primary" id="totalDownloads" th:text="${statistics?.totalDownloads ?: 0}">0</div>
                                <div class="text-muted">总下载次数</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card today h-100">
                            <div class="card-body text-center">
                                <div class="icon text-success">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="number text-success" id="todayDownloads" th:text="${statistics?.todayDownloads ?: 0}">0</div>
                                <div class="text-muted">今日下载</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card month h-100">
                            <div class="card-body text-center">
                                <div class="icon text-warning">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                                <div class="number text-warning" id="monthDownloads" th:text="${statistics?.thisMonthDownloads ?: 0}">0</div>
                                <div class="text-muted">本月下载</div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6">
                        <div class="card stat-card unique h-100">
                            <div class="card-body text-center">
                                <div class="icon text-danger">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="number text-danger" id="uniqueIps" th:text="${statistics?.uniqueIpCount ?: 0}">0</div>
                                <div class="text-muted">独立IP</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="row g-4">
                    <div class="col-lg-6">
                        <div class="card shadow-sm">
                            <div class="card-header bg-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-area me-2 text-primary"></i>最近7天下载趋势
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="sevenDaysChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card shadow-sm">
                            <div class="card-header bg-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-bar me-2 text-success"></i>最近30天下载趋势
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="thirtyDaysChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 许可证生成标签页 -->
            <div class="tab-pane fade" id="license" role="tabpanel">
                <div class="license-container">
                    <h2 class="mb-4"><i class="fas fa-key me-2"></i>许可证生成工具</h2>

                    <div class="license-card">
                        <div class="license-card-header">
                            <h5 class="mb-0">生成许可证</h5>
                        </div>
                        <div class="license-card-body">
                            <form id="licenseForm">
                                <div class="mb-3">
                                    <label for="secretKey" class="form-label">密钥</label>
                                    <input type="password" class="form-control" id="secretKey" name="secretKey" placeholder="输入管理密钥" required>
                                    <div class="form-text">请输入正确的管理密钥以生成许可证。</div>
                                </div>

                                <div class="mb-3">
                                    <label for="hardwareFingerprint" class="form-label">硬件指纹</label>
                                    <input type="text" class="form-control" id="hardwareFingerprint" placeholder="输入客户的硬件指纹" required>
                                    <div class="form-text">客户可以在授权页面获取硬件指纹</div>
                                </div>

                                <div class="mb-3">
                                    <label for="systemIdentifier" class="form-label">系统标识</label>
                                    <input type="text" class="form-control" id="systemIdentifier" placeholder="输入客户的系统标识" required>
                                    <div class="form-text">客户可以在授权页面获取系统标识</div>
                                </div>

                                <div class="mb-3">
                                    <label for="licenseType" class="form-label">授权类型</label>
                                    <select class="form-select" id="licenseType">
                                        <option value="trial">试用版</option>
                                        <option value="standard">标准版</option>
                                        <option value="professional">专业版</option>
                                        <option value="enterprise">企业版</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="expireType" class="form-label">过期类型</label>
                                    <select class="form-select" id="expireType" onchange="toggleExpireDate()">
                                        <option value="never">永不过期</option>
                                        <option value="date">指定日期</option>
                                    </select>
                                </div>

                                <div class="mb-3" id="expireDateGroup" style="display: none;">
                                    <label for="expireDate" class="form-label">过期日期</label>
                                    <input type="date" class="form-control" id="expireDate">
                                </div>

                                <button type="button" class="btn btn-primary" onclick="generateLicense()">生成许可证</button>
                            </form>

                            <div id="resultContainer" style="display: none;">
                                <div class="result-box" id="licenseResult"></div>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-outline-primary" onclick="copyLicense()">
                                        <i class="bi bi-clipboard"></i> 复制许可证
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="license-card">
                        <div class="license-card-header">
                            <h5 class="mb-0">使用说明</h5>
                        </div>
                        <div class="license-card-body">
                            <ol>
                                <li>输入客户提供的硬件指纹和系统标识</li>
                                <li>选择授权类型和过期时间</li>
                                <li>点击"生成许可证"按钮</li>
                                <li>复制生成的许可证，发送给客户</li>
                                <li>客户在授权页面输入许可证并激活</li>
                            </ol>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                注意：生成的许可证只能在指定的硬件和系统上使用，请确保输入信息准确。
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 许可证管理标签页 -->
            <div class="tab-pane fade" id="license-management" role="tabpanel">
                <div id="license-management-content">
                    <!-- 这里将通过 iframe 加载许可证管理页面 -->
                    <iframe id="license-management-frame" 
                            src="/aistudio/admin/license-management" 
                            style="width: 100%; height: calc(100vh - 200px); border: none; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    </iframe>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aistudio/js/vendor/bootstrap.bundle.min.js"></script>
    <!-- API 工具类 -->
    <script src="/aistudio/js/common/api-utils.js"></script>
    
    <script>
        // 传递服务器端数据到前端
        window.statisticsData = /*[[${statistics}]]*/ null;
        
        // 记录已加载的脚本
        const loadedScripts = new Set();
        
        // 动态加载脚本函数
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                if (loadedScripts.has(src)) {
                    resolve();
                    return;
                }
                
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    loadedScripts.add(src);
                    resolve();
                };
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        // 标签页切换事件处理
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('#adminTabs button[data-bs-toggle="tab"]');
            
            tabButtons.forEach(button => {
                button.addEventListener('shown.bs.tab', function(event) {
                    const targetId = event.target.getAttribute('data-bs-target');
                    
                    switch(targetId) {
                        case '#dashboard':
                            loadScript('/aistudio/js/features/admin/dashboard.js')
                                .then(() => {
                                    // 仪表板脚本加载完成后的初始化
                                    if (typeof initializeDashboard === 'function') {
                                        initializeDashboard();
                                    }
                                })
                                .catch(err => console.error('加载仪表板脚本失败:', err));
                            break;
                            
                        case '#license':
                            loadScript('/aistudio/js/features/admin/license-generator.js')
                                .then(() => {
                                    // 许可证生成脚本加载完成后的初始化
                                    initializeLicenseGenerator();
                                })
                                .catch(err => console.error('加载许可证生成脚本失败:', err));
                            break;
                            
                        case '#license-management':
                            // 许可证管理页面通过iframe加载，无需额外处理
                            break;
                    }
                });
            });
            
            // 页面加载时初始化默认激活的标签页（仪表板）
            loadScript('/aistudio/js/features/admin/dashboard.js')
                .then(() => {
                    if (typeof initializeDashboard === 'function') {
                        initializeDashboard();
                    }
                })
                .catch(err => console.error('加载仪表板脚本失败:', err));
        });
        
        // 许可证生成页面初始化函数
        function initializeLicenseGenerator() {
            // 设置默认过期日期为一年后
            const today = new Date();
            const oneYearLater = new Date(today.getFullYear() + 1, today.getMonth(), today.getDate());
            
            const expireDateInput = document.getElementById('expireDate');
            if (expireDateInput) {
                expireDateInput.valueAsDate = oneYearLater;
            }
        }

        // 许可证生成相关函数
        function toggleExpireDate() {
            const expireType = document.getElementById('expireType').value;
            const expireDateGroup = document.getElementById('expireDateGroup');
            if (expireType === 'date') {
                expireDateGroup.style.display = 'block';
            } else {
                expireDateGroup.style.display = 'none';
            }
        }

        function generateLicense() {
            // 调用原有的许可证生成逻辑
            const secretKey = document.getElementById('secretKey').value;
            const hardwareFingerprint = document.getElementById('hardwareFingerprint').value.trim();
            const systemIdentifier = document.getElementById('systemIdentifier').value.trim();
            const licenseType = document.getElementById('licenseType').value;
            const expireType = document.getElementById('expireType').value;
            
            if (!secretKey) {
                alert('请输入密钥');
                return;
            }
            if (!hardwareFingerprint || !systemIdentifier) {
                alert('请输入硬件指纹和系统标识');
                return;
            }
            
            // 构建请求数据
            const requestData = {
                secretKey: secretKey,
                hardwareFingerprint: hardwareFingerprint,
                systemIdentifier: systemIdentifier,
                licenseType: licenseType
            };
            
            // 如果选择了指定日期过期
            if (expireType === 'date') {
                const expireDate = document.getElementById('expireDate').value;
                if (!expireDate) {
                    alert('请选择过期日期');
                    return;
                }
                
                // 设置过期时间为当天23:59:59
                const expireDateTime = new Date(expireDate);
                expireDateTime.setHours(23, 59, 59, 999);
                
                requestData.expireTime = expireDateTime.toISOString();
            }
            
            // 发送请求
            ApiUtils.post('/api/license/generate', requestData)
                .then(data => {
                    if (data.code === 200 && data.data) {
                        // 显示结果
                        document.getElementById('licenseResult').textContent = data.data;
                        document.getElementById('resultContainer').style.display = 'block';
                        ApiUtils.showSuccess('许可证生成成功');
                    } else {
                        ApiUtils.showError('生成许可证失败: ' + (data.message || '未知错误'));
                    }
                })
                .catch(error => {
                    console.error('生成许可证失败:', error);
                    ApiUtils.showError('生成许可证失败，请稍后再试');
                });
        }

        function copyLicense() {
            const licenseText = document.getElementById('licenseResult').textContent;
            
            // 创建临时文本区域
            const textarea = document.createElement('textarea');
            textarea.value = licenseText;
            textarea.style.position = 'fixed';
            document.body.appendChild(textarea);
            textarea.select();
            
            try {
                // 执行复制命令
                document.execCommand('copy');
                alert('许可证已复制到剪贴板');
            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            }
            
            // 删除临时文本区域
            document.body.removeChild(textarea);
        }

        // 退出登录函数
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                window.location.href = '/aistudio/admin/logout';
            }
        }

        // 刷新数据函数
        function refreshData() {
            location.reload();
        }
    </script>
</body>
</html>