package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.User;
import org.apache.ibatis.annotations.Mapper;

@Mapper // 显式添加 @Mapper 注解
public interface UserMapper extends BaseMapper<User> {

    // BaseMapper 已经提供了根据 ID 查询、插入、更新、删除等常用方法
    // 对于根据 username 查询，我们将在 Service 层使用 QueryWrapper
    // 如果需要更复杂的 SQL 或者性能优化，可以在这里定义方法并使用 @Select 注解或 XML 实现

} 