package com.zibbava.edgemind.cortex.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.onlyoffice.manager.security.JwtManager;
import com.onlyoffice.manager.settings.SettingsManager;
import com.onlyoffice.model.documenteditor.Callback;
import com.onlyoffice.service.documenteditor.callback.DefaultCallbackService;
import com.zibbava.edgemind.cortex.service.KnowledgeBaseService;
import com.zibbava.edgemind.cortex.util.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.net.URL;

/**
 * ONLYOFFICE回调处理服务实现
 */
@Component
@Slf4j
public class CallbackServiceImpl extends DefaultCallbackService {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final KnowledgeBaseService knowledgeBaseService;


    // 注入FileUtils工具类
    private final FileUtils fileUtils;

    public CallbackServiceImpl(
            final JwtManager jwtManager,
            final SettingsManager settingsManager,
            final KnowledgeBaseService knowledgeBaseService,
            final FileUtils fileUtils) {
        super(jwtManager, settingsManager);
        this.knowledgeBaseService = knowledgeBaseService;
        this.fileUtils = fileUtils;
    }

    /**
     * 处理文档编辑中的回调
     */
    @Override
    public void handlerEditing(final Callback callback, final String nodeId) throws Exception {
        log.info("文档 {} 正在编辑中...", nodeId);
        log.debug("回调数据: {}", objectMapper.writeValueAsString(callback));
    }

    /**
     * 处理文档保存回调
     */
    @Override
    public void handlerSave(final Callback callback, final String nodeId) throws Exception {
        log.info("文档 {} 保存中...", nodeId);
        log.debug("回调数据: {}", objectMapper.writeValueAsString(callback));

//        if (callback.getUrl() == null) {
//            log.error("回调中缺少URL字段");
//            return;
//        }
//
//        String url = callback.getUrl();
//
//        // 1. 检查文件节点是否存在
//        KnowledgeNode node = knowledgeBaseService.findNodeById(nodeId);
//        if (node == null) {
//            log.error("找不到节点: {}", nodeId);
//            return;
//        }
//
//        // 2. 检查文档记录是否存在
//        KnowledgeDocument document = knowledgeBaseService.findDocumentByNodeId(nodeId);
//        if (document == null) {
//            log.error("找不到节点 {} 关联的文档记录", nodeId);
//            return;
//        }
//
//        // 3. 下载新版本的文档
//        byte[] fileContent;
//        try {
//            fileContent = downloadFileFromUrl(url);
//            if (fileContent == null || fileContent.length == 0) {
//                log.error("从 URL 下载的文件内容为空: {}", url);
//                return;
//            }
//        } catch (Exception e) {
//            log.error("从URL下载文件失败: {}", e.getMessage(), e);
//            return;
//        }
//
//        // 4. 保存新版本文件，替换原有文件
//        try {
//            // 使用FileUtils工具类定位文件
//            FileUtils.StoragePath storagePath = fileUtils.getStoragePathForNode(nodeId);
//            Path nodePath = fileUtils.buildNodePath(storagePath.getFullPath(), nodeId);
//            Path parentPath = nodePath.getParent();
//
//            // 找到实际的文件
//            Path actualFilePath = null;
//            if (Files.exists(parentPath)) {
//                final String nodeIdStr = nodeId;
//                try {
//                    actualFilePath = Files.list(parentPath)
//                            .filter(path -> path.getFileName().toString().startsWith(nodeIdStr) && Files.isRegularFile(path))
//                            .findFirst()
//                            .orElse(null);
//                } catch (IOException e) {
//                    log.error("查找文件时出错: {}", e.getMessage(), e);
//                }
//            }
//
//            if (actualFilePath == null) {
//                // 如果文件不存在，创建新文件
//                String extension = MediaUtils.getMimeTypeExtension(document.getMimeType());
//                actualFilePath = parentPath.resolve(nodeId + extension);
//
//                // 确保父目录存在
//                if (!Files.exists(parentPath)) {
//                    Files.createDirectories(parentPath);
//                }
//            }
//
//            // 保存新内容到文件
//            Files.write(actualFilePath, fileContent, StandardOpenOption.CREATE,
//                    StandardOpenOption.TRUNCATE_EXISTING);
//
//            log.info("成功保存编辑后的文档到 {}", actualFilePath);
//
//            // 5. 更新文档记录 - 计算新的内容哈希
//            String newHash = DigestUtils.sha256Hex(fileContent);
//            document.setContentHash(newHash);
//            document.setUpdateTime(LocalDateTime.now());
//
//            // 6. 更新数据库
//            try {
//                knowledgeBaseService.updateDocument(document);
//                log.info("成功更新文档记录元数据: {}", document.getDocumentId());
//            } catch (Exception e) {
//                log.error("更新文档记录元数据失败: {}", e.getMessage(), e);
//                // 即使元数据更新失败，文件已保存成功
//            }
//        } catch (Exception e) {
//            log.error("保存编辑后的文档失败: {}", e.getMessage(), e);
//        }
    }

    /**
     * 从URL下载文件内容
     */
    private byte[] downloadFileFromUrl(String url) throws Exception {
        try (InputStream in = new URL(url).openStream()) {
            return in.readAllBytes();
        }
    }

}