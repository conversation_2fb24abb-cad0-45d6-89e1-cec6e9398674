// conversation-manager.js

import { ModalComponent, showToast } from '../../shared/components/modal-component.js';

// --- 全局变量或状态共享 ---
// 这些变量/函数可能需要被其他模块访问或修改
let currentConversationId = null; // 当前会话ID
let isLoadingConversationList = false; // 是否正在加载更多会话
let currentConversationPage = 0; // 当前已加载的页码
let hasMoreConversations = true; // 是否还有更多会话
let isGuestMode = false; // Flag for guest mode

// --- DOM 元素引用 (在主脚本初始化后设置) ---
let conversationsList = null;
let iframe = null; // Add reference to the iframe
// let chatMessages = null; // 聊天容器由 message-handler 管理，但这里可能需要清空

// --- 全局变量，用于持有当前显示的 modal 实例，防止重复创建
let currentModal = null;
let currentModalElement = null;

// --- PostMessage Handler ---
function handlePostMessage(event) {
    // 重要: 在生产环境中添加 origin 校验!
    // if (event.origin !== 'YOUR_EXPECTED_IFRAME_ORIGIN') return;

    const data = event.data;
    if (!data || !data.type) return;

    switch (data.type) {
        case 'GET_CONVERSATION_STATE':
            if (iframe && iframe.contentWindow) {
        
                iframe.contentWindow.postMessage({
                    type: 'CONVERSATION_STATE',
                    payload: { isGuest: isGuestMode, currentConversationId: currentConversationId }
                }, '*'); // <-- TODO: Replace * with specific target origin
            } else {
                 console.error("[ConvManager] Cannot find iframe to send state back.");
            }
            break;
        case 'UPDATE_TITLE':
            if (data.payload && data.payload.conversationId && data.payload.text) {
        
                // Directly call the internal function
                checkAndUpdateConversationTitleIfNeededInternal(data.payload.conversationId, data.payload.text);
            }
            break;
        case 'SHOW_TOAST':
             if (data.payload && data.payload.message) {
         
                 // Call the imported showToast function
                 showToast(data.payload.message, data.payload.type || 'info');
             }
             break;
        // Add more cases as needed
    }
}

// --- 初始化函数 (由父页面 layout.html 调用) ---
async function initialize() {

    conversationsList = document.getElementById('conversations-list');
    iframe = document.getElementById('contentFrame'); // Get iframe reference

    if (!conversationsList) {
        console.error("[ConvManager] Error: Cannot find #conversations-list.");
        return;
    }

    setupConversationListScrollListener();
    await initializeConversationState(); // Renamed from initializeConversation

    // Add the postMessage listener here
    window.addEventListener('message', handlePostMessage);

}

// --- 会话列表管理 ---

// 格式化日期
function formatDate(dateStr) {
    try {
        const date = new Date(dateStr);
        return date.toLocaleString('zh-CN', {
            month: 'numeric',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        console.warn("日期格式化失败:", dateStr, e);
        return "未知时间";
    }
}

// 获取当前会话的标题
function getCurrentConversationTitle(conversations, currentId) {
    if (!conversations || !currentId) return "当前对话";

    try {
        const currentConv = conversations.find(conv => conv.id == currentId);
        if (!currentConv) return "当前对话";

        return currentConv.title && currentConv.title.trim() !== '' ?
            currentConv.title :
            `对话 ${formatDate(currentConv.createTime)}`;
    } catch (error) {
        console.error("获取会话标题失败:", error);
        return "当前对话";
    }
}

// 重置会话列表分页状态
function resetConversationListPagination() {
    currentConversationPage = 0;
    hasMoreConversations = true;
    if (conversationsList) {
        conversationsList.innerHTML = ''; // 清空列表
        delete conversationsList.dataset.noMoreConversations;
    }
}

// 加载会话列表 (供滚动加载和初始加载使用)
async function loadConversationList(page = 0, size = 30) {
    if (isLoadingConversationList || !hasMoreConversations || !conversationsList) return;

    isLoadingConversationList = true;

    let loadingIndicator = null;
    if (page > 0) {
        loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'conversation-loading-indicator text-center text-muted small py-2';
        loadingIndicator.innerHTML = '<span>加载更多会话...</span>';
        conversationsList.appendChild(loadingIndicator);
    }

    try {
        const response = await fetch(`/wkg/api/conversations?page=${page}&size=${size}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        const conversations = data.content || [];
        const totalPages = data.totalPages || 1;

        hasMoreConversations = page < totalPages - 1;
        if (!hasMoreConversations && conversationsList) {
            conversationsList.dataset.noMoreConversations = "true";
        }

        if (loadingIndicator) loadingIndicator.remove();

        // 确保当前会话项存在并高亮
        if (page === 0 && currentConversationId) {
            await ensureCurrentConversationItemExists(data.conversations, currentConversationId);
        }

        // 添加历史会话
        appendHistoryConversations(conversations);

        currentConversationPage = page;
        // console.log(`已加载会话列表第${page}页, 数量: ${conversations.length}, 是否有更多: ${hasMoreConversations}`);

    } catch (error) {
        console.error(`加载会话列表第${page}页失败:`, error);
        if (loadingIndicator) loadingIndicator.remove();
        if (page > 0) {
            showConversationLoadingError(page, size);
        }
    } finally {
        isLoadingConversationList = false;
    }
}

// 确保"当前对话"项存在于列表中
async function ensureCurrentConversationItemExists(fetchedConversations, currentId) {
    if (!conversationsList || !currentId) return;

    const existingItem = conversationsList.querySelector(`.conversation-item[data-conversation-id="${currentId}"]`);
    if (existingItem) {
        activateConversationItem(currentId); // 确保激活
        return;
    }

    // 如果列表中没有，尝试从获取的数据中查找
    let currentConvData = fetchedConversations ? fetchedConversations.find(c => c.id == currentId) : null;

    if (!currentConvData) {
        // 如果在当前批次中找不到，尝试单独获取
        try {

            const response = await fetch(`/wkg/api/conversations/${currentId}`);
            if (response.ok) {
                currentConvData = await response.json();
            } else {
                console.error(`单独获取会话 ${currentId} 失败: ${response.status}`);
            }
        } catch (fetchError) {
            console.error(`单独获取会话 ${currentId} 时出错:`, fetchError);
        }
    }

    const title = currentConvData ? getCurrentConversationTitle([currentConvData], currentId) : "当前对话";
    addConversationItem(currentId, title, true, true); // 添加到列表顶部并激活
}

// 将历史会话添加到列表
function appendHistoryConversations(conversations) {
    if (!conversationsList || !conversations || conversations.length === 0) return;

    const fragment = document.createDocumentFragment();
    conversations.forEach(conv => {
        if (!conv || !conv.id) return;
        // 跳过已存在或当前的会话
        if (conversationsList.querySelector(`.conversation-item[data-conversation-id="${conv.id}"]`) || conv.id == currentConversationId) {
            return;
        }
        const title = conv.title && conv.title.trim() !== '' ? conv.title : `对话 ${formatDate(conv.createTime)}`;
        const item = createConversationItemElement(conv.id, title, false);
        fragment.appendChild(item);
    });

    conversationsList.appendChild(fragment);

    // 检查是否需要显示空列表提示
    checkAndShowEmptyIndicator();
}

// 创建单个会话列表项的DOM元素
function createConversationItemElement(id, title, isActive, isGuest = false) {
    const item = document.createElement('div');
    // 使用新的样式类，并移除 d-flex 等，因为父级已经是 flex
    item.classList.add('conversation-item', 'position-relative'); // 添加 position-relative 以便绝对定位 dropdown
    item.dataset.conversationId = id;
    if (isActive) {
        item.classList.add('active');
    }
    const iconClass = 'bi-chat-dots';

    // 主内容区域 (图标 + 标题 + 下拉菜单触发器)
    // 使用 a 标签以便更好地表示链接
    const mainLink = document.createElement('a');
    mainLink.href = "#"; // 阻止默认跳转
    mainLink.classList.add('d-flex', 'align-items-center', 'text-decoration-none', 'w-100', 'pe-3'); // 宽度100% 并留出右侧空间给按钮
    mainLink.style.color = 'inherit'; // 继承父级颜色
    mainLink.innerHTML = `
        <i class="bi ${iconClass} me-2 flex-shrink-0"></i>
        <span class="conversation-title text-truncate flex-grow-1">${escapeHtml(title)}</span>
    `;

    // 添加点击事件 (仅非游客)
    if (!isGuest) {
        mainLink.style.cursor = 'pointer';
        mainLink.addEventListener('click', (event) => {
            event.preventDefault(); // 阻止 a 标签跳转
            // 只有在点击非下拉菜单按钮时才加载
            if (!event.target.closest('.conversation-options-btn')) {
         
                 loadConversation(id);
            }
        });
    } else {
        mainLink.style.cursor = 'default';
        item.classList.add('guest-item');
    }

    item.appendChild(mainLink);

    // 添加操作下拉菜单 (仅非游客)
    if (!isGuest) {
        const optionsContainer = document.createElement('div');
        // 移到绝对定位
        optionsContainer.classList.add('dropdown', 'conversation-options-dropdown', 'position-absolute', 'top-50', 'end-0', 'translate-middle-y', 'me-1');
        const dropdownId = `conv-options-${id}`;
        optionsContainer.innerHTML = `
            <button class="btn btn-sm btn-icon conversation-options-btn p-0 border-0" type="button" id="${dropdownId}" data-bs-toggle="dropdown" aria-expanded="false" title="更多选项">
                <i class="bi bi-three-dots-vertical"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="${dropdownId}">
                <li><a class="dropdown-item rename-conv-item d-flex align-items-center" href="#"><i class="bi bi-pencil me-2"></i>重命名</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item delete-conv-item text-danger d-flex align-items-center" href="#"><i class="bi bi-trash me-2"></i>删除</a></li>
            </ul>
        `;
        const dropdownButton = optionsContainer.querySelector('.conversation-options-btn');
        dropdownButton?.addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡到 mainLink
        });

        const renameItem = optionsContainer.querySelector('.rename-conv-item');
        renameItem?.addEventListener('click', (e) => {
            e.preventDefault(); e.stopPropagation();
            // 调用chat-history.js中的方法
            if (window.chatHistory && typeof window.chatHistory.showRenameModal === 'function') {
                window.chatHistory.showRenameModal(id, title);
            } else {
                console.error("找不到chat-history.js中的showRenameModal方法");
                showToast("重命名功能暂不可用，请刷新页面重试", "error");
            }
            const ddInstance = bootstrap.Dropdown.getInstance(dropdownButton);
            ddInstance?.hide();
        });

        const deleteItem = optionsContainer.querySelector('.delete-conv-item');
        deleteItem?.addEventListener('click', (e) => {
            e.preventDefault(); e.stopPropagation();
            // 获取当前最新的会话标题
            const currentTitle = item.querySelector('.conversation-title').textContent;
            // 调用chat-history.js中的方法
            if (window.chatHistory && typeof window.chatHistory.showDeleteModal === 'function') {
                window.chatHistory.showDeleteModal(id, currentTitle);
            } else {
                console.error("找不到chat-history.js中的showDeleteModal方法");
                showToast("删除功能暂不可用，请刷新页面重试", "error");
            }
            const ddInstance = bootstrap.Dropdown.getInstance(dropdownButton);
            ddInstance?.hide();
        });
        item.appendChild(optionsContainer);
    }

    return item;
}

// 将会话项添加到列表
function addConversationItem(id, title, isActive, prepend = false, isGuest = false) {
    if (!conversationsList || conversationsList.querySelector(`.conversation-item[data-conversation-id="${id}"]`)) {
        return; // 避免重复添加
    }
    const item = createConversationItemElement(id, title, isActive, isGuest);
    if (prepend) {
        conversationsList.insertBefore(item, conversationsList.firstChild);
    } else {
        conversationsList.appendChild(item);
    }
    // 移除可能存在的空列表提示
    removeEmptyIndicator();
}

// 激活指定的会话项
function activateConversationItem(conversationId) {
    if (!conversationsList) return;
    conversationsList.querySelectorAll('.conversation-item').forEach(item => {
        item.classList.toggle('active', item.dataset.conversationId == conversationId);
    });
}

// 显示会话列表加载错误
function showConversationLoadingError(page, size) {
    if (!conversationsList) return;
    // 避免重复添加错误提示
    if (conversationsList.querySelector('.conversation-loading-error-indicator')) return;

    const errorIndicator = document.createElement('div');
    errorIndicator.className = 'conversation-loading-error-indicator text-center text-danger small py-2';
    errorIndicator.innerHTML = '<span>加载失败，点击重试</span>';
    errorIndicator.style.cursor = 'pointer';
    errorIndicator.addEventListener('click', () => {
        errorIndicator.remove();
        loadConversationList(page, size);
    }, { once: true }); // 点击后自动移除监听器
    conversationsList.appendChild(errorIndicator);
}

// 检查并显示空列表提示
function checkAndShowEmptyIndicator() {
    if (!conversationsList) return;
    // 如果列表为空（没有 .conversation-item 元素），则显示提示
    if (conversationsList.children.length === 0 && !conversationsList.querySelector('.conversation-empty-indicator')) {
        const emptyIndicator = document.createElement('div');
        emptyIndicator.className = 'conversation-empty-indicator text-center text-muted small py-3';
        emptyIndicator.innerHTML = '<span>暂无历史会话</span>';
        conversationsList.appendChild(emptyIndicator);
    }
}

// 移除空列表提示
function removeEmptyIndicator() {
    conversationsList?.querySelector('.conversation-empty-indicator')?.remove();
}

// 设置会话列表滚动监听器
function setupConversationListScrollListener() {
    if (!conversationsList) return;
    // 确保只添加一次监听器
    if (conversationsList._scrollListenerAttached) return;

    conversationsList._scrollListener = function() {
        const nearBottom = conversationsList.scrollHeight - conversationsList.scrollTop - conversationsList.clientHeight < 100; // 增加触发距离
        if (nearBottom && !isLoadingConversationList && hasMoreConversations && !conversationsList.querySelector('.conversation-loading-indicator')) {
            // console.log("滚动到底部，加载更多历史会话");
            loadConversationList(currentConversationPage + 1);
        }
    };
    conversationsList.addEventListener('scroll', conversationsList._scrollListener);
    conversationsList._scrollListenerAttached = true;
    // console.log("已设置会话列表滚动监听器");
}

// --- 创建与切换会话 ---

// 创建新会话
async function createNewConversation() {
    if (isGuestMode) {
        showToast("请登录后创建新对话", "warning");
        return;
    }
    try {
        const now = new Date();
        const defaultTitle = `新对话 ${formatDate(now)}`;
        const title = defaultTitle.length > 10 ? defaultTitle.substring(0, 10) : defaultTitle;

        const response = await fetch('/wkg/api/conversations', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ title: title })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const newConversation = await response.json();
        // console.log("创建新会话成功:", newConversation.id, "标题:", newConversation.title);

        // 添加到列表顶部并激活
        addConversationItem(newConversation.id, newConversation.title || title, true, true);

        // 加载新会话内容
        await loadConversation(newConversation.id, true);

    } catch (error) {
        console.error("创建新会话失败:", error);
        showToast(`创建新会话失败: ${error.message}`, 'error');
    }
}

// 切换到指定会话
async function loadConversation(conversationId, isNew = false) {
    if (isGuestMode) {
        console.warn("尝试在游客模式下加载会话，已忽略。");
        return;
    }
    if (!conversationId) {
        console.error("[ConvManager] 无法加载会话：会话ID为空");
        return;
    }
    if (currentConversationId === conversationId && !isNew) {

        return; // 避免重复加载同一个活动会话
    }

    // 检查是否正在发送消息或加载消息
    if (window.messageHandler?.isProcessingOrLoading(conversationId)) {
 
        return;
    }

    const previousConversationId = currentConversationId;
    currentConversationId = conversationId;


    // 更新侧边栏 UI
    activateConversationItem(conversationId);

    // 更新 iframe 的 src 以加载新的会话内容
    const iframe = document.getElementById('contentFrame');
    if (iframe) {
        const newSrc = `/wkg/chat/content?conversationId=${conversationId}`;
        if (iframe.src !== newSrc) {
            iframe.src = newSrc;
    
        } else {
    
            // 如果 src 相同，但内容需要重新加载（例如，从错误状态恢复），
            // 可能需要强制重新加载，但这通常由 iframe 内部逻辑处理更佳
            // iframe.contentWindow.location.reload(); // 一般不推荐父页面强制重载
        }
    } else {
        console.error("[ConvManager] Cannot find iframe with id 'contentFrame' to update src.");
    }
}

// 检查并根据第一条消息自动更新标题
async function checkAndUpdateConversationTitleIfNeededInternal(conversationId, firstMessageText) {
    if (isGuestMode || !conversationId || !firstMessageText || !conversationsList) return;

    try {
        const currentItem = conversationsList.querySelector(`.conversation-item[data-conversation-id="${conversationId}"]`);
        const currentTitleElement = currentItem?.querySelector('.conversation-title');
        if (!currentTitleElement) return;

        const currentTitle = currentTitleElement.textContent;
        const isDefaultTitle = currentTitle.startsWith('新对话 ') || /^对话 \d{1,2}\/\d{1,2} \d{2}:\d{2}$/.test(currentTitle);

        if (isDefaultTitle) {
            let newTitle = firstMessageText.substring(0, 15).trim();
            if (firstMessageText.length > 15) newTitle += '...';
            if (newTitle) {
                // 调用chat-history.js中的方法
                if (window.chatHistory && typeof window.chatHistory.updateConversationTitle === 'function') {
                    await window.chatHistory.updateConversationTitle(conversationId, newTitle);
                } else {
                    console.error("找不到chat-history.js中的updateConversationTitle方法");
                }
            }
        }
    } catch (error) {
        console.error("检查并更新会话标题时出错:", error);
    }
}

// --- 以下功能已移至chat-history.js ---
// 注意：重命名和删除功能完全由chat-history.js实现
// 本文件只负责调用window.chatHistory中的方法

// HTML 转义
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') return '';
    return unsafe
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}

// --- 初始化流程 ---
async function initializeConversationState() {

    if (!conversationsList) {
        console.error("无法初始化：会话列表容器未设置");
        return;
    }

    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'conversation-loading-indicator text-center text-muted small py-3';
    loadingDiv.textContent = '正在加载会话列表...';
    conversationsList.innerHTML = '';
    conversationsList.appendChild(loadingDiv);

    try {
        const response = await fetch('/wkg/api/conversations/init');
        if (!response.ok) {
            if (response.status === 401 || response.status === 403) {
         
                 setupGuestMode();
                 return;
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        // console.log("Init data received:", data);
        conversationsList.innerHTML = ''; // 清除加载提示

        if (data.currentConversationId) {
            // 已登录用户
            isGuestMode = false;
            currentConversationId = data.currentConversationId;
            resetConversationListPagination();
            await ensureCurrentConversationItemExists(data.conversations, currentConversationId);
            appendHistoryConversations(data.conversations || []);

            // 设置 iframe 初始 src (在确认用户已登录且获取到 current ID 后)
            const iframe = document.getElementById('contentFrame');
            if (iframe) {
                const initialSrc = `/wkg/chat/content?conversationId=${currentConversationId}`;
                if (iframe.src.endsWith('/chat/content') || iframe.src === 'about:blank') { // 避免覆盖已由用户手动切换的src
                    iframe.src = initialSrc;
            
                }
            } else {
                console.error("[ConvManager Init] Cannot find iframe 'contentFrame' to set initial src.");
            }

        } else {
            // 游客模式
            setupGuestMode();
        }
    

    } catch (error) {
        console.error("初始化会话失败:", error);
        setupGuestMode(); // 初始化失败也进入游客模式
        showToast("加载会话列表失败", "error");
    }
}

// 设置游客模式的 UI
function setupGuestMode() {
    isGuestMode = true;
    currentConversationId = null;


    if(conversationsList) {
        conversationsList.innerHTML = ''; // 清空
        const guestItem = createConversationItemElement('guest', '临时对话', true, true);
        conversationsList.appendChild(guestItem);
    }
    // 显示欢迎信息
    // window.messageHandler?.showWelcomeMessage();

    // 禁用滚动加载
    if (conversationsList && conversationsList._scrollListener) {
         conversationsList.removeEventListener('scroll', conversationsList._scrollListener);
         conversationsList._scrollListenerAttached = false;
         // console.log("已移除游客模式会话列表滚动监听器");
    }
}

// 导出初始化函数给 layout.html
export { initialize };