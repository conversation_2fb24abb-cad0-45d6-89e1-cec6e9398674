package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 许可证实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_license")
public class License {

    /**
     * 许可证ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 许可证密钥
     */
    @TableField("license_key")
    private String licenseKey;

    /**
     * 硬件指纹
     */
    @TableField("hardware_fingerprint")
    private String hardwareFingerprint;

    /**
     * 激活状态：0-未激活，1-已激活
     */
    @TableField("status")
    private Integer status;

    /**
     * 激活时间
     */
    @TableField("activated_time")
    private LocalDateTime activatedTime;

    /**
     * 过期时间，null表示永不过期
     */
    @TableField("expire_time")
    private LocalDateTime expireTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
