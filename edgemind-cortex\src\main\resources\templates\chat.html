<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 聊天</title>
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css" />
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css" />
    <link rel="stylesheet" href="/wkg/css/vendor/github.min.css" />
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-select.min.css" />
    <link rel="stylesheet" href="/wkg/css/components/model-selector-custom.css" />
    <link rel="stylesheet" href="/wkg/css/components/chat-area.css" />
    <link rel="stylesheet" href="/wkg/css/components/chat-input-area.css" />
    <link rel="stylesheet" href="/wkg/css/components/chat-history.css" />
    <link rel="stylesheet" href="/wkg/css/components/markdown-styles.css" />
    <link rel="stylesheet" href="/wkg/css/components/code-block-styles.css" />
    <link rel="stylesheet" href="/wkg/css/components/loading-animation.css" />
    <link rel="stylesheet" href="/wkg/css/components/scrollbar-styles.css" />
    <link rel="stylesheet" href="/wkg/css/components/chat-main.css" />
    <link rel="stylesheet" href="/wkg/css/components/deep-thinking-toggle.css" />
    <link rel="stylesheet" href="/wkg/css/components/modal-unified.css" />
</head>
<body>
<div class="chat-main-layout">
    <!-- 左侧对话列表 -->
    <aside class="chat-history-panel">
        <!-- 发起新对话按钮 - 移到列表外固定位置 -->
        <div class="new-chat-button" id="new-chat-button">
            <i class="bi bi-plus-circle"></i>
            <span>发起新对话</span>
        </div>
        <!-- 会话列表 -->
        <div class="conversations-list" id="conversations-list">
            <!-- 会话列表将动态加载 -->
        </div>
    </aside>
    <!-- 右侧聊天主内容区 -->
    <div class="chat-main-page">
        <!-- 聊天消息区 -->
        <div class="chat-container flex-grow-1 d-flex flex-column" style="min-height:0;">
            <div class="chat-messages" id="chat-messages">
                <!-- 聊天消息将显示在这里 -->
                <div class="message welcome-message">
                    <p>你好！我是AI助手，有什么可以帮你的吗？</p>
                </div>
            </div>
        </div>
        <!-- 聊天输入区 -->
        <footer class="chat-input-area">
            <div class="chat-input-content">
                <!-- 新增：整体输入框容器 -->
                <div class="chat-input-box"> 
                    <!-- 文本输入区域 -->
                    <textarea id="user-input" class="form-control chat-textarea" placeholder="输入消息..." rows="1" style="resize: none;"></textarea>
                    <!-- 图片预览区 - 移到输入框下方，但在边框内部 -->
                    <div id="image-preview-container" class="mt-2 mb-2"></div>
                    <!-- 底部工具栏：模型选择 + 按钮 -->
                    <div class="input-toolbar d-flex justify-content-between align-items-center mt-2">
                        <!-- 左侧：模型选择器和深度思考按钮 -->
                        <div class="model-selector-container d-flex align-items-center flex-grow-0 me-3"> 
                             <div class="model-selector me-2">
                                 <label for="model-select" class="visually-hidden">选择模型</label>
                                 <select id="model-select" class="model-select-component selectpicker" data-style=""> 
                                     <!-- 内容由 JS 填充 -->
                                 </select>
                             </div>
                             <!-- 深度思考切换按钮 -->
                            <button class="deep-thinking-toggle active" 
                                    id="general-deep-thinking"
                                    data-deep-thinking-id="general-deep-thinking"
                                    data-bs-toggle="tooltip" 
                                    data-bs-placement="top" 
                                    title="深度思考"
                                    aria-label="切换深度思考">
                                <i class="bi bi-lightbulb"></i>
                            </button>
                         </div>
                        <!-- 右侧：按钮组 -->
                        <div class="button-group d-flex align-items-center">
                            <button id="upload-button" class="btn btn-icon" title="上传图片" data-bs-toggle="tooltip" data-bs-placement="top">
<!--                                <i class="bi bi-image"></i>-->
                            </button>
                            <input type="file" id="image-upload" accept="image/*" style="display: none;">
                            <button id="send-button" class="btn btn-icon btn-primary" aria-label="发送消息" title="发送消息">
                                <i class="bi bi-send"></i>
                            </button>
                            <!-- 暂停按钮，初始隐藏 -->
                            <button id="pause-button" class="btn btn-icon btn-danger" style="display: none;" aria-label="终止对话" title="终止对话">
                                <i class="bi bi-stop-fill"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>
</div>

<!-- Toast容器 -->
<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 9000"></div>

<script src="/wkg/js/vendor/jquery-3.7.1.min.js"></script> 
<!-- 必须先加载Bootstrap.js -->
<script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
<script src="/wkg/js/vendor/highlight.min.js"></script>
<!-- 添加 bootstrap-select JS -->
<script src="/wkg/js/vendor/bootstrap-select.min.js"></script>
<!-- 确保 highlight.js 正确初始化 -->
<script>
    // 确保 highlight.js 正确初始化
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof hljs !== 'undefined') {

            // 禁用自动检测，明确注册 Java 语言
            hljs.configure({
                languages: ['java', 'javascript', 'html', 'xml', 'css', 'python'],
                ignoreUnescapedHTML: true
            });

            // 初始化所有已存在的代码块
            document.querySelectorAll('pre code').forEach(function(block) {
                try {

                    hljs.highlightElement(block);
                } catch(e) {
                    console.error("初始化高亮失败:", e);
                }
            });


        } else {
            console.error("highlight.js 未加载!");
        }
    });
</script>
<!-- 非模块化的代码块处理脚本 -->
<script src="/wkg/js/features/chat/code-block-handler.js"></script>
<!-- 模块化脚本 -->
<script type="module" src="/wkg/js/shared/lib/streaming-markdown.js"></script>
<script type="module" src="/wkg/js/shared/components/modal-component.js"></script>
<script type="module" src="/wkg/js/shared/components/deep-thinking-toggle.js"></script>
<script type="module" src="/wkg/js/features/chat/model-selector-component.js"></script>
<script type="module" src="/wkg/js/features/chat/message-handler.js"></script>
<script type="module" src="/wkg/js/features/chat/chat-script.js"></script>
<!-- 初始化 bootstrap-select -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
    });
</script>
</body>
</html>