package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 聊天消息实体类
 */
@Data
@TableName("chat_message")
public class ChatMessage {
    
    /**
     * 消息ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 会话ID
     */
    private Long conversationId;
    
    /**
     * 发送者类型: user-用户, ai-AI
     */
    private String sender;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * AI思考过程内容
     */
    private String thinkingContent;
    
    /**
     * 使用的模型名称(仅AI消息有)
     */
    private String modelName;
    
    /**
     * 图片路径(如果有)
     */
    private String imagePath;
    
    /**
     * 消息Token数量
     */
    private Integer tokenCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 