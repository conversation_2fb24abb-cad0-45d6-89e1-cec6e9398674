<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EdgeMind 管理后台 - 修改密码</title>
    
    <!-- Bootstrap CSS -->
    <link href="/aistudio/css/vendor/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/aistudio/css/vendor/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="/aistudio/css/features/admin/change-password.css">
</head>
<body>
    <div class="password-container">
        <div class="password-header">
            <h2><i class="fas fa-key me-2"></i>修改密码</h2>
            <p>为了账户安全，请定期更换密码</p>
            <div th:if="${currentAdmin}" class="mt-2">
                <small class="text-muted">
                    当前用户：<strong th:text="${currentAdmin.nickname}">管理员</strong>
                </small>
            </div>
        </div>
        
        <!-- 消息提示区域 -->
        <div id="alertContainer"></div>
        
        <form id="changePasswordForm">
            <div class="form-floating position-relative">
                <input type="password" class="form-control" id="currentPassword" placeholder="当前密码" required>
                <label for="currentPassword"><i class="bi bi-lock me-2"></i>当前密码</label>
                <button type="button" class="password-toggle" onclick="togglePassword('currentPassword')">
                        <i class="bi bi-eye"></i>
                </button>
            </div>
            
            <div class="form-floating position-relative">
                <input type="password" class="form-control" id="newPassword" placeholder="新密码" required>
                <label for="newPassword"><i class="bi bi-key me-2"></i>新密码</label>
                <button type="button" class="password-toggle" onclick="togglePassword('newPassword')">
                        <i class="bi bi-eye"></i>
                </button>
                <div class="password-strength">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="strength-text">密码强度</span>
                        <span class="strength-level">请输入密码</span>
                    </div>
                    <div class="strength-bar">
                        <div class="strength-fill"></div>
                    </div>
                </div>
            </div>
            
            <div class="form-floating position-relative">
                <input type="password" class="form-control" id="confirmPassword" placeholder="确认新密码" required>
                <label for="confirmPassword"><i class="bi bi-check me-2"></i>确认新密码</label>
                <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                        <i class="bi bi-eye"></i>
                </button>
            </div>
            
            <button type="submit" class="btn btn-primary btn-change-password">
                <span class="spinner-border spinner-border-sm loading-spinner me-2" role="status"></span>
                <i class="fas fa-save me-2"></i>修改密码
            </button>
            
            <button type="button" class="btn btn-secondary btn-back" onclick="goBack()">
                <i class="fas fa-arrow-left me-2"></i>返回
            </button>
        </form>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aistudio/js/vendor/bootstrap.bundle.min.js"></script>
    <!-- API 工具类 -->
    <script src="/aistudio/js/common/api-utils.js"></script>
    <!-- 页面脚本 -->
    <script src="/aistudio/js/change-password.js"></script>
</body>
</html>