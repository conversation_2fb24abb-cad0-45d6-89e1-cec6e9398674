package com.zibbava.edgemind.cortex.strategy.impl;

import com.benjaminwan.ocrlibrary.OcrResult;
import com.zibbava.edgemind.cortex.strategy.DocumentParsingStrategy;
import dev.langchain4j.data.document.Document;
import io.github.mymonstercat.Model;
import io.github.mymonstercat.ocr.InferenceEngine;
import io.github.mymonstercat.ocr.config.ParamConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;

/**
 * PDF document parsing strategy implementation
 * Uses PDFBox for page-by-page processing, combined with RapidOCR for image content recognition
 */
@Component
@Slf4j
public class PdfParsingStrategy implements DocumentParsingStrategy {

    private static final float DPI = 300f; // 图片渲染DPI
    private static final String TEMP_DIR_PREFIX = "pdf_ocr_temp_";

    @Override
    public Document parseDocument(byte[] fileContent, String fileName) throws Exception {
        log.info("使用PDF解析策略处理文档: {}", fileName);

        PDDocument document = null;
        Path tempDir = null;

        try {
            // 加载PDF文档
            document = Loader.loadPDF(fileContent);

            // Create temporary directory
            tempDir = Files.createTempDirectory(TEMP_DIR_PREFIX);
            log.debug("Created temporary directory: {}", tempDir);

            // Initialize OCR engine
            InferenceEngine ocrEngine = InferenceEngine.getInstance(Model.ONNX_PPOCR_V4);
            ParamConfig paramConfig = ParamConfig.getDefaultConfig();

            // Create PDF renderer and text stripper
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            PDFTextStripper textStripper = new PDFTextStripper();

            List<String> pageContents = new ArrayList<>();
            int totalPages = document.getNumberOfPages();

            log.info("Starting to process PDF document with {} pages", totalPages);

            // Process page by page
            for (int pageIndex = 0; pageIndex < totalPages; pageIndex++) {
                log.debug("Processing page {}", pageIndex + 1);

                StringBuilder pageContent = new StringBuilder();

                try {
                    // 1. Extract text content
                    textStripper.setStartPage(pageIndex + 1);
                    textStripper.setEndPage(pageIndex + 1);
                    String textContent = textStripper.getText(document);

                    if (textContent != null && !textContent.trim().isEmpty()) {
                        pageContent.append("[Text Content]\n").append(textContent.trim()).append("\n\n");
                        log.debug("Page {} extracted text content: {} characters", pageIndex + 1, textContent.length());
                    }

                    // 2. Render page as image and perform OCR recognition
                    BufferedImage pageImage = pdfRenderer.renderImageWithDPI(pageIndex, DPI, ImageType.RGB);

                    // Save image to temporary file
                    File tempImageFile = tempDir.resolve("page_" + (pageIndex + 1) + ".png").toFile();
                    ImageIO.write(pageImage, "PNG", tempImageFile);

                    // Perform OCR recognition
                    OcrResult ocrResult = ocrEngine.runOcr(tempImageFile.getAbsolutePath(), paramConfig);
                    String ocrText = ocrResult.getStrRes();

                    if (ocrText != null && !ocrText.trim().isEmpty()) {
                        pageContent.append("[OCR Content]\n").append(ocrText.trim()).append("\n\n");
                        log.debug("Page {} OCR recognized content: {} characters", pageIndex + 1, ocrText.length());
                    }

                    // Clean up temporary image file
                    if (tempImageFile.exists()) {
                        tempImageFile.delete();
                    }

                } catch (Exception e) {
                    log.error("Exception occurred during PDF parsing: {}", e.getMessage(), e);
                    throw new RuntimeException("PDF parsing failed: " + e.getMessage(), e);
                }

                pageContents.add(pageContent.toString());
            }

            // Merge all page contents
            String finalContent = String.join("\n\n=== Page Separator ===\n\n", pageContents);

            log.info("PDF document processing completed, total extracted content length: {} characters", finalContent.length());

            return Document.from(finalContent);

        } finally {
            // Clean up resources
            if (document != null) {
                try {
                    document.close();
                    log.debug("PDDocument closed");
                } catch (IOException e) {
                    log.warn("Error closing PDDocument: {}", e.getMessage());
                }
            }

            // Clean up temporary directory
            if (tempDir != null) {
                try {
                    deleteDirectory(tempDir);
                    log.debug("Temporary directory cleaned up: {}", tempDir);
                } catch (Exception e) {
                    log.warn("Error cleaning up temporary directory: {}", e.getMessage());
                }
            }
        }
    }

    @Override
    public boolean supports(String fileName) {
        if (fileName == null) {
            return false;
        }
        return fileName.toLowerCase().endsWith(".pdf");
    }

    /**
     * Recursively delete directory and all its contents
     */
    private void deleteDirectory(Path directory) throws IOException {
        if (Files.exists(directory)) {
            Files.walk(directory)
                    .sorted((a, b) -> b.compareTo(a)) // delete files first, then directories
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("Failed to delete file: {}", path, e);
                        }
                    });
        }
    }
}