// 全局变量
let currentData = [];
let filteredData = [];
let currentPage = 1;
const pageSize = 10;

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        ApiUtils.post('/admin/logout')
            .then(() => {
                ApiUtils.redirectToLogin();
            })
            .catch(error => {
                console.error('退出登录失败:', error);
                ApiUtils.redirectToLogin();
            });
    }
}

// 初始化数据表格
function initDataTable() {
    showLoading(true);
    
    // 获取筛选条件
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const status = document.getElementById('statusFilter').value;
    
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    if (status) params.append('status', status);
    
    ApiUtils.get(`/admin/download-stats?${params.toString()}`)
        .then(response => {
            if (response.code === 200) {
                currentData = response.data || [];
                filteredData = [...currentData];
                renderTable();
                updateStatistics();
            } else {
                showError('获取数据失败: ' + (response.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('获取数据失败:', error);
            showError('网络错误，请稍后重试');
        })
        .finally(() => {
            showLoading(false);
        });
}

// 显示加载状态
function showLoading(show) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (show) {
        loadingOverlay.style.display = 'flex';
    } else {
        loadingOverlay.style.display = 'none';
    }
}

// 显示错误信息
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger alert-dismissible fade show';
    errorDiv.innerHTML = `
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(errorDiv, container.firstChild);
    
    // 5秒后自动隐藏
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 5000);
}

// 渲染表格
function renderTable() {
    const tbody = document.getElementById('dataTableBody');
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = filteredData.slice(startIndex, endIndex);
    
    if (pageData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="bi bi-inbox fs-1 text-muted"></i>
                    <p class="text-muted mt-2">暂无数据</p>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = pageData.map(item => `
        <tr>
            <td>${item.id || '-'}</td>
            <td>${item.fileName || '-'}</td>
            <td>${item.fileSize ? formatFileSize(item.fileSize) : '-'}</td>
            <td>${item.downloadTime ? formatDateTime(item.downloadTime) : '-'}</td>
            <td>${item.userIp || '-'}</td>
            <td>
                <span class="status-badge status-${getStatusClass(item.status)}">
                    ${getStatusText(item.status)}
                </span>
            </td>
            <td>
                <button class="btn btn-info btn-sm" onclick="showDetail(${item.id})">
                    <i class="bi bi-eye"></i> 详情
                </button>
            </td>
        </tr>
    `).join('');
    
    renderPagination();
}

// 渲染分页
function renderPagination() {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHtml = '';
    
    // 上一页
    paginationHtml += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            paginationHtml += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `;
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            paginationHtml += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // 下一页
    paginationHtml += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        </li>
    `;
    
    pagination.innerHTML = paginationHtml;
}

// 切换页面
function changePage(page) {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    if (page < 1 || page > totalPages) return;
    
    currentPage = page;
    renderTable();
}

// 更新统计数据
function updateStatistics() {
    const totalDownloads = filteredData.length;
    const successDownloads = filteredData.filter(item => item.status === 'SUCCESS').length;
    const failedDownloads = filteredData.filter(item => item.status === 'FAILED').length;
    const totalSize = filteredData.reduce((sum, item) => sum + (item.fileSize || 0), 0);
    
    document.getElementById('totalDownloads').textContent = totalDownloads;
    document.getElementById('successDownloads').textContent = successDownloads;
    document.getElementById('failedDownloads').textContent = failedDownloads;
    document.getElementById('totalSize').textContent = formatFileSize(totalSize);
}

// 应用筛选
function applyFilter() {
    currentPage = 1;
    initDataTable();
}

// 重置筛选
function resetFilter() {
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';
    document.getElementById('statusFilter').value = '';
    currentPage = 1;
    initDataTable();
}

// 刷新表格
function refreshTable() {
    currentPage = 1;
    initDataTable();
}

// 显示详情
function showDetail(id) {
    const item = currentData.find(d => d.id === id);
    if (!item) {
        showError('未找到相关数据');
        return;
    }
    
    document.getElementById('detailId').textContent = item.id || '-';
    document.getElementById('detailFileName').textContent = item.fileName || '-';
    document.getElementById('detailFileSize').textContent = item.fileSize ? formatFileSize(item.fileSize) : '-';
    document.getElementById('detailDownloadTime').textContent = item.downloadTime ? formatDateTime(item.downloadTime) : '-';
    document.getElementById('detailUserIp').textContent = item.userIp || '-';
    document.getElementById('detailUserAgent').textContent = item.userAgent || '-';
    document.getElementById('detailStatus').innerHTML = `
        <span class="status-badge status-${getStatusClass(item.status)}">
            ${getStatusText(item.status)}
        </span>
    `;
    document.getElementById('detailErrorMessage').textContent = item.errorMessage || '-';
    
    const modal = new bootstrap.Modal(document.getElementById('detailModal'));
    modal.show();
}

// 导出数据
function exportData() {
    if (filteredData.length === 0) {
        showError('没有数据可以导出');
        return;
    }
    
    const csvContent = generateCSV(filteredData);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `download-stats-${formatDate(new Date())}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 生成CSV内容
function generateCSV(data) {
    const headers = ['ID', '文件名', '文件大小', '下载时间', '用户IP', '状态', '错误信息'];
    const csvRows = [headers.join(',')];
    
    data.forEach(item => {
        const row = [
            item.id || '',
            `"${(item.fileName || '').replace(/"/g, '""')}"`,
            item.fileSize || '',
            item.downloadTime || '',
            item.userIp || '',
            getStatusText(item.status),
            `"${(item.errorMessage || '').replace(/"/g, '""')}"`
        ];
        csvRows.push(row.join(','));
    });
    
    return csvRows.join('\n');
}

// 工具函数
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDateTime(dateTime) {
    if (!dateTime) return '-';
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function getStatusClass(status) {
    switch (status) {
        case 'SUCCESS': return 'success';
        case 'FAILED': return 'failed';
        case 'PENDING': return 'pending';
        default: return 'secondary';
    }
}

function getStatusText(status) {
    switch (status) {
        case 'SUCCESS': return '成功';
        case 'FAILED': return '失败';
        case 'PENDING': return '进行中';
        default: return '未知';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 设置默认日期范围（最近30天）
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    
    document.getElementById('startDate').value = formatDate(startDate);
    document.getElementById('endDate').value = formatDate(endDate);
    
    // 初始化数据表格
    initDataTable();
    
    // 更新统计数据
    updateStatistics();
});