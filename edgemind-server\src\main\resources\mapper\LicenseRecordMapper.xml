<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zibbava.edgemind.server.mapper.LicenseRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.zibbava.edgemind.server.entity.LicenseRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="license_key" property="licenseKey" jdbcType="LONGVARCHAR"/>
        <result column="hardware_fingerprint" property="hardwareFingerprint" jdbcType="VARCHAR"/>
        <result column="system_identifier" property="systemIdentifier" jdbcType="VARCHAR"/>
        <result column="license_type" property="licenseType" jdbcType="TINYINT"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="activated_time" property="activatedTime" jdbcType="TIMESTAMP"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="generated_by" property="generatedBy" jdbcType="VARCHAR"/>
        <result column="client_info" property="clientInfo" jdbcType="LONGVARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 分页查询许可证记录 -->
    <select id="selectLicenseRecordPage" resultMap="BaseResultMap">
        SELECT
            id,
            license_key,
            hardware_fingerprint,
            system_identifier,
            license_type,
            status,
            create_time,
            activated_time,
            expire_time,
            generated_by,
            client_info,
            remark,
            update_time
        FROM license_record
        <where>
            <if test="licenseType != null">
                AND license_type = #{licenseType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="hardwareFingerprint != null and hardwareFingerprint != ''">
                AND hardware_fingerprint LIKE CONCAT('%', #{hardwareFingerprint}, '%')
            </if>
            <if test="systemIdentifier != null and systemIdentifier != ''">
                AND system_identifier LIKE CONCAT('%', #{systemIdentifier}, '%')
            </if>
            <if test="generatedBy != null and generatedBy != ''">
                AND generated_by LIKE CONCAT('%', #{generatedBy}, '%')
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 统计许可证数量 -->
    <select id="countByTypeAndStatus" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM license_record
        <where>
            <if test="licenseType != null">
                AND license_type = #{licenseType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

    <!-- 查询即将过期的许可证 -->
    <select id="selectExpiringLicenses" resultMap="BaseResultMap">
        SELECT
            id,
            license_key,
            hardware_fingerprint,
            system_identifier,
            license_type,
            status,
            create_time,
            activated_time,
            expire_time,
            generated_by,
            client_info,
            remark,
            update_time
        FROM license_record
        WHERE status = 1
          AND expire_time IS NOT NULL
          AND expire_time &lt;= DATE_ADD(NOW(), INTERVAL #{days} DAY)
          AND expire_time > NOW()
        ORDER BY expire_time ASC
    </select>

    <!-- 已移除过期状态更新SQL - 仅保留记录查询功能 -->

</mapper>