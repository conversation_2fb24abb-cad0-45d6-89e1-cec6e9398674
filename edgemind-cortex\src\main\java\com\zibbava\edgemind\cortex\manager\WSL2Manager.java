package com.zibbava.edgemind.cortex.manager;

import javax.swing.*;
import java.awt.*;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.util.concurrent.CompletableFuture;
import com.zibbava.edgemind.cortex.config.EnvironmentConfig;

/**
 * WSL2管理器
 * 负责WSL2的检测、安装和管理（基于Java的完整离线安装实现）
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class WSL2Manager {
    
    private static JProgressBar progressBar;
    private static JLabel statusLabel;
    private static boolean restartRequired = false;
    
    /**
     * 设置GUI组件引用
     */
    public static void setGuiComponents(JProgressBar progressBar, JLabel statusLabel) {
        WSL2Manager.progressBar = progressBar;
        WSL2Manager.statusLabel = statusLabel;
    }
    
    /**
     * 检查并安装WSL2（优先使用离线文件）
     */
    public static void checkAndInstallWSL2() {
        try {
            updateStatus("正在检查WSL2安装状态...");
            updateProgress(5);
            
            if (isWSL2Installed()) {
                updateStatus("WSL2已安装并配置完成");
                updateProgress(30);
                return;
            }
            
            if (isWSLInstalled()) {
                updateStatus("WSL已安装，正在配置WSL2...");
                updateProgress(15);
                configureWSL2();
                return;
            }
            
            updateStatus("WSL2未安装，开始检查离线安装文件...");
            updateProgress(7);
            
            // 检查系统要求
            if (!checkSystemRequirements()) {
                updateStatus("系统不满足WSL2要求，跳过WSL2安装");
                showSystemRequirementsDialog();
                return;
            }
            
            // 检查离线文件是否存在
            OfflineFiles offlineFiles = checkOfflineFiles();
            
            if (offlineFiles.isComplete()) {
                updateStatus("找到离线安装文件，开始离线安装...");
                installWSL2Offline(offlineFiles);
            } else {
                updateStatus("离线文件不完整，提供手动安装选项");
                showManualInstallOptions(offlineFiles);
            }
            
        } catch (Exception e) {
            System.err.println("检查WSL2时发生错误: " + e.getMessage());
            updateStatus("WSL2检查失败: " + e.getMessage());
            showErrorDialog("WSL2检查失败", e.getMessage());
        }
    }
    
    /**
     * 检查WSL2是否已安装并正确配置
     */
    public static boolean isWSL2Installed() {
        try {
            String wslPath = getWSLExecutablePath();
            
            // 检查WSL是否安装
            ProcessBuilder pb = new ProcessBuilder(wslPath, "--status");
            Process process = pb.start();
            int exitCode = process.waitFor();
            
            if (exitCode != 0) {
                return false;
            }
            
            // 检查是否有WSL2发行版
            pb = new ProcessBuilder(wslPath, "-l", "-v");
            process = pb.start();
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            boolean hasWSL2 = false;
            
            while ((line = reader.readLine()) != null) {
                if (line.contains("2") && !line.contains("NAME")) {
                    hasWSL2 = true;
                    break;
                }
            }
            
            process.waitFor();
            return hasWSL2;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查WSL是否安装（任何版本）
     */
    private static boolean isWSLInstalled() {
        try {
            String wslPath = getWSLExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(wslPath, "--list");
            Process process = pb.start();
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查系统要求
     */
    private static boolean checkSystemRequirements() {
        try {
            updateStatus("正在检查系统要求...");
            
            // 检查Windows版本
            ProcessBuilder pb = new ProcessBuilder(
                "powershell", "-Command",
                "$build = (Get-ItemProperty 'HKLM:SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion').CurrentBuild; " +
                "$build -ge 19041"
            );
            Process process = pb.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String result = reader.readLine();
            process.waitFor();
            
            if (result == null || !result.trim().equalsIgnoreCase("True")) {
                System.err.println("Windows版本不满足要求，需要Windows 10 Build 19041+或Windows 11");
                return false;
            }
            
            // 检查内存
            pb = new ProcessBuilder(
                "powershell", "-Command",
                "(Get-CimInstance Win32_PhysicalMemory | Measure-Object -Property capacity -Sum).Sum / 1GB -ge 4"
            );
            process = pb.start();
            reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            result = reader.readLine();
            process.waitFor();
            
            if (result == null || !result.trim().equalsIgnoreCase("True")) {
                System.err.println("系统内存不足，WSL2推荐至少4GB内存");
                // 内存不足不阻止安装，只是警告
            }
            
            // 检查虚拟化支持
            pb = new ProcessBuilder(
                "powershell", "-Command",
                "Get-WmiObject -Class Win32_Processor | Select-Object -ExpandProperty VirtualizationFirmwareEnabled"
            );
            process = pb.start();
            reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            result = reader.readLine();
            process.waitFor();
            
            if (result == null || !result.trim().equalsIgnoreCase("True")) {
                System.out.println("警告：无法确定虚拟化支持状态，请确保在BIOS中启用了虚拟化");
            }
            
            return true;
            
        } catch (Exception e) {
            System.err.println("检查系统要求时发生错误: " + e.getMessage());
            return true; // 检查失败不阻止安装
        }
    }
    
    /**
     * 离线文件信息类
     */
    private static class OfflineFiles {
        private String architecture;
        private File kernelFile;
        private File ubuntuFile;
        private boolean hasKernel;
        private boolean hasUbuntu;
        
        public OfflineFiles(String arch, File kernel, File ubuntu) {
            this.architecture = arch;
            this.kernelFile = kernel;
            this.ubuntuFile = ubuntu;
            this.hasKernel = kernel != null && kernel.exists();
            this.hasUbuntu = ubuntu != null && ubuntu.exists();
        }
        
        public boolean isComplete() {
            return hasKernel && hasUbuntu;
        }
        
        public String getStatus() {
            return String.format("架构:%s 内核:%s Ubuntu:%s", 
                architecture, 
                hasKernel ? "✓" : "✗", 
                hasUbuntu ? "✓" : "✗");
        }
        
        // Getters
        public String getArchitecture() { return architecture; }
        public File getKernelFile() { return kernelFile; }
        public File getUbuntuFile() { return ubuntuFile; }
        public boolean hasKernel() { return hasKernel; }
        public boolean hasUbuntu() { return hasUbuntu; }
    }
    
    /**
     * 检查离线安装文件
     */
    private static OfflineFiles checkOfflineFiles() {
        try {
            String basePath = EnvironmentConfig.getBasePath();
            // 所有安装文件与exe在同一目录，不是上级目录
            
            // 检测系统架构
            String arch = getSystemArchitecture();
            
            File kernelFile = new File(basePath, "wsl_update_" + arch + ".msi");
            File ubuntuFile = new File(basePath, "Ubuntu2204-221101.AppxBundle");
            
            OfflineFiles files = new OfflineFiles(arch, kernelFile, ubuntuFile);
            
            updateStatus("离线文件检查: " + files.getStatus());
            
            System.out.println("WSL2离线文件检查详情:");
            System.out.println("  项目目录: " + basePath);
            System.out.println("  系统架构: " + arch);
            System.out.println("  内核文件: " + kernelFile.getAbsolutePath() + " - " + (files.hasKernel() ? "存在" : "不存在"));
            System.out.println("  Ubuntu文件: " + ubuntuFile.getAbsolutePath() + " - " + (files.hasUbuntu() ? "存在" : "不存在"));
            
            return files;
            
        } catch (Exception e) {
            System.err.println("检查WSL2离线文件时发生错误: " + e.getMessage());
            String arch = "x64"; // 默认架构
            return new OfflineFiles(arch, null, null);
        }
    }
    
    /**
     * 获取WSL可执行文件的完整路径
     * @return WSL可执行文件路径，如果未找到则返回"wsl"
     */
    private static String getWSLExecutablePath() {
        // 尝试常见的WSL安装位置
        String[] possiblePaths = {
            "C:\\Windows\\System32\\wsl.exe",
            System.getenv("SystemRoot") + "\\System32\\wsl.exe"
        };
        
        for (String path : possiblePaths) {
            if (path != null && new File(path).exists()) {
                System.out.println("找到WSL可执行文件: " + path);
                return path;
            }
        }
        
        // 如果都没找到，返回默认命令（依赖环境变量）
        System.out.println("未找到WSL可执行文件，使用默认命令");
        return "wsl";
    }
    
    /**
     * 获取系统架构
     */
    private static String getSystemArchitecture() {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                "powershell", "-Command",
                "$arch = (Get-WmiObject Win32_OperatingSystem).OSArchitecture; " +
                "if ($arch -eq '64-bit') { 'x64' } elseif ($arch -eq 'ARM 64-bit') { 'arm64' } else { 'x64' }"
            );
            Process process = pb.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String result = reader.readLine();
            process.waitFor();
            
            if (result != null && result.trim().equals("arm64")) {
                return "arm64";
            } else {
                return "x64";
            }
            
        } catch (Exception e) {
            System.err.println("检测系统架构失败，默认使用x64: " + e.getMessage());
            return "x64";
        }
    }
    
    /**
     * 离线安装WSL2
     */
    private static void installWSL2Offline(OfflineFiles files) {
        try {
            updateStatus("开始WSL2离线安装 (" + files.getArchitecture() + "架构)");
            updateProgress(10);
            
            // 第一阶段：启用Windows功能
            if (!enableWSLFeatures()) {
                updateStatus("启用WSL功能失败");
                showErrorDialog("安装失败", "无法启用Windows子系统功能，请检查管理员权限");
                return;
            }
            
            // 第二阶段：安装内核更新包
            updateStatus("正在安装WSL2内核更新包...");
            updateProgress(15);
            
            if (!installKernelUpdate(files.getKernelFile())) {
                updateStatus("内核更新包安装失败");
                showErrorDialog("安装失败", "WSL2内核更新包安装失败，请检查文件是否损坏");
                return;
            }
            
            // 第三阶段：设置WSL2为默认
            updateStatus("正在设置WSL2为默认版本...");
            updateProgress(20);
            setWSL2AsDefault();
            
            // 第四阶段：安装Ubuntu
            updateStatus("正在安装Ubuntu发行版...");
            updateProgress(25);
            
            if (!installUbuntuDistribution(files.getUbuntuFile())) {
                updateStatus("Ubuntu发行版安装失败");
                showErrorDialog("安装失败", "Ubuntu发行版安装失败，请检查AppxBundle文件是否完整");
                return;
            }
            
            // 第五阶段：配置Ubuntu为WSL2
            updateStatus("正在配置Ubuntu为WSL2...");
            updateProgress(28);
            configureUbuntuAsWSL2();
            
            // 检查是否需要重启
            if (checkIfRestartRequired()) {
                restartRequired = true;
                showRestartDialog();
            } else {
                updateStatus("WSL2离线安装完成！");
                updateProgress(30);
//                showSuccessDialog();
            }
            
        } catch (Exception e) {
            System.err.println("离线安装WSL2时发生错误: " + e.getMessage());
            updateStatus("WSL2安装失败: " + e.getMessage());
            showErrorDialog("安装失败", "安装过程中发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 启用WSL功能
     */
    private static boolean enableWSLFeatures() {
        try {
            updateStatus("正在启用Windows子系统功能...");
            
            // 启用WSL功能
            ProcessBuilder pb = new ProcessBuilder(
                "powershell", "-Command", 
                "Start-Process powershell -ArgumentList '-Command'," +
                "'dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart'" +
                " -Verb RunAs -Wait -WindowStyle Hidden"
            );
            Process process = pb.start();
            int exitCode = process.waitFor();
            
            if (exitCode != 0) {
                System.err.println("启用WSL功能失败，退出码: " + exitCode);
                return false;
            }
            
            // 启用虚拟机平台功能
            updateStatus("正在启用虚拟机平台功能...");
            
            pb = new ProcessBuilder(
                "powershell", "-Command", 
                "Start-Process powershell -ArgumentList '-Command'," +
                "'dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart'" +
                " -Verb RunAs -Wait -WindowStyle Hidden"
            );
            process = pb.start();
            exitCode = process.waitFor();
            
            if (exitCode != 0) {
                System.err.println("启用虚拟机平台功能失败，退出码: " + exitCode);
                return false;
            }
            
            System.out.println("Windows功能启用成功");
            return true;
            
        } catch (Exception e) {
            System.err.println("启用Windows功能时发生错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 安装内核更新包
     */
    private static boolean installKernelUpdate(File kernelFile) {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                "powershell", "-Command", 
                "Start-Process msiexec.exe -ArgumentList '/i','" + 
                kernelFile.getAbsolutePath().replace("\\", "\\\\") + 
                "','/quiet','/norestart' -Verb RunAs -Wait -WindowStyle Hidden"
            );
            Process process = pb.start();
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                System.out.println("WSL2内核更新包安装成功");
                return true;
            } else {
                System.err.println("WSL2内核更新包安装失败，退出码: " + exitCode);
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("安装内核更新包时发生错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 设置WSL2为默认版本
     */
    private static void setWSL2AsDefault() {
        try {
            String wslPath = getWSLExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(wslPath, "--set-default-version", "2");
            Process process = pb.start();
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                System.out.println("WSL2设置为默认版本成功");
            } else {
                System.err.println("设置WSL2为默认版本失败，退出码: " + exitCode);
            }
            
        } catch (Exception e) {
            System.err.println("设置WSL2默认版本时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 安装Ubuntu发行版
     */
    private static boolean installUbuntuDistribution(File ubuntuFile) {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                "powershell", "-Command", 
                "Add-AppxPackage -Path '" + ubuntuFile.getAbsolutePath() + "'"
            );
            Process process = pb.start();
            
            // 读取输出以监控安装进度
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println("Ubuntu安装: " + line);
            }
            while ((line = errorReader.readLine()) != null) {
                System.err.println("Ubuntu安装警告: " + line);
            }
            
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                System.out.println("Ubuntu发行版安装成功");
                return true;
            } else {
                System.err.println("Ubuntu发行版安装失败，退出码: " + exitCode);
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("安装Ubuntu发行版时发生错误: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 配置Ubuntu为WSL2
     */
    private static void configureUbuntuAsWSL2() {
        try {
            // 等待发行版注册完成
            Thread.sleep(3000);
            
            String wslPath = getWSLExecutablePath();
            
            // 获取Ubuntu发行版名称
            ProcessBuilder pb = new ProcessBuilder(wslPath, "-l", "-v");
            Process process = pb.start();
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            String ubuntuName = null;
            
            while ((line = reader.readLine()) != null) {
                if (line.contains("Ubuntu") && !line.contains("NAME")) {
                    String[] parts = line.trim().split("\\s+");
                    if (parts.length > 0) {
                        ubuntuName = parts[0];
                        break;
                    }
                }
            }
            process.waitFor();
            
            if (ubuntuName != null) {
                // 设置Ubuntu使用WSL2
                pb = new ProcessBuilder(wslPath, "--set-version", ubuntuName, "2");
                process = pb.start();
                int exitCode = process.waitFor();
                
                if (exitCode == 0) {
                    System.out.println("Ubuntu配置为WSL2成功");
                } else {
                    System.err.println("配置Ubuntu为WSL2失败，退出码: " + exitCode);
                }
            } else {
                System.err.println("未找到Ubuntu发行版");
            }
            
        } catch (Exception e) {
            System.err.println("配置Ubuntu为WSL2时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 配置现有WSL为WSL2
     */
    private static void configureWSL2() {
        try {
            updateStatus("正在配置现有WSL为WSL2...");
            
            // 设置默认版本
            setWSL2AsDefault();
            
            String wslPath = getWSLExecutablePath();
            
            // 获取所有发行版并升级到WSL2
            ProcessBuilder pb = new ProcessBuilder(wslPath, "-l", "-v");
            Process process = pb.start();
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            
            while ((line = reader.readLine()) != null) {
                if (line.contains("1") && !line.contains("NAME")) {
                    String[] parts = line.trim().split("\\s+");
                    if (parts.length > 0) {
                        String distroName = parts[0];
                        
                        updateStatus("正在升级 " + distroName + " 到WSL2...");
                        pb = new ProcessBuilder(wslPath, "--set-version", distroName, "2");
                        Process upgradeProcess = pb.start();
                        upgradeProcess.waitFor();
                    }
                }
            }
            process.waitFor();
            
            updateStatus("WSL2配置完成");
            updateProgress(30);
            
        } catch (Exception e) {
            System.err.println("配置WSL2时发生错误: " + e.getMessage());
            updateStatus("WSL2配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否需要重启
     */
    private static boolean checkIfRestartRequired() {
        try {
            // 检查挂起的Windows功能操作
            ProcessBuilder pb = new ProcessBuilder(
                "powershell", "-Command",
                "Get-WindowsOptionalFeature -Online | Where-Object {$_.FeatureName -like '*Linux*' -and $_.RestartRequired -eq $true}"
            );
            Process process = pb.start();
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line = reader.readLine();
            process.waitFor();
            
            return line != null && !line.trim().isEmpty();
            
        } catch (Exception e) {
            // 无法确定时，假设需要重启
            return true;
        }
    }
    
    /**
     * 显示重启对话框
     */
    private static void showRestartDialog() {
        SwingUtilities.invokeLater(() -> {
            updateStatus("WSL2安装完成，需要重启计算机");
            
            String message = "WSL2安装完成！\n\n" +
                           "系统需要重启以完成安装。\n" +
                           "重启后WSL2将可以正常使用。\n\n" +
                           "是否现在重启计算机？";
            
            int choice = JOptionPane.showConfirmDialog(
                null,
                message,
                "WSL2安装完成 - 需要重启",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.INFORMATION_MESSAGE
            );
            
            if (choice == JOptionPane.YES_OPTION) {
                // 执行重启
                restartComputer();
            } else {
                JOptionPane.showMessageDialog(
                    null,
                    "请在方便时手动重启计算机以完成WSL2安装。",
                    "提醒",
                    JOptionPane.INFORMATION_MESSAGE
                );
            }
        });
    }
    
    /**
     * 重启计算机
     */
    private static void restartComputer() {
        try {
            ProcessBuilder pb = new ProcessBuilder("shutdown", "/r", "/t", "10", "/c", "WSL2安装完成，系统将在10秒后重启");
            pb.start();
        } catch (Exception e) {
            System.err.println("执行重启命令失败: " + e.getMessage());
            JOptionPane.showMessageDialog(
                null,
                "自动重启失败，请手动重启计算机。",
                "重启失败",
                JOptionPane.WARNING_MESSAGE
            );
        }
    }
    
    /**
     * 显示成功对话框
     */
    private static void showSuccessDialog() {
        SwingUtilities.invokeLater(() -> {
            JOptionPane.showMessageDialog(
                null,
                "WSL2安装并配置完成！请重启电脑完成后续安装",
                "安装成功",
                JOptionPane.INFORMATION_MESSAGE
            );
        });
    }
    
    /**
     * 显示系统要求对话框
     */
    private static void showSystemRequirementsDialog() {
        SwingUtilities.invokeLater(() -> {
            String message = "系统不满足WSL2最低要求：\n\n" +
                           "• Windows 10 Build 19041+或Windows 11\n" +
                           "• 启用虚拟化功能（BIOS设置）\n" +
                           "• 推荐4GB以上内存\n\n" +
                           "请升级系统或检查BIOS设置后重试。";
            
            JOptionPane.showMessageDialog(
                null,
                message,
                "系统要求不满足",
                JOptionPane.WARNING_MESSAGE
            );
        });
    }
    
    /**
     * 显示手动安装选项
     */
    private static void showManualInstallOptions(OfflineFiles files) {
        SwingUtilities.invokeLater(() -> {
            String message = "WSL2离线安装文件检查结果：\n\n" + files.getStatus() + "\n\n";
            
            if (!files.hasKernel()) {
                message += "缺少WSL2内核更新包 (wsl_update_" + files.getArchitecture() + ".msi)\n";
            }
            if (!files.hasUbuntu()) {
                message += "缺少Ubuntu发行版 (Ubuntu2204-221101.AppxBundle)\n";
            }
            
            message += "\n请下载缺少的文件到项目目录（与exe同一目录），或选择在线安装。";
            
            String[] options = {"重新检查文件", "在线安装", "跳过WSL2安装"};
            int choice = JOptionPane.showOptionDialog(
                null,
                message,
                "WSL2离线文件不完整",
                JOptionPane.YES_NO_CANCEL_OPTION,
                JOptionPane.WARNING_MESSAGE,
                null,
                options,
                options[0]
            );
            
            switch (choice) {
                case 0: // 重新检查
                    CompletableFuture.runAsync(() -> checkAndInstallWSL2());
                    break;
                case 1: // 在线安装
                    CompletableFuture.runAsync(() -> installWSL2Online());
                    break;
                case 2: // 跳过
                default:
                    updateStatus("跳过WSL2安装");
                    break;
            }
        });
    }
    
    /**
     * 在线安装WSL2（备用方案）
     */
    private static void installWSL2Online() {
        try {
            updateStatus("开始在线安装WSL2...");
            updateProgress(10);
            
            String wslPath = getWSLExecutablePath();
            
            // 使用Windows 11的wsl --install命令
            ProcessBuilder pb = new ProcessBuilder(wslPath, "--install", "--no-launch");
            Process process = pb.start();
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                updateStatus("WSL2在线安装完成，需要重启");
                restartRequired = true;
                showRestartDialog();
            } else {
                updateStatus("WSL2在线安装失败");
                showErrorDialog("在线安装失败", "WSL在线安装失败，请检查网络连接或使用离线安装方式");
            }
            
        } catch (Exception e) {
            System.err.println("在线安装WSL2时发生错误: " + e.getMessage());
            updateStatus("WSL2在线安装失败: " + e.getMessage());
            showErrorDialog("在线安装失败", "安装过程中发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 显示错误对话框
     */
    private static void showErrorDialog(String title, String message) {
        SwingUtilities.invokeLater(() -> {
            JOptionPane.showMessageDialog(
                null,
                message,
                title,
                JOptionPane.ERROR_MESSAGE
            );
        });
    }
    
    /**
     * 更新状态标签
     */
    private static void updateStatus(String message) {
        SwingUtilities.invokeLater(() -> {
            if (statusLabel != null) {
                statusLabel.setText(message);
            }
        });
        System.out.println("WSL2状态: " + message);
    }
    
    /**
     * 更新进度条
     */
    private static void updateProgress(int value) {
        SwingUtilities.invokeLater(() -> {
            if (progressBar != null) {
                progressBar.setValue(value);
            }
        });
    }

}