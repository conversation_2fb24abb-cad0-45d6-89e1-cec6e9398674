package com.zibbava.edgemind.cortex.config;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.ollama.OllamaChatModel;
import dev.langchain4j.model.ollama.OllamaStreamingChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
public class ChatModelProvider {

    @Value("${ai.model.type:ollama}")
    private String modelType;

    @Value("${ai.model.name:deepseek-r1:7b}")
    private String modelName;

    @Value("${ai.model.baseUrl:http://localhost:11434}")
    private String baseUrl;

    @Value("${ai.model.apiKey:}")
    private String apiKey;

    /**
     * Provides the primary ChatLanguageModel bean based on configuration.
     * @return ChatLanguageModel instance.
     */
    @Bean
    public ChatModel chatLanguageModel() {
        System.out.println("Creating ChatLanguageModel bean of type: " + modelType);
        if ("openai".equalsIgnoreCase(modelType) || "deepseek".equalsIgnoreCase(modelType)) {
            return OpenAiChatModel.builder()
                    .baseUrl(baseUrl)
                    .modelName(modelName)
                    .apiKey(apiKey)
                    .timeout(Duration.ofSeconds(300L))
                    .logRequests(true) // Enable logging for debugging
                    .logResponses(true)
                    .build();
        } else if ("ollama".equalsIgnoreCase(modelType)) {
            return OllamaChatModel.builder()
                    .baseUrl(baseUrl)
                    .modelName(modelName)
                    .logRequests(true)
                    .logResponses(true)
                    .build();
        } else {
            System.err.println("Unsupported model type specified: " + modelType + ". Defaulting to Ollama.");
            return OllamaChatModel.builder()
                    .baseUrl(baseUrl)
                    .modelName(modelName)
                    .logRequests(true)
                    .logResponses(true)
                    .build();
        }
    }

    /**
     * Provides the primary StreamingChatLanguageModel bean based on configuration.
     * @return StreamingChatLanguageModel instance.
     */
    @Bean
    public StreamingChatModel streamingChatLanguageModel() {
        System.out.println("Creating StreamingChatLanguageModel bean of type: " + modelType);
        if ("openai".equalsIgnoreCase(modelType) || "deepseek".equalsIgnoreCase(modelType)) {
            return OpenAiStreamingChatModel.builder()
                    .baseUrl(baseUrl)
                    .modelName(modelName)
                    .apiKey(apiKey)
                    .logRequests(true)
                    .logResponses(true)
                    .build();
        } else if ("ollama".equalsIgnoreCase(modelType)) {
            return OllamaStreamingChatModel.builder()
                    .baseUrl(baseUrl)
                    .modelName(modelName)
                    .logRequests(true)
                    .logResponses(true)
                    .build();
        } else {
            System.err.println("Unsupported streaming model type specified: " + modelType + ". Defaulting to Ollama.");
            return OllamaStreamingChatModel.builder()
                    .baseUrl(baseUrl)
                    .modelName(modelName)
                    .logRequests(true)
                    .logResponses(true)
                    .build();
        }
    }
} 