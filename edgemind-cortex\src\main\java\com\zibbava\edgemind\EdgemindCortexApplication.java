package com.zibbava.edgemind;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import com.zibbava.edgemind.cortex.manager.EnvironmentManager;
import com.zibbava.edgemind.cortex.manager.DockerManager;
import com.zibbava.edgemind.cortex.manager.OllamaManager;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.awt.*;
import java.net.URI;
import java.util.concurrent.CountDownLatch;


/**
 * 端智 AI 助手主启动类
 * 在Spring Boot应用启动前显示GUI界面并检测Docker状态
 */
@SpringBootApplication
@MapperScan("com.zibbava.**.mapper")
@EnableScheduling
@EnableEncryptableProperties
public class EdgemindCortexApplication {



	public static void main(String[] args) {
		//开发模式直接指定安装doecker ollama地址,非开发模式默认当前路径下
		// System.setProperty("edgemind.dev.mode", "false"); // 注释掉此行，以允许通过VM options (-Dedgemind.dev.mode=true) 来控制
		boolean closeGui = true;
		System.out.println("当前运行模式: " + (closeGui ? "开发模式" : "生产模式"));


		// 根据开发模式决定是否显示GUI
		CountDownLatch environmentReadyLatch;
		if (closeGui) {
			// 开发模式：跳过GUI检查，直接启动
			System.out.println("开发模式：跳过环境检查GUI，直接启动应用");
			environmentReadyLatch = new CountDownLatch(0); // 直接就绪
		} else {

			// 添加JVM shutdown hook来确保退出时正确清理资源
			Runtime.getRuntime().addShutdownHook(new Thread(() -> {
				System.out.println("检测到应用程序退出，正在清理资源...");
				performCleanup();
			}));
			// 生产模式：显示完整的启动GUI和环境检查
			System.out.println("生产模式：启动环境检查GUI");
			environmentReadyLatch = EnvironmentManager.showStartupGuiAndCheckEnvironment();
		}

		try {
			// 等待环境就绪
			environmentReadyLatch.await();

			// 环境OK后，更新GUI状态，准备启动Web服务
			EnvironmentManager.updateStatus("正在启动Web服务，请稍候...");

			// 启动Spring Boot应用
			ConfigurableApplicationContext context = SpringApplication.run(EdgemindCortexApplication.class, args);
			
			// Web服务启动后，才关闭启动窗口
			EnvironmentManager.closeStartupGui();

			// 在应用启动后打开浏览器
			openBrowserOnStartup(context);

		} catch (InterruptedException e) {
			System.err.println("等待环境启动时被中断: " + e.getMessage());
			Thread.currentThread().interrupt();
		}
	}

	/**
	 * 在应用启动后打开浏览器
	 * @param context 应用上下文
	 */
	private static void openBrowserOnStartup(ConfigurableApplicationContext context) {
		// 检查GUI环境是否可用
		if (GraphicsEnvironment.isHeadless()) {
			System.out.println("检测到无头环境，跳过打开浏览器。");
			return;
		}

		// 从环境中获取端口，但使用固定的目标路径
		String port = context.getEnvironment().getProperty("server.port", "8080");
		String url = String.format("http://localhost:%s/wkg/", port);

		System.out.println("应用启动完成，正在尝试打开浏览器访问: " + url);

		try {
			// 使用Desktop类打开默认浏览器
			Desktop.getDesktop().browse(new URI(url));
			System.out.println("成功打开浏览器。");
		} catch (Exception e) {
			System.err.println("自动打开浏览器失败，请手动访问 " + url);
			e.printStackTrace();
		}
	}

	/**
	 * 执行资源清理
	 */
	private static void performCleanup() {
		try {
			System.out.println("正在停止Docker服务...");
			DockerManager.stopDocker();
			
			System.out.println("正在停止Ollama服务...");
			OllamaManager.stopOllama();
			
			// 停止Web进程（通过端口8080）
			System.out.println("正在停止Web服务...");
			stopWebProcess();
			
			System.out.println("资源清理完成");
			
		} catch (Exception e) {
			System.err.println("清理资源时发生错误: " + e.getMessage());
		}
	}

	/**
	 * 停止Web进程
	 */
	private static void stopWebProcess() {
		try {
			// 通过端口8080查找并终止进程
			ProcessBuilder pb = new ProcessBuilder(
				"powershell", "-Command",
				"Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue }"
			);
			Process process = pb.start();
			boolean finished = process.waitFor(10, java.util.concurrent.TimeUnit.SECONDS);
			
			if (finished) {
				System.out.println("Web进程已停止");
			} else {
				System.out.println("Web进程停止超时");
				process.destroyForcibly();
			}
			
		} catch (Exception e) {
			System.err.println("停止Web进程失败: " + e.getMessage());
		}
	}
}
