package com.zibbava.edgemind.cortex.util;

import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.ollama.OllamaStreamingChatModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 模型工具类，统一提供AI模型相关的工具方法
 */
@Component
public class ModelUtils {

    @Value("${ai.model.ollama.baseUrl:http://localhost:11434}")
    private String ollamaBaseUrl;

    @Value("${ai.model.ollama.timeout:120}")
    private long ollamaTimeoutSeconds;

    @Value("${ai.model.ollama.temperature:0.7}")
    private double ollamaTemperature;

    @Value("${ai.model.ollama.topP:0.9}")
    private double ollamaTopP;

    @Value("${ai.model.deepseek.baseUrl:https://api.deepseek.com/v1}")
    private String deepseekBaseUrl;
    @Value("${ai.model.deepseek.apiKey:***********************************}")
    private String deepseekApiKey;
    @Value("${ai.model.deepseek.modelName:deepseek-reasoner}")
    private String deepseekModelName;
    @Value("${ai.model.deepseek.timeout:120}")
    private long deepseekTimeoutSeconds;
    @Value("${run.env:dev}")
    private String runEnv;

    /**
     * 获取Ollama流式聊天语言模型
     *
     * @param modelName 模型名称
     * @return 配置好的流式聊天语言模型
     */
    public StreamingChatModel getStreamingChatModel(String modelName) {
        if (runEnv.equals("demo")) {
            return OpenAiStreamingChatModel.builder()
                    .apiKey("sk-cfc07f26f6de47fc927275d7f1304632")
                    .modelName("qwen-turbo")
                    .baseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1")
                    .build();
        }
        if ("deepseek-chat".equals(modelName)) {
            return OpenAiStreamingChatModel.builder()
                    .baseUrl(deepseekBaseUrl)
                    .apiKey(deepseekApiKey)
                    .modelName("deepseek-chat")
                    .timeout(Duration.ofSeconds(120))
                    .build();
        }
        return OllamaStreamingChatModel.builder()
                .baseUrl(ollamaBaseUrl)
                .modelName(modelName)
                .timeout(Duration.ofSeconds(ollamaTimeoutSeconds))
                .temperature(ollamaTemperature)
                .topP(ollamaTopP)
                .build();
    }
}
