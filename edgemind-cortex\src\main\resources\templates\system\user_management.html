<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 端智AI助手</title>
    <!-- 使用EdgeMind本地资源 -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <!-- EdgeMind设计系统样式 -->
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/components/modal-unified.css">
</head>
<body style="background-color: #f8f9fa; padding: 20px;">
    <div class="container-fluid">
        <!-- 页面标题 - 使用EdgeMind标准样式 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-primary fw-semibold mb-0">
                <i class="bi bi-person-fill me-2"></i>
                用户管理
            </h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="#" class="text-decoration-none" style="color: var(--text-secondary);">系统管理</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">用户管理</li>
                </ol>
            </nav>
        </div>

        <!-- 搜索表单 - 使用EdgeMind卡片样式 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <form id="searchForm" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">用户名</label>
                        <input type="text" class="form-control border-1" id="searchUsername"
                               placeholder="请输入用户名" style="border-color: var(--border-color);">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">昵称</label>
                        <input type="text" class="form-control border-1" id="searchNickname"
                               placeholder="请输入昵称" style="border-color: var(--border-color);">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">部门</label>
                        <select class="form-select border-1" id="searchDept" style="border-color: var(--border-color);">
                            <option value="">全部部门</option>
                            <!-- 部门选项将通过JS动态加载 -->
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">状态</label>
                        <select class="form-select border-1" id="searchStatus" style="border-color: var(--border-color);">
                            <option value="">全部状态</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary px-4"
                                style="background-color: var(--primary-color); border-color: var(--primary-color);">
                            <i class="bi bi-search me-2"></i>搜索
                        </button>
                        <button type="button" class="btn btn-outline-secondary ms-2 px-3" onclick="resetSearch()">
                            <i class="bi bi-arrow-clockwise me-2"></i>重置
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 操作按钮组 - 使用EdgeMind按钮样式 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <button type="button" class="btn btn-success px-4" onclick="showCreateModal()">
                        <i class="bi bi-plus-lg me-2"></i>新增用户
                    </button>
                    <button type="button" class="btn btn-danger px-4" onclick="batchDelete()" disabled id="batchDeleteBtn">
                        <i class="bi bi-trash me-2"></i>批量删除
                    </button>
                    <button type="button" class="btn btn-info px-4" onclick="exportUsers()">
                        <i class="bi bi-download me-2"></i>导出数据
                    </button>
                </div>
            </div>
        </div>

        <!-- 用户列表表格 - 使用EdgeMind表格样式 -->
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead style="background-color: var(--primary-light);">
                            <tr>
                                <th class="fw-semibold" style="color: var(--text-primary);">
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                </th>
                                <th class="fw-semibold" style="color: var(--text-primary);">头像</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">用户名</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">昵称</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">邮箱</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">手机号</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">部门</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">状态</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">最后登录</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">创建时间</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody">
                            <!-- 用户数据将通过JS动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 - 使用EdgeMind分页样式 -->
                <nav aria-label="用户列表分页" class="mt-4">
                    <ul class="pagination justify-content-center mb-0" id="pagination">
                        <!-- 分页按钮将通过JS动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 创建/编辑用户模态框 - 使用EdgeMind模态框样式 -->
    <div class="modal fade modal-unified" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content border-0 shadow">
                <div class="modal-header" style="background-color: var(--primary-light); border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title fw-semibold" id="userModalTitle" style="color: var(--text-primary);">
                        <i class="bi bi-person-plus me-2"></i>新增用户
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">
                                    用户名 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control border-1" id="username" required
                                       style="border-color: var(--border-color);">
                                <div class="form-text" style="color: var(--text-secondary);">
                                    用户名只能包含字母、数字和下划线
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">昵称</label>
                                <input type="text" class="form-control border-1" id="nickname"
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6" id="passwordField">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">
                                    密码 <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control border-1" id="password" required
                                       style="border-color: var(--border-color);">
                                <div class="form-text" style="color: var(--text-secondary);">
                                    密码长度6-20位
                                </div>
                            </div>
                            <div class="col-md-6" id="confirmPasswordField">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">
                                    确认密码 <span class="text-danger">*</span>
                                </label>
                                <input type="password" class="form-control border-1" id="confirmPassword" required
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">邮箱</label>
                                <input type="email" class="form-control border-1" id="email"
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">手机号</label>
                                <input type="tel" class="form-control border-1" id="phone"
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">所属部门</label>
                                <select class="form-select border-1" id="deptId" style="border-color: var(--border-color);">
                                    <option value="">请选择部门</option>
                                    <!-- 部门选项将通过JS动态加载 -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">状态</label>
                                <select class="form-select border-1" id="status" style="border-color: var(--border-color);">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">角色分配</label>
                                <div id="roleCheckboxes" class="row g-2 p-3 rounded"
                                     style="background-color: var(--hover-bg); border: 1px solid var(--border-color);">
                                    <!-- 角色复选框将通过JS动态生成 -->
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">备注</label>
                                <textarea class="form-control border-1" id="remark" rows="3"
                                          style="border-color: var(--border-color);"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="background-color: #f8f9fa; border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary px-4" onclick="saveUser()"
                            style="background-color: var(--primary-color); border-color: var(--primary-color);">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 重置密码模态框 - 使用EdgeMind模态框样式 -->
    <div class="modal fade modal-unified" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content border-0 shadow">
                <div class="modal-header" style="background-color: #fff3cd; border-bottom: 1px solid #ffeaa7;">
                    <h5 class="modal-title fw-semibold text-warning">
                        <i class="bi bi-key me-2"></i>重置密码
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="resetPasswordForm">
                        <input type="hidden" id="resetUserId">
                        <div class="mb-3">
                            <label class="form-label fw-medium" style="color: var(--text-primary);">
                                新密码 <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control border-1" id="newPassword" required
                                   style="border-color: var(--border-color);">
                            <div class="form-text" style="color: var(--text-secondary);">
                                密码长度6-20位
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-medium" style="color: var(--text-primary);">
                                确认新密码 <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control border-1" id="confirmNewPassword" required
                                   style="border-color: var(--border-color);">
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="background-color: #f8f9fa; border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning px-4" onclick="resetPassword()">确认重置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 修改密码模态框 - 用户自己修改密码 -->
    <div class="modal fade modal-unified" id="changePasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content border-0 shadow">
                <div class="modal-header" style="background-color: var(--primary-light); border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title fw-semibold" style="color: var(--text-primary);">
                        <i class="bi bi-shield-lock me-2"></i>修改密码
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="changePasswordForm">
                        <input type="hidden" id="changeUserId">
                        <div class="mb-3">
                            <label class="form-label fw-medium" style="color: var(--text-primary);">
                                原密码 <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control border-1" id="oldPassword" required
                                   style="border-color: var(--border-color);">
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-medium" style="color: var(--text-primary);">
                                新密码 <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control border-1" id="changeNewPassword" required
                                   style="border-color: var(--border-color);">
                            <div class="form-text" style="color: var(--text-secondary);">
                                密码长度6-20位
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-medium" style="color: var(--text-primary);">
                                确认新密码 <span class="text-danger">*</span>
                            </label>
                            <input type="password" class="form-control border-1" id="changeConfirmPassword" required
                                   style="border-color: var(--border-color);">
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="background-color: #f8f9fa; border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary px-4" onclick="changePassword()"
                            style="background-color: var(--primary-color); border-color: var(--primary-color);">确认修改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用EdgeMind本地JavaScript资源 -->
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script type="module" src="/wkg/js/shared/components/modal-component.js"></script>
    <script src="/wkg/js/system/user-management.js"></script>
</body>
</html>
