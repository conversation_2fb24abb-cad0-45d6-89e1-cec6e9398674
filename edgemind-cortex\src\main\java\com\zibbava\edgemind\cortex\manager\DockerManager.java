package com.zibbava.edgemind.cortex.manager;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zibbava.edgemind.cortex.config.EnvironmentConfig;

import javax.swing.*;
import java.awt.*;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Docker管理器
 * 负责Docker的检测、安装、启动、停止和状态监控（基于Java的完整实现）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DockerManager {

    private static JLabel statusLabel;
    private static JProgressBar progressBar;
    private static boolean restartRequired = false;

    /**
     * 获取Docker安装路径
     */
    private static String getDockerInstallPath() {
        return EnvironmentConfig.getBasePath() + "\\Docker";
    }

    /**
     * 设置GUI组件
     */
    public static void setGuiComponents(JLabel statusLabel, JProgressBar progressBar, Object dockerReadyLatch) {
        DockerManager.statusLabel = statusLabel;
        DockerManager.progressBar = progressBar;
    }

    /**
     * 检查并启动Docker（优先使用离线安装）
     */
    public static void checkAndStartDocker() {
        try {
            updateStatus("正在检查Docker服务及所有容器状态...");
            updateProgress(30);

            // 优化启动流程：首先检查所有容器是否都已正常运行
            if (isDockerRunning() && verifyDockerComposeServices(true)) {
                updateStatus("所有服务容器均已在运行。");
                updateProgress(90); // 直接跳到较高进度
                return; // 所有服务都OK，直接返回
            }

            // 如果容器不完整或Docker未运行，则继续执行标准检查流程
            updateStatus("正在检查Docker安装状态...");
            if (isDockerRunning()) {
                // Docker在运行，但容器不完整
                updateStatus("Docker已运行，但部分服务容器未启动，尝试启动...");
                startDockerComposeServices();
                // 再次验证
                if (!verifyDockerComposeServices(false)) { // 使用常规模式（带等待）验证
                    showErrorDialog("容器启动失败", "尝试启动后，部分或全部服务容器仍无法启动。");
                    return;
                }
                // 验证成功后，流程可以结束
                updateStatus("所有服务容器运行正常。");
                updateProgress(90);
                return;
            }

            if (isDockerInstalled()) {
                updateStatus("Docker已安装，正在启动服务...");
                updateProgress(35);

                if (startDockerServices()) {
                    updateStatus("Docker启动成功，检查镜像...");
                    updateProgress(55);

                    // 检查Docker镜像
                    if (!checkDockerImages()) {
                        updateStatus("正在检查和加载Docker镜像...");
                        updateProgress(65);
                        loadDockerImagesFromFiles();
                    } else {
                        updateStatus("Docker镜像已齐全");
                        updateProgress(65);
                    }

                    // 启动Docker Compose服务
                    updateStatus("正在启动Docker Compose服务...");
                    updateProgress(68);
                    startDockerComposeServices();

                    updateStatus("Docker启动成功");
                    updateProgress(70);
                    return;
                } else {
                    updateStatus("Docker启动失败，检查安装完整性...");
                    showDockerTroubleshootDialog();
                    return;
                }
            }

            updateStatus("Docker未安装，检查离线安装文件...");
            updateProgress(32);

            // 检查离线安装文件
            DockerOfflineFiles offlineFiles = checkDockerOfflineFiles();

            if (offlineFiles.isComplete()) {
                updateStatus("找到Docker离线安装文件，开始离线安装...");
                installDockerOffline(offlineFiles);
            } else {
                updateStatus("Docker离线文件不完整，提供安装选项");
                showDockerInstallOptions(offlineFiles);
            }

        } catch (Exception e) {
            System.err.println("检查和启动Docker时出错: " + e.getMessage());
            updateStatus("Docker检查失败: " + e.getMessage());
            showErrorDialog("Docker检查失败", e.getMessage());
        }
    }

    /**
     * 检查Docker是否正在运行
     */
    public static boolean isDockerRunning() {
        try {
            String dockerPath = getDockerExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(dockerPath, "version", "--format", "{{.Server.Version}}");
            Process process = pb.start();
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);

            if (finished && process.exitValue() == 0) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String version = reader.readLine();
                return version != null && !version.trim().isEmpty();
            }
        } catch (Exception e) {
            System.err.println("检查Docker运行状态时出错: " + e.getMessage());
        }
        return false;
    }

    /**
     * 检查Docker是否已安装
     */
    public static boolean isDockerInstalled() {
        try {
            String dockerPath = getDockerExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(dockerPath, "--version");
            Process process = pb.start();
            boolean finished = process.waitFor(5, TimeUnit.SECONDS);

            return finished && process.exitValue() == 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证Docker服务状态
     */
    private static boolean verifyDockerServices() {
        try {
            String dockerPath = getDockerExecutablePath();

            // 检查Docker Engine状态
            ProcessBuilder pb = new ProcessBuilder(dockerPath, "info", "--format", "{{.ServerVersion}}");
            Process process = pb.start();
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);

            if (!finished || process.exitValue() != 0) {
                return false;
            }

            // 检查Docker Compose
            pb = new ProcessBuilder(dockerPath, "compose", "version");
            process = pb.start();
            finished = process.waitFor(5, TimeUnit.SECONDS);

            return finished && process.exitValue() == 0;

        } catch (Exception e) {
            System.err.println("验证Docker服务时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * Docker离线文件信息类
     */
    private static class DockerOfflineFiles {
        private File dockerInstaller;
        private File dockerComposeFile;
        private File dockerImagesDir;
        private boolean hasInstaller;
        private boolean hasCompose;
        private boolean hasImages;

        public DockerOfflineFiles(File installer, File compose, File images) {
            this.dockerInstaller = installer;
            this.dockerComposeFile = compose;
            this.dockerImagesDir = images;
            this.hasInstaller = installer != null && installer.exists();
            this.hasCompose = compose != null && compose.exists();
            this.hasImages = images != null && images.exists() && images.isDirectory();
        }

        public boolean isComplete() {
            return hasInstaller; // 至少需要安装程序
        }

        public String getStatus() {
            return String.format("Docker安装程序:%s Compose:%s 镜像:%s",
                    hasInstaller ? "✓" : "✗",
                    hasCompose ? "✓" : "✗",
                    hasImages ? "✓" : "✗");
        }

        // Getters
        public File getDockerInstaller() {
            return dockerInstaller;
        }

        public File getDockerComposeFile() {
            return dockerComposeFile;
        }

        public File getDockerImagesDir() {
            return dockerImagesDir;
        }

        public boolean hasInstaller() {
            return hasInstaller;
        }

        public boolean hasCompose() {
            return hasCompose;
        }

        public boolean hasImages() {
            return hasImages;
        }
    }

    /**
     * 检查Docker离线安装文件
     */
    private static DockerOfflineFiles checkDockerOfflineFiles() {
        try {
            String basePath = EnvironmentConfig.getBasePath();
            String environment = EnvironmentConfig.IS_DEVELOPMENT ? "开发环境" : "生产环境";

            // 寻找Docker安装程序 (支持多种可能的文件名)
            String[] possibleInstallers = {
                    "Docker Desktop Installer.exe",
                    "Docker Desktop.exe",
                    "DockerDesktopInstaller.exe",
                    "docker-desktop-installer.exe"
            };

            File dockerInstaller = null;
            for (String installerName : possibleInstallers) {
                File candidate = new File(basePath, installerName);
                if (candidate.exists()) {
                    dockerInstaller = candidate;
                    break;
                }
            }

            // 寻找Docker Compose文件（在基础目录）
            File dockerCompose = new File(basePath, "docker-compose.exe");
            if (!dockerCompose.exists()) {
                dockerCompose = new File(basePath, "docker-compose");
            }

            // 寻找Docker镜像目录（在基础目录）
            File dockerImages = new File(basePath, "docker-images");

            DockerOfflineFiles files = new DockerOfflineFiles(dockerInstaller, dockerCompose, dockerImages);

            updateStatus("Docker离线文件检查: " + files.getStatus());

            System.out.println("Docker离线文件检查详情 (" + environment + "):");
            System.out.println("  基础目录: " + basePath);
            System.out.println("  安装程序: " + (dockerInstaller != null ? dockerInstaller.getAbsolutePath() : "未找到") +
                    " - " + (files.hasInstaller() ? "存在" : "不存在"));
            System.out.println("  Compose: " + dockerCompose.getAbsolutePath() + " - " + (files.hasCompose() ? "存在" : "不存在"));
            System.out.println("  镜像目录: " + dockerImages.getAbsolutePath() + " - " + (files.hasImages() ? "存在" : "不存在"));

            return files;

        } catch (Exception e) {
            System.err.println("检查Docker离线文件时发生错误: " + e.getMessage());
            return new DockerOfflineFiles(null, null, null);
        }
    }

    /**
     * 离线安装Docker
     */
    private static void installDockerOffline(DockerOfflineFiles files) {
        try {
            updateStatus("开始Docker离线安装...");
            updateProgress(35);

            // 第一阶段：安装Docker Desktop
            updateStatus("正在安装Docker Desktop...");
            updateProgress(40);

            if (!installDockerDesktop(files.getDockerInstaller())) {
                updateStatus("Docker Desktop安装失败");
                showErrorDialog("安装失败", "Docker Desktop安装失败，请检查安装程序是否完整");
                return;
            }

            // 第二阶段：配置Docker设置（禁用自动启动）
            updateStatus("正在配置Docker设置...");
            updateProgress(45);
            configureDockerSettings();

            // 第三阶段：等待Docker服务启动
            updateStatus("等待Docker服务启动...");
            updateProgress(50);

            if (!startDockerServices()) {
                updateStatus("Docker服务启动超时");
                showDockerServiceDialog();
                return;
            }

            // 第四阶段：安装Docker Compose（如果有）
            if (files.hasCompose()) {
                updateStatus("正在配置Docker Compose...");
                updateProgress(60);
                installDockerCompose(files.getDockerComposeFile());
            }

            // 第五阶段：检查和加载Docker镜像
            if (!checkDockerImages()) {
                updateStatus("正在检查和加载Docker镜像...");
                updateProgress(65);
                loadDockerImagesFromFiles();
            } else {
                updateStatus("Docker镜像已齐全");
                updateProgress(65);
            }

            // 第六阶段：启动Docker Compose服务
            updateStatus("正在启动Docker Compose服务...");
            updateProgress(68);
            startDockerComposeServices();

            // 第七阶段：验证安装和容器状态
            updateStatus("正在验证Docker安装和容器状态...");
            updateProgress(70);

            if (verifyDockerInstallation() && verifyDockerComposeServices()) {
                updateStatus("Docker安装并配置完成！");
                updateProgress(70);
            } else {
                updateStatus("Docker安装完成但存在问题");
                showDockerTroubleshootDialog();
            }

        } catch (Exception e) {
            System.err.println("离线安装Docker时发生错误: " + e.getMessage());
            updateStatus("Docker安装失败: " + e.getMessage());
            showErrorDialog("安装失败", "安装过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 安装Docker Desktop
     */
    private static boolean installDockerDesktop(File installerFile) {
        try {
            String installPath = getDockerInstallPath();
            String environment = EnvironmentConfig.IS_DEVELOPMENT ? "开发环境" : "生产环境";

            System.out.println("Docker安装配置 (" + environment + "):");
            System.out.println("  安装程序: " + installerFile.getAbsolutePath());
            System.out.println("  安装路径: " + installPath);

            // 使用静默安装参数，禁用自动启动
            ProcessBuilder pb = new ProcessBuilder(
                    installerFile.getAbsolutePath(),
                    "install",
                    "--quiet",
                    "--accept-license",
                    "--no-windows-containers",
                    "--backend=wsl-2",
                    "--installation-dir=" + installPath,
                    "--no-desktop-shortcut"
            );

            Process process = pb.start();

            // 监控安装进度
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));

            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println("Docker安装: " + line);
                if (line.contains("Installing")) {
                    updateStatus("正在安装Docker组件...");
                } else if (line.contains("Configuring")) {
                    updateStatus("正在配置Docker设置...");
                }
            }

            while ((line = errorReader.readLine()) != null) {
                System.err.println("Docker安装警告: " + line);
            }

            int exitCode = process.waitFor();

            if (exitCode == 0) {
                System.out.println("Docker Desktop安装成功");
                return true;
            } else {
                System.err.println("Docker Desktop安装失败，退出码: " + exitCode);
                return false;
            }

        } catch (Exception e) {
            System.err.println("安装Docker Desktop时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 配置Docker设置（禁用自动启动）
     */
    private static void configureDockerSettings() {
        System.out.println("正在配置Docker核心设置 (基于 settings-store.json)...");
        try {
            // 步骤1: 使用Java代码定位%APPDATA%\Docker\settings-store.json
            String appData = System.getenv("APPDATA");
            if (appData == null || appData.isEmpty()) {
                System.err.println("  - 错误：无法找到APPDATA环境变量。跳过Docker配置。");
                return;
            }
            Path settingsPath = Paths.get(appData, "Docker", "settings-store.json");
            System.out.println("  - 目标配置文件: " + settingsPath);

            // 确保Docker配置目录存在
            Files.createDirectories(settingsPath.getParent());

            // 步骤2: 读取现有JSON，如果文件不存在、为空或无效，则创建一个新的JSON对象
            JSONObject settings;
            if (Files.exists(settingsPath) && Files.size(settingsPath) > 0) {
                try {
                    String content = new String(Files.readAllBytes(settingsPath), StandardCharsets.UTF_8);
                    // 容错处理：如果内容是空的或无效的JSON，则创建一个新对象
                    if (content.trim().isEmpty() || !content.trim().startsWith("{")) {
                        settings = new JSONObject();
                    } else {
                        settings = JSONUtil.parseObj(content);
                    }
                } catch (Exception e) {
                    System.err.println("  - 解析现有settings-store.json失败，将创建一个新的。错误: " + e.getMessage());
                    settings = new JSONObject(); // 解析失败则创建一个新的JSON对象
                }
            } else {
                System.out.println("  - 配置文件不存在或为空，将创建新的配置。");
                settings = new JSONObject();
            }

            // 步骤3: 应用正确的配置值 (使用Hutool-json，它会自动处理键不存在的情况)
            settings.set("AutoStart", false);
            settings.set("DisplayedOnboarding", true);
            settings.set("EnableCLIHints", true);
            settings.set("EnableDockerAI", false);
            settings.set("OpenUIOnStartupDisabled", true);
            settings.set("UseBackgroundIndexing", false);

            // 步骤4: 将修改后的配置写回文件，使用格式化输出
            Files.write(settingsPath, JSONUtil.toJsonPrettyStr(settings).getBytes(StandardCharsets.UTF_8));
            System.out.println("  - 成功应用Docker配置: AutoStart=false, OpenUIOnStartupDisabled=true");

        } catch (Exception e) {
            System.err.println("配置Docker设置时发生严重错误: " + e.getMessage());
            e.printStackTrace();
        }
        System.out.println("Docker核心设置配置完成。");
    }

    /**
     * 启动Docker服务
     */
    private static boolean startDockerServices() {
        try {
            updateStatus("正在启动Docker服务...");

            // 方法1：尝试启动Docker Desktop（后台启动到系统托盘）
            String dockerDesktopPath = findDockerDesktopPath();
            if (dockerDesktopPath != null) {
                // 使用--background参数确保后台启动，不显示窗口
                ProcessBuilder pb = new ProcessBuilder(dockerDesktopPath, "--background");
                pb.environment().put("DOCKER_DESKTOP_STARTUP_MODE", "background");
                Process process = pb.start();
                process.waitFor(5, TimeUnit.SECONDS);
                System.out.println("Docker Desktop已后台启动到系统托盘");
            }

            // 方法2：通过服务管理器启动Docker服务
            ProcessBuilder pb = new ProcessBuilder(
                    "powershell", "-Command",
                    "Start-Service -Name 'com.docker.service' -ErrorAction SilentlyContinue"
            );
            Process process = pb.start();
            process.waitFor(10, TimeUnit.SECONDS);

            // 等待服务启动
            return waitForDockerReady();

        } catch (Exception e) {
            System.err.println("启动Docker服务时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 寻找Docker Desktop路径
     */
    private static String findDockerDesktopPath() {
        String basePath = EnvironmentConfig.getBasePath();
        String[] possiblePaths = {
                basePath + "\\Docker\\Docker Desktop.exe"
        };

        for (String path : possiblePaths) {
            if (path != null && new File(path).exists()) {
                return path;
            }
        }

        return null;
    }

    /**
     * 获取Docker可执行文件的完整路径
     *
     * @return Docker可执行文件路径，如果未找到则返回"docker"
     */
    private static String getDockerExecutablePath() {
        // 尝试常见的Docker安装位置
        String[] possiblePaths = {
                "C:\\Program Files\\Docker\\Docker\\resources\\bin\\docker.exe",
                "C:\\Program Files (x86)\\Docker\\Docker\\resources\\bin\\docker.exe",
                EnvironmentConfig.getBasePath() + "\\Docker\\resources\\bin\\docker.exe",
                EnvironmentConfig.getBasePath() + "\\Docker\\docker.exe"
        };

        for (String path : possiblePaths) {
            if (new File(path).exists()) {
                System.out.println("找到Docker可执行文件: " + path);
                return path;
            }
        }

        // 如果都没找到，返回默认命令（依赖环境变量）
        System.out.println("未找到Docker可执行文件，使用默认命令");
        return "docker";
    }

    /**
     * 等待Docker准备就绪
     */
    private static boolean waitForDockerReady() {
        updateStatus("等待Docker启动...");

        int maxAttempts = 60; // 最多等待60次，每次2秒
        int attempt = 0;

        while (attempt < maxAttempts) {
            if (isDockerRunning()) {
                updateStatus("Docker启动成功");
                return true;
            }

            try {
                Thread.sleep(2000); // 等待2秒
                attempt++;

                int progress = 45 + (attempt * 10 / maxAttempts);
                updateProgress(progress);

                updateStatus("等待Docker启动... (" + attempt + "/" + maxAttempts + ")");

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        updateStatus("Docker启动超时");
        return false;
    }

    /**
     * 安装Docker Compose
     */
    private static void installDockerCompose(File composeFile) {
        try {
            // 将Docker Compose复制到基础目录
            String basePath = EnvironmentConfig.getBasePath();
            String environment = EnvironmentConfig.IS_DEVELOPMENT ? "开发环境" : "生产环境";
            File targetDir = new File(basePath);

            System.out.println("Docker Compose安装配置 (" + environment + "):");
            System.out.println("  源文件: " + composeFile.getAbsolutePath());
            System.out.println("  目标目录: " + basePath);

            if (targetDir.exists()) {
                File targetFile = new File(targetDir, "docker-compose.exe");

                ProcessBuilder pb = new ProcessBuilder(
                        "powershell", "-Command",
                        "Copy-Item '" + composeFile.getAbsolutePath() + "' '" + targetFile.getAbsolutePath() + "'"
                );
                Process process = pb.start();
                int exitCode = process.waitFor();

                if (exitCode == 0) {
                    System.out.println("Docker Compose安装成功");
                } else {
                    System.err.println("Docker Compose安装失败");
                }
            }

        } catch (Exception e) {
            System.err.println("安装Docker Compose时发生错误: " + e.getMessage());
        }
    }


    /**
     * 验证Docker安装
     */
    private static boolean verifyDockerInstallation() {
        try {
            String dockerPath = getDockerExecutablePath();

            // 检查Docker版本
            ProcessBuilder pb = new ProcessBuilder(dockerPath, "--version");
            Process process = pb.start();
            boolean finished = process.waitFor(10, TimeUnit.SECONDS);

            if (!finished || process.exitValue() != 0) {
                return false;
            }

            // 检查Docker运行状态
            if (!isDockerRunning()) {
                return false;
            }

            // 尝试运行简单的容器测试
            pb = new ProcessBuilder(dockerPath, "run", "--rm", "hello-world");
            process = pb.start();
            finished = process.waitFor(30, TimeUnit.SECONDS);

            return finished && process.exitValue() == 0;

        } catch (Exception e) {
            System.err.println("验证Docker安装时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 显示Docker安装选项
     */
    private static void showDockerInstallOptions(DockerOfflineFiles files) {
        SwingUtilities.invokeLater(() -> {
            String message = "Docker离线安装文件检查结果：\n\n" + files.getStatus() + "\n\n";

            if (!files.hasInstaller()) {
                message += "缺少Docker Desktop安装程序\n";
                message += "请下载 Docker Desktop Installer.exe 到项目目录（与exe同一目录）\n";
            }

            message += "\n请选择安装方式：";

            String[] options = {"重新检查文件", "在线下载安装", "手动安装", "跳过Docker安装"};
            int choice = JOptionPane.showOptionDialog(
                    null,
                    message,
                    "Docker安装选项",
                    JOptionPane.YES_NO_CANCEL_OPTION,
                    JOptionPane.QUESTION_MESSAGE,
                    null,
                    options,
                    options[0]
            );

            switch (choice) {
                case 0: // 重新检查
                    CompletableFuture.runAsync(() -> checkAndStartDocker());
                    break;
                case 1: // 在线安装
                    CompletableFuture.runAsync(() -> installDockerOnline());
                    break;
                case 2: // 手动安装
                    showManualInstallDialog();
                    break;
                case 3: // 跳过
                default:
                    updateStatus("跳过Docker安装");
                    break;
            }
        });
    }

    /**
     * 在线安装Docker
     */
    private static void installDockerOnline() {
        try {
            updateStatus("开始Docker在线安装...");
            updateProgress(35);

            // 下载Docker Desktop
            String downloadUrl = "https://desktop.docker.com/win/stable/Docker%20Desktop%20Installer.exe";
            String tempPath = System.getProperty("java.io.tmpdir") + "\\Docker Desktop Installer.exe";

            updateStatus("正在下载Docker Desktop...");
            ProcessBuilder pb = new ProcessBuilder(
                    "powershell", "-Command",
                    "Invoke-WebRequest -Uri '" + downloadUrl + "' -OutFile '" + tempPath + "' -UseBasicParsing"
            );
            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode != 0) {
                updateStatus("Docker下载失败");
                showErrorDialog("下载失败", "无法下载Docker Desktop，请检查网络连接");
                return;
            }

            updateProgress(45);

            // 安装Docker
            File installerFile = new File(tempPath);
            DockerOfflineFiles files = new DockerOfflineFiles(installerFile, null, null);
            installDockerOffline(files);

            // 清理临时文件
            installerFile.delete();

        } catch (Exception e) {
            System.err.println("在线安装Docker时发生错误: " + e.getMessage());
            updateStatus("Docker在线安装失败: " + e.getMessage());
            showErrorDialog("在线安装失败", "安装过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 显示手动安装对话框
     */
    private static void showManualInstallDialog() {
        SwingUtilities.invokeLater(() -> {
            String message = "手动安装Docker Desktop步骤：\n\n" +
                    "1. 访问 https://www.docker.com/products/docker-desktop\n" +
                    "2. 下载 Docker Desktop for Windows\n" +
                    "3. 运行安装程序并按照提示完成安装\n" +
                    "4. 重启计算机（如果需要）\n" +
                    "5. 启动Docker Desktop\n\n" +
                    "安装完成后，点击'重新检查'验证安装。";

            String[] options = {"打开下载页面", "重新检查", "关闭"};
            int choice = JOptionPane.showOptionDialog(
                    null,
                    message,
                    "手动安装Docker",
                    JOptionPane.YES_NO_CANCEL_OPTION,
                    JOptionPane.INFORMATION_MESSAGE,
                    null,
                    options,
                    options[1]
            );

            switch (choice) {
                case 0: // 打开下载页面
                    try {
                        Desktop.getDesktop().browse(new java.net.URI("https://www.docker.com/products/docker-desktop"));
                    } catch (Exception e) {
                        System.err.println("无法打开浏览器: " + e.getMessage());
                    }
                    break;
                case 1: // 重新检查
                    CompletableFuture.runAsync(() -> checkAndStartDocker());
                    break;
                case 2: // 关闭
                default:
                    break;
            }
        });
    }

    /**
     * 显示Docker服务对话框
     */
    private static void showDockerServiceDialog() {
        SwingUtilities.invokeLater(() -> {
            String message = "Docker安装完成，但服务启动超时。\n\n" +
                    "可能的原因：\n" +
                    "• WSL2未正确安装或配置\n" +
                    "• 系统需要重启以完成安装\n" +
                    "• 虚拟化功能未启用\n\n" +
                    "建议的解决方案：";

            String[] options = {"重启计算机", "手动启动Docker", "重试检测", "跳过"};
            int choice = JOptionPane.showOptionDialog(
                    null,
                    message,
                    "Docker服务启动问题",
                    JOptionPane.YES_NO_CANCEL_OPTION,
                    JOptionPane.WARNING_MESSAGE,
                    null,
                    options,
                    options[0]
            );

            switch (choice) {
                case 0: // 重启计算机
                    showRestartConfirmDialog();
                    break;
                case 1: // 手动启动
                    manualStartDocker();
                    break;
                case 2: // 重试
                    CompletableFuture.runAsync(() -> checkAndStartDocker());
                    break;
                case 3: // 跳过
                default:
                    updateStatus("跳过Docker启动");
                    break;
            }
        });
    }

    /**
     * 显示重启确认对话框
     */
    private static void showRestartConfirmDialog() {
        SwingUtilities.invokeLater(() -> {
            int choice = JOptionPane.showConfirmDialog(
                    null,
                    "重启计算机有助于完成Docker安装和WSL2配置。\n\n是否现在重启？",
                    "重启确认",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.QUESTION_MESSAGE
            );

            if (choice == JOptionPane.YES_OPTION) {
                try {
                    ProcessBuilder pb = new ProcessBuilder("shutdown", "/r", "/t", "10", "/c", "Docker安装完成，系统将在10秒后重启");
                    pb.start();
                } catch (Exception e) {
                    showErrorDialog("重启失败", "无法执行重启命令，请手动重启计算机");
                }
            }
        });
    }

    /**
     * 手动启动Docker
     */
    private static void manualStartDocker() {
        try {
            String dockerPath = findDockerDesktopPath();
            if (dockerPath != null) {
                ProcessBuilder pb = new ProcessBuilder(dockerPath);
                pb.start();

                updateStatus("正在手动启动Docker Desktop...");

                // 等待一段时间后重新检查
                CompletableFuture.runAsync(() -> {
                    try {
                        Thread.sleep(10000); // 等待10秒
                        if (isDockerRunning()) {
                            updateStatus("Docker手动启动成功");
                            updateProgress(70);
                        } else {
                            updateStatus("Docker手动启动失败");
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                });
            } else {
                showErrorDialog("启动失败", "找不到Docker Desktop安装路径");
            }
        } catch (Exception e) {
            showErrorDialog("启动失败", "无法启动Docker Desktop: " + e.getMessage());
        }
    }

    /**
     * 显示Docker故障排除对话框
     */
    private static void showDockerTroubleshootDialog() {
        SwingUtilities.invokeLater(() -> {
            String message = "Docker安装或启动存在问题。\n\n" +
                    "常见解决方案：\n" +
                    "1. 检查WSL2是否正确安装\n" +
                    "2. 确保虚拟化功能已启用\n" +
                    "3. 重启Docker Desktop\n" +
                    "4. 重启计算机\n\n" +
                    "是否查看详细的故障排除信息？";

            int choice = JOptionPane.showConfirmDialog(
                    null,
                    message,
                    "Docker故障排除",
                    JOptionPane.YES_NO_OPTION,
                    JOptionPane.WARNING_MESSAGE
            );

            if (choice == JOptionPane.YES_OPTION) {
                showDetailedTroubleshootDialog();
            }
        });
    }

    /**
     * 显示详细故障排除对话框
     */
    private static void showDetailedTroubleshootDialog() {
        SwingUtilities.invokeLater(() -> {
            String message = "详细故障排除步骤：\n\n" +
                    "WSL2相关：\n" +
                    "• 运行 'wsl --list --verbose' 检查WSL状态\n" +
                    "• 确保默认WSL版本为2\n" +
                    "• 如有问题，重新安装WSL2内核\n\n" +
                    "Docker相关：\n" +
                    "• 检查Windows功能中的容器功能是否启用\n" +
                    "• 查看Docker Desktop设置中的WSL2引擎配置\n" +
                    "• 尝试重置Docker Desktop设置\n\n" +
                    "系统相关：\n" +
                    "• 确保BIOS中虚拟化功能已启用\n" +
                    "• 检查Windows版本是否支持WSL2\n" +
                    "• 临时关闭防病毒软件重试";

            JTextArea textArea = new JTextArea(message);
            textArea.setEditable(false);
            textArea.setRows(15);
            textArea.setColumns(50);

            JScrollPane scrollPane = new JScrollPane(textArea);

            JOptionPane.showMessageDialog(
                    null,
                    scrollPane,
                    "Docker详细故障排除",
                    JOptionPane.INFORMATION_MESSAGE
            );
        });
    }

    /**
     * 停止Docker
     */
    public static void stopDocker() {
        try {
            System.out.println("正在停止Docker服务...");
            updateStatus("正在停止Docker...");


            // 第四步：停止Docker Desktop进程
            try {
                System.out.println("停止Docker Desktop进程...");

                // 尝试优雅关闭
                ProcessBuilder pb1 = new ProcessBuilder("taskkill", "/F", "/IM", "Docker Desktop.exe");
                Process process1 = pb1.start();
                process1.waitFor(2, TimeUnit.SECONDS);

                // 同时关闭相关进程
                String[] dockerProcesses = {
                        "dockerd.exe",
                        "docker.exe",
                        "containerd.exe",
                        "vpnkit.exe",
                        "com.docker.build.exe",
                        "com.docker.backend.exe"
                };

                for (String processName : dockerProcesses) {
                    try {
                        ProcessBuilder pb = new ProcessBuilder("taskkill", "/F", "/IM", processName);
                        Process process = pb.start();
                        process.waitFor(2, TimeUnit.SECONDS);
                    } catch (Exception e) {
                        // 忽略单个进程关闭失败
                    }
                }

                System.out.println("Docker Desktop进程已停止");

            } catch (Exception e) {
                System.err.println("停止Docker Desktop进程时出错: " + e.getMessage());
            }


            updateStatus("Docker已停止");
            System.out.println("Docker停止流程完成");

        } catch (Exception e) {
            System.err.println("停止Docker时出错: " + e.getMessage());
            updateStatus("Docker停止时出错: " + e.getMessage());
        }
    }

    /**
     * 获取Docker版本信息
     */
    public static String getDockerVersion() {
        try {
            String dockerPath = getDockerExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(dockerPath, "--version");
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String version = reader.readLine();

            process.waitFor();
            return version != null ? version : "未知版本";
        } catch (Exception e) {
            return "获取版本失败: " + e.getMessage();
        }
    }

    /**
     * 获取Docker容器数量
     */
    public static int getContainerCount() {
        try {
            String dockerPath = getDockerExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(dockerPath, "ps", "-q");
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            int count = 0;
            while (reader.readLine() != null) {
                count++;
            }

            process.waitFor();
            return count;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * Docker状态信息类
     */
    public static class DockerStatus {
        private boolean installed;
        private boolean running;
        private String version;
        private int containerCount;

        public DockerStatus(boolean installed, boolean running, String version, int containerCount) {
            this.installed = installed;
            this.running = running;
            this.version = version;
            this.containerCount = containerCount;
        }

        public boolean isInstalled() {
            return installed;
        }

        public boolean isRunning() {
            return running;
        }

        public String getVersion() {
            return version;
        }

        public int getContainerCount() {
            return containerCount;
        }
    }

    /**
     * 获取Docker完整状态
     */
    public static DockerStatus getDockerStatus() {
        boolean installed = isDockerInstalled();
        boolean running = installed && isDockerRunning();
        String version = installed ? getDockerVersion() : "未安装";
        int containerCount = running ? getContainerCount() : 0;

        return new DockerStatus(installed, running, version, containerCount);
    }

    /**
     * 显示错误对话框
     */
    private static void showErrorDialog(String title, String message) {
        SwingUtilities.invokeLater(() -> {
            JOptionPane.showMessageDialog(
                    null,
                    message,
                    title,
                    JOptionPane.ERROR_MESSAGE
            );
        });
    }

    /**
     * 更新状态标签
     */
    private static void updateStatus(String message) {
        SwingUtilities.invokeLater(() -> {
            if (statusLabel != null) {
                statusLabel.setText(message);
            }
        });
        System.out.println("Docker状态: " + message);
    }

    /**
     * 更新进度条
     */
    private static void updateProgress(int value) {
        SwingUtilities.invokeLater(() -> {
            if (progressBar != null) {
                progressBar.setValue(value);
                progressBar.setString(value + "%");
            }
        });
    }

    /**
     * 启动Docker Compose服务
     */
    private static void startDockerComposeServices() {
        try {
            String basePath = EnvironmentConfig.getBasePath();
            String composeFile = basePath + "\\docker-compose.yml";

            if (!new File(composeFile).exists()) {
                System.err.println("Docker Compose文件不存在: " + composeFile);
                return;
            }

            // 设置环境变量 - data目录在基础目录下
            String dataPath = basePath + "\\data";
            String dockerPath = getDockerExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(
                    dockerPath, "compose", "-f", composeFile, "up", "-d"
            );

            // 设置环境变量
            pb.environment().put("DATA_PATH", dataPath);
            pb.directory(new File(basePath));

            Process process = pb.start();
            ThreadUtil.sleep(2000);

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println("Docker Compose: " + line);
                if (line.contains("Creating")) {
                    updateStatus("正在创建容器: " + line);
                } else if (line.contains("Starting")) {
                    updateStatus("正在启动容器: " + line);
                }
            }

            int exitCode = process.waitFor();

            if (exitCode == 0) {
                System.out.println("Docker Compose服务启动成功");
                System.out.println("数据存储路径: " + dataPath);
            } else {
                System.err.println("Docker Compose服务启动失败，退出码: " + exitCode);
            }

        } catch (Exception e) {
            System.err.println("启动Docker Compose服务时发生错误: " + e.getMessage());
        }
    }

    /**
     * 验证Docker Compose服务状态
     *
     * @param quickCheck 是否执行快速检查（不等待，只检查当前状态）
     * @return boolean 服务是否全部正常
     */
    private static boolean verifyDockerComposeServices(boolean quickCheck) {
        updateStatus("正在验证Docker Compose服务状态...");
        int maxAttempts = quickCheck ? 1 : 90; // 快速检查只尝试一次，否则最多等待3分钟
        int expectedServices = 4; // mysql, redis, onlyoffice, weaviate

        for (int attempt = 0; attempt < maxAttempts; attempt++) {
            // 如果不是快速检查且是第一次以上的循环，则进行日志提示
            if (!quickCheck && attempt > 0) {
                System.out.println("等待容器启动... 第 " + (attempt + 1) + " 次尝试。");
            }

            try {
                String basePath = EnvironmentConfig.getBasePath();
                String composeFile = basePath + "\\docker-compose.yml";
                if (!new File(composeFile).exists()) {
                    System.err.println("Docker Compose文件不存在: " + composeFile);
                    return false;
                }
                String dockerPath = getDockerExecutablePath();
                ProcessBuilder pb = new ProcessBuilder(
                        dockerPath, "compose", "-f", composeFile, "ps", "--format", "json"
                );
                pb.directory(new File(basePath));
                Process process = pb.start();

                // 读取JSON输出并逐行处理
                int runningServices = 0;
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        if (line.trim().isEmpty() || !line.trim().startsWith("{")) {
                            continue;
                        }
                        try {
                            JSONObject serviceJson = JSONUtil.parseObj(line);
                            String state = serviceJson.getStr("State", "");
                            if (state.toLowerCase().contains("running")) {
                                runningServices++;
                            }
                        } catch (Exception e) {
                            System.err.println("解析单行JSON时出错: " + line + " | 错误: " + e.getMessage());
                        }
                    }
                }
                process.waitFor();

                updateStatus(String.format("正在启动容器... (%d/%d)", runningServices, expectedServices));
                int progress = 70 + (int) ((double) runningServices / expectedServices * 20);
                updateProgress(progress);

                if (runningServices >= expectedServices) {
                    System.out.println("所有Docker Compose服务已成功启动！");
                    updateStatus("所有容器均已成功启动！");
                    updateProgress(90);
                    return true;
                }

                // 如果是快速检查，则在此处直接返回失败，不进行等待
                if (quickCheck) {
                    System.out.println("快速检查失败：当前运行的服务容器数量 (" + runningServices + ") 未达到预期 (" + expectedServices + ")。");
                    return false;
                }

                // 等待2秒再重试
                Thread.sleep(2000);

            } catch (Exception e) {
                System.err.println("验证Docker Compose服务时发生错误: " + e.getMessage());
                // 出现异常时短暂等待后重试
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }

        System.err.println("Docker Compose服务启动超时。");
        updateStatus("错误：部分或全部容器启动超时！");
        return false;
    }

    /**
     * 验证Docker Compose服务状态
     */
    private static boolean verifyDockerComposeServices() {
        // 默认调用非快速检查模式
        return verifyDockerComposeServices(false);
    }

    /**
     * 检查Docker镜像是否存在
     */
    private static boolean checkDockerImages() {
        try {
            updateStatus("正在检查Docker镜像...");

            String[] requiredImages = {
                    "redis:7.2",
                    "mysql:8.0",
                    "semitechnologies/weaviate:1.30.4",
                    "onlyoffice/documentserver:8.3.3"
            };

            int existingCount = 0;
            for (String image : requiredImages) {
                if (isDockerImageExists(image)) {
                    System.out.println("镜像已存在: " + image);
                    existingCount++;
                } else {
                    System.out.println("镜像不存在: " + image);
                }
            }

            System.out.println("Docker镜像检查完成: " + existingCount + "/" + requiredImages.length + " 个镜像存在");
            return existingCount == requiredImages.length;

        } catch (Exception e) {
            System.err.println("检查Docker镜像时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查指定Docker镜像是否存在
     */
    private static boolean isDockerImageExists(String imageName) {
        try {
            String dockerPath = getDockerExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(dockerPath, "images", imageName, "--format", "table {{.Repository}}:{{.Tag}}");
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;

            while ((line = reader.readLine()) != null) {
                if (line.trim().equals(imageName)) {
                    return true;
                }
            }

            process.waitFor(10, TimeUnit.SECONDS);
            return false;

        } catch (Exception e) {
            System.err.println("检查Docker镜像 " + imageName + " 时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 从离线文件加载Docker镜像
     */
    private static void loadDockerImagesFromFiles() {
        try {
            String basePath = EnvironmentConfig.getBasePath();

            // 定义镜像文件映射，与install_docker.bat保持一致
            String[][] imageFiles = {
                    {"redis_7.2.tar", "redis:7.2"},
                    {"mysql_8.0.tar", "mysql:8.0"},
                    {"cr.weaviate.io_semitechnologies_weaviate_1.30.4.tar", "cr.weaviate.io/semitechnologies/weaviate:1.30.4"},
                    {"onlyoffice_documentserver_8.3.3.tar", "onlyoffice/documentserver:8.3.3"}
            };

            updateStatus("正在检查并加载Docker镜像...");

            for (String[] imageMapping : imageFiles) {
                String fileName = imageMapping[0];
                String imageName = imageMapping[1];

                File imageFile = new File(basePath, fileName);

                if (imageFile.exists()) {
                    // 检查镜像是否已存在
                    if (isDockerImageExists(imageName)) {
                        System.out.println("镜像 " + imageName + " 已存在，跳过加载");
                        updateStatus("镜像 " + imageName + " 已存在，跳过加载");
                    } else {
                        System.out.println("正在加载镜像: " + imageName + " 从文件: " + fileName);
                        updateStatus("正在加载镜像: " + imageName);

                        if (loadDockerImageFromFile(imageFile, imageName)) {
                            System.out.println("镜像 " + imageName + " 加载成功");
                        } else {
                            System.err.println("镜像 " + imageName + " 加载失败");
                        }
                    }
                } else {
                    System.out.println("警告: 镜像文件不存在: " + imageFile.getAbsolutePath());
                }
            }

            updateStatus("Docker镜像加载完成");

        } catch (Exception e) {
            System.err.println("加载Docker镜像时出错: " + e.getMessage());
        }
    }

    /**
     * 从文件加载单个Docker镜像
     */
    private static boolean loadDockerImageFromFile(File imageFile, String imageName) {
        try {
            String dockerPath = getDockerExecutablePath();
            ProcessBuilder pb = new ProcessBuilder(dockerPath, "load", "-i", imageFile.getAbsolutePath());
            Process process = pb.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));

            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println("Docker加载: " + line);
            }

            while ((line = errorReader.readLine()) != null) {
                System.err.println("Docker加载错误: " + line);
            }

            int exitCode = process.waitFor();
            return exitCode == 0;

        } catch (Exception e) {
            System.err.println("加载镜像文件 " + imageFile.getName() + " 时出错: " + e.getMessage());
            return false;
        }
    }
}