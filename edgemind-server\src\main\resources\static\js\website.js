// 同步文档按钮功能
function initSyncButton() {
    const syncBtn = document.getElementById('syncDocsBtn');
    if (syncBtn) {
        syncBtn.addEventListener('click', function() {
            // 这里可以添加同步文档的逻辑
        });
    }
}

// 文档树折叠展开功能
function initDocumentTree() {
    // 为所有切换按钮添加点击事件
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('tree-toggle')) {
            const treeItem = e.target.closest('.tree-item');
            const treeChildren = treeItem.querySelector('.tree-children');

            if (treeChildren) {
                // 切换展开/折叠状态
                if (treeItem.classList.contains('expanded')) {
                    treeItem.classList.remove('expanded');
                    treeItem.classList.add('collapsed');
                    e.target.classList.remove('expanded');
                    e.target.classList.add('collapsed');
                } else {
                    treeItem.classList.remove('collapsed');
                    treeItem.classList.add('expanded');
                    e.target.classList.remove('collapsed');
                    e.target.classList.add('expanded');
                }
            }
        }
    });

    // 初始化所有有子节点的项目
    document.querySelectorAll('.tree-node.has-children').forEach(node => {
        const toggle = node.querySelector('.tree-toggle');
        const treeItem = node.closest('.tree-item');

        if (treeItem.classList.contains('expanded')) {
            toggle.classList.add('expanded');
        } else {
            toggle.classList.add('collapsed');
        }
    });
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有功能
    initChatDemo();
    initModelSelector();
    initDocTree();
    initScrollEffects();
    initContactForm();
    initSyncButton();
    initDocumentTree();
});

// 聊天演示功能
function initChatDemo() {
    const chatMessages = document.getElementById('chatMessages');
    const chatInput = document.getElementById('chatInput');
    const sendBtn = document.getElementById('sendBtn');

    if (!chatMessages || !chatInput || !sendBtn) return;

    // 预设的对话内容
    const conversations = [
        {
            user: "EdgeMind支持哪些文档格式？",
            ai: "EdgeMind支持多种文档格式，包括PDF、Word、Excel、PowerPoint、Markdown、TXT等常见格式。"
        },
        {
            user: "安装复杂吗？需要配置环境吗？",
            ai: "完全不用担心！我们提供一键安装包，无需翻墙，无需自己解决环境问题。双击安装包即可完成部署，浏览器打开即可使用，无需重复安装。"
        },
        {
            user: "如何将本地文档导入AI助手？",
            ai: "非常简单！我们提供一键同步功能，可以将本地磁盘的文档数据一键同步到AI助手中。支持Office格式全兼容，包括Word、Excel、PowerPoint等。"
        },
        {
            user: "如何快速找到文档中的特定内容？",
            ai: "我们独有的文档树功能可以实现每个节点对话，包括文档和文件夹都能精准定位。点击任意文档节点即可开始针对性对话。"
        },
        {
            user: "AI模型下载很麻烦，不懂怎么配置？",
            ai: "完全不用担心！内置大模型中心提供一键下载，支持Google Gemma、DeepSeek、Qwen、Llama等主流模型。下载完成后即可离线运行，无需复杂配置。"
        },
        {
            user: "需要安装客户端软件吗？",
            ai: "不需要！安装完成后直接用浏览器打开即可使用，无需安装额外的客户端软件。支持局域网互联互通，只需一台服务器，团队成员都能通过浏览器访问。"
        },
        {
            user: "数据安全有保障吗？",
            ai: "绝对安全！文档始终存放在您的本地磁盘，与AI助手完全分离。即使迁移或备份，您的原始文档依然在本地，数据完全由您掌控。"
        }
    ];

    let currentConversation = 0;
    let isTyping = false;

    // 自动演示对话
    function startAutoDemo() {
        if (currentConversation >= conversations.length) {
            currentConversation = 0;
            // 清空聊天记录，重新开始
            setTimeout(() => {
                chatMessages.innerHTML = `
                    <div class="message ai-message">
                        <div class="message-avatar">
                            <i class="bi bi-robot"></i>
                        </div>
                        <div class="message-content">
                            您好！我是端智AI助手，有什么可以帮您的吗？
                        </div>
                    </div>
                `;
                setTimeout(startAutoDemo, 1500);
            }, 1000);
            return;
        }

        const conv = conversations[currentConversation];

        // 显示用户消息
        setTimeout(() => {
            addMessage(conv.user, 'user');

            // 显示AI正在输入
            setTimeout(() => {
                showTypingIndicator();

                // 显示AI回复
                setTimeout(() => {
                    hideTypingIndicator();
                    addMessage(conv.ai, 'ai');
                    currentConversation++;

                    // 继续下一轮对话
                    setTimeout(startAutoDemo, 2000);
                }, 800);
            }, 500);
        }, 1000);
    }

    // 添加消息
    function addMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;

        const avatar = type === 'ai' ? '<i class="bi bi-robot"></i>' : '<i class="bi bi-person-fill"></i>';

        messageDiv.innerHTML = `
            <div class="message-avatar">
                ${avatar}
            </div>
            <div class="message-content">
                ${content}
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // 显示输入指示器
    function showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai-message typing-indicator';
        typingDiv.innerHTML = `
            <div class="message-avatar">
                <i class="bi bi-robot"></i>
            </div>
            <div class="message-content">
                <div class="typing-dots">
                    <span></span><span></span><span></span>
                </div>
            </div>
        `;

        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // 添加输入动画样式
        const style = document.createElement('style');
        style.textContent = `
            .typing-dots {
                display: flex;
                gap: 4px;
                align-items: center;
            }
            .typing-dots span {
                width: 6px;
                height: 6px;
                background: #64748b;
                border-radius: 50%;
                animation: typing 1.4s infinite;
            }
            .typing-dots span:nth-child(2) {
                animation-delay: 0.2s;
            }
            .typing-dots span:nth-child(3) {
                animation-delay: 0.4s;
            }
            @keyframes typing {
                0%, 60%, 100% { opacity: 0.3; }
                30% { opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    // 隐藏输入指示器
    function hideTypingIndicator() {
        const typingIndicator = chatMessages.querySelector('.typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    // 手动发送消息
    function sendMessage() {
        const message = chatInput.value.trim();
        if (message && !isTyping) {
            addMessage(message, 'user');
            chatInput.value = '';

            // 模拟AI回复
            isTyping = true;
            setTimeout(() => {
                showTypingIndicator();
                setTimeout(() => {
                    hideTypingIndicator();
                    addMessage('感谢您的提问！这是一个演示系统，实际使用时AI会根据您的知识库内容提供准确回答。', 'ai');
                    isTyping = false;
                }, 1500);
            }, 500);
        }
    }

    // 绑定事件
    sendBtn.addEventListener('click', sendMessage);
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });

    // 开始自动演示
    setTimeout(startAutoDemo, 3000);
}

// 模型选择器功能
function initModelSelector() {
    const modelItems = document.querySelectorAll('.model-item');
    const thinkingModeToggle = document.getElementById('thinkingMode');

    // 模型选择
    modelItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除其他选中状态
            modelItems.forEach(i => i.classList.remove('active'));
            // 添加选中状态
            this.classList.add('active');

            // 模拟选择效果
            const modelName = this.querySelector('.model-name').textContent;
            console.log(`已选择模型: ${modelName}`);
        });
    });

    // 深度思考模式切换
    if (thinkingModeToggle) {
        thinkingModeToggle.addEventListener('change', function() {
            const isEnabled = this.checked;
            console.log(`深度思考模式: ${isEnabled ? '已启用' : '已禁用'}`);

            // 添加视觉反馈
            const label = this.nextElementSibling;
            if (isEnabled) {
                label.style.color = '#2563eb';
                label.style.fontWeight = '600';
            } else {
                label.style.color = '';
                label.style.fontWeight = '';
            }
        });
    }
}

// 文档树功能
function initDocTree() {
    const treeNodes = document.querySelectorAll('.tree-node');
    const chatBtns = document.querySelectorAll('.chat-btn');

    // 文档树节点点击
    treeNodes.forEach(node => {
        node.addEventListener('click', function(e) {
            // 如果点击的是聊天按钮，不处理展开/收起
            if (e.target.closest('.chat-btn')) {
                return;
            }

            // 阻止冒泡
            e.stopPropagation();

            // 切换展开/收起状态
            const treeItem = this.closest('.tree-item');
            const children = treeItem.querySelector('.tree-children');

            if (children) {
                treeItem.classList.toggle('expanded');
            }

            // 添加选中效果
            treeNodes.forEach(n => n.classList.remove('selected'));
            this.classList.add('selected');

            // 添加选中样式
            const style = document.createElement('style');
            style.textContent = `
                .tree-node.selected {
                    background: #e0f2fe !important;
                    border-left: 3px solid #2563eb;
                }
            `;
            if (!document.querySelector('style[data-tree-style]')) {
                style.setAttribute('data-tree-style', 'true');
                document.head.appendChild(style);
            }
        });
    });

    // 聊天按钮点击事件 - 直接在右侧面板显示对话
    chatBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const node = this.closest('.tree-node');
            const fileName = node.querySelector('span').textContent;

            // 更新聊天面板上下文
            const chatContext = document.getElementById('chatContext');
            if (chatContext) {
                chatContext.textContent = fileName;
            }

            // 在文档聊天面板中添加消息
            const docChatMessages = document.getElementById('docChatMessages');
            if (docChatMessages) {
                // 清空之前的消息
                docChatMessages.innerHTML = `
                    <div class="message ai-message">
                        <div class="message-avatar">
                            <i class="bi bi-robot"></i>
                        </div>
                        <div class="message-content">
                            <p>您好！我已经为您加载了《${fileName}》，请问有什么可以帮助您的吗？</p>
                            <small class="text-muted">刚刚</small>
                        </div>
                    </div>
                `;
            }
        });
    });

    // 文档聊天输入功能
    const docChatInput = document.getElementById('docChatInput');
    const sendDocMessageBtn = document.getElementById('sendDocMessage');
    const docChatMessages = document.getElementById('docChatMessages');

    function sendDocMessage() {
        const message = docChatInput.value.trim();
        if (!message) return;

        // 添加用户消息
        const userMessageDiv = document.createElement('div');
        userMessageDiv.className = 'message user-message';
        userMessageDiv.innerHTML = `
            <div class="message-avatar">
                <i class="bi bi-person-circle"></i>
            </div>
            <div class="message-content">
                <p>${message}</p>
                <small class="text-muted">刚刚</small>
            </div>
        `;
        docChatMessages.appendChild(userMessageDiv);

        // 清空输入框
        docChatInput.value = '';

        // 模拟AI回复
        setTimeout(() => {
            const aiMessageDiv = document.createElement('div');
            aiMessageDiv.className = 'message ai-message';
            aiMessageDiv.innerHTML = `
                <div class="message-avatar">
                    <i class="bi bi-robot"></i>
                </div>
                <div class="message-content">
                    <p>根据文档内容，我为您找到了相关信息。这个问题涉及到文档中的重要条款，建议您仔细阅读相关章节。</p>
                    <small class="text-muted">刚刚</small>
                </div>
            `;
            docChatMessages.appendChild(aiMessageDiv);
            // 滚动到底部 - 使用父容器.chat-panel-body
            const chatPanelBody = docChatMessages.closest('.chat-panel-body');
            if (chatPanelBody) {
                chatPanelBody.scrollTop = chatPanelBody.scrollHeight;
            }
        }, 1000);

        // 滚动到底部 - 使用父容器.chat-panel-body
        const chatPanelBody = docChatMessages.closest('.chat-panel-body');
        if (chatPanelBody) {
            chatPanelBody.scrollTop = chatPanelBody.scrollHeight;
        }
    }

    if (sendDocMessageBtn) {
        sendDocMessageBtn.addEventListener('click', sendDocMessage);
    }

    if (docChatInput) {
        docChatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendDocMessage();
            }
        });
    }

    // 同步按钮功能
    const syncBtn = document.querySelector('.doc-tree-header .btn');
    if (syncBtn) {
        syncBtn.addEventListener('click', function() {
            // 模拟同步过程
            const originalText = this.innerHTML;
            this.innerHTML = '<div class="loading"></div> 同步中...';
            this.disabled = true;

            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
                showToast('文档同步完成！');
            }, 2000);
        });
    }
}

// 滚动效果
function initScrollEffects() {
    // 平滑滚动到锚点
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 80; // 考虑导航栏高度
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // 滚动时的导航栏效果
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 50) {
            navbar.style.backgroundColor = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
        }
    });

    // 元素进入视口时的动画效果
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);

    // 观察需要动画的元素
    const animatedElements = document.querySelectorAll('.advantage-card, .tech-card, .scenario-card, .value-card');
    animatedElements.forEach(el => {
        observer.observe(el);
    });
}

// 联系表单功能
function initContactForm() {
    // 二维码悬停效果
    const qrCode = document.querySelector('.qr-placeholder');
    if (qrCode) {
        qrCode.addEventListener('mouseenter', function() {
            this.style.borderColor = '#2563eb';
            this.style.backgroundColor = '#f0f9ff';
            this.style.transform = 'scale(1.05)';
            this.style.transition = 'all 0.3s ease';
        });

        qrCode.addEventListener('mouseleave', function() {
            this.style.borderColor = '#e2e8f0';
            this.style.backgroundColor = '#f8fafc';
            this.style.transform = 'scale(1)';
        });
    }

    // 联系信息点击复制
    const contactItems = document.querySelectorAll('.contact-item');
    contactItems.forEach(item => {
        item.addEventListener('click', function() {
            const text = this.textContent.trim();
            if (text.includes('400-') || text.includes('@')) {
                // 提取联系方式
                const contactInfo = text.split('：')[1] || text;

                // 复制到剪贴板
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(contactInfo).then(() => {
                        showToast('联系方式已复制到剪贴板');
                    });
                } else {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = contactInfo;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    showToast('联系方式已复制到剪贴板');
                }

                // 添加点击效果
                this.style.backgroundColor = '#e0f2fe';
                setTimeout(() => {
                    this.style.backgroundColor = '';
                }, 200);
            }
        });

        // 添加悬停效果
        item.addEventListener('mouseenter', function() {
            this.style.cursor = 'pointer';
            this.style.backgroundColor = '#f8fafc';
            this.style.borderRadius = '6px';
            this.style.padding = '8px';
            this.style.margin = '-8px';
            this.style.transition = 'all 0.3s ease';
        });

        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
            this.style.padding = '';
            this.style.margin = '';
        });
    });
}

// 显示提示消息
function showToast(message, type = 'success') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `toast-message toast-${type}`;
    toast.textContent = message;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .toast-message {
            position: fixed;
            top: 100px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999;
            font-size: 14px;
            font-weight: 500;
            animation: slideInRight 0.3s ease, slideOutRight 0.3s ease 2.7s;
        }
        .toast-success {
            background: #10b981;
        }
        .toast-info {
            background: #2563eb;
        }
        .toast-warning {
            background: #f59e0b;
        }
        .toast-error {
            background: #ef4444;
        }
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;

    if (!document.querySelector('style[data-toast-style]')) {
        style.setAttribute('data-toast-style', 'true');
        document.head.appendChild(style);
    }

    // 添加到页面
    document.body.appendChild(toast);

    // 3秒后移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 页面性能优化
window.addEventListener('load', function() {
    // 预加载关键资源
    const criticalImages = [
        // 这里可以添加需要预加载的图片路径
    ];

    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });

    // 延迟加载非关键资源
    setTimeout(() => {
        // 这里可以添加延迟加载的功能
    }, 1000);
});

// 错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    // 这里可以添加错误上报逻辑
});

// 导出主要函数供外部使用
window.EdgeMindDemo = {
    showToast,
    debounce,
    throttle
};