<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EdgeMind 管理后台 - 下载统计</title>
    
    <!-- Bootstrap CSS -->
    <link href="/aistudio/css/vendor/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/aistudio/css/vendor/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="/aistudio/css/download-stats.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container-fluid">
            <a class="navbar-brand" href="/aistudio/admin/dashboard">
                <i class="fas fa-brain me-2"></i>EdgeMind 管理后台
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        <span th:if="${currentAdmin}" th:text="${currentAdmin.nickname}">管理员</span>
                        <span th:unless="${currentAdmin}">管理员</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/aistudio/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-2"></i>仪表盘
                        </a></li>
                        <li><a class="dropdown-item" href="/aistudio/admin/download-stats">
                            <i class="fas fa-chart-bar me-2"></i>下载统计
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/aistudio/admin/change-password">
                            <i class="fas fa-key me-2"></i>修改密码
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="logout()">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0"><i class="fas fa-chart-bar me-2"></i>下载统计管理</h2>
            <div>
                <button onclick="exportData()" class="btn btn-success me-2">
                    <i class="fas fa-file-excel me-2"></i>导出数据
                </button>
                <button onclick="refreshTable()" class="btn btn-primary">
                    <i class="fas fa-sync-alt me-2"></i>刷新
                </button>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="row g-4 mb-4">
            <div class="col-xl-3 col-md-6">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <div class="text-primary mb-2">
                            <i class="fas fa-download fa-2x"></i>
                        </div>
                        <h3 class="text-primary mb-1" id="totalCount">0</h3>
                        <p class="text-muted mb-0">总下载次数</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <div class="text-success mb-2">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                        <h3 class="text-success mb-1" id="todayCount">0</h3>
                        <p class="text-muted mb-0">今日下载</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <div class="text-warning mb-2">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                        <h3 class="text-warning mb-1" id="uniqueIps">0</h3>
                        <p class="text-muted mb-0">独立IP</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-md-6">
                <div class="card stat-card h-100">
                    <div class="card-body text-center">
                        <div class="text-info mb-2">
                            <i class="fas fa-file fa-2x"></i>
                        </div>
                        <h3 class="text-info mb-1" id="fileCount">0</h3>
                        <p class="text-muted mb-0">文件类型</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section p-4 mb-4">
            <h5 class="mb-3"><i class="fas fa-filter me-2"></i>数据筛选</h5>
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">日期范围</label>
                    <div class="input-group">
                        <input type="date" class="form-control" id="startDate">
                        <span class="input-group-text">至</span>
                        <input type="date" class="form-control" id="endDate">
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">文件名</label>
                    <input type="text" class="form-control" id="fileName" placeholder="搜索文件名">
                </div>
                <div class="col-md-2">
                    <label class="form-label">IP地址</label>
                    <input type="text" class="form-control" id="ipAddress" placeholder="搜索IP">
                </div>
                <div class="col-md-2">
                    <label class="form-label">下载状态</label>
                    <select class="form-select" id="downloadStatus">
                        <option value="">全部状态</option>
                        <option value="SUCCESS">成功</option>
                        <option value="FAILED">失败</option>
                        <option value="PARTIAL">部分下载</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button onclick="applyFilter()" class="btn btn-primary flex-fill">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                        <button onclick="resetFilter()" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-1"></i>重置
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-container p-4 position-relative">
            <div class="loading-overlay d-none" id="loadingOverlay">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
            
            <div class="table-responsive">
                <table id="downloadTable" class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>文件名</th>
                            <th>文件大小</th>
                            <th>IP地址</th>
                            <th>用户代理</th>
                            <th>来源页面</th>
                            <th>下载时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>下载详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- 详情内容将动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/aistudio/js/vendor/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/aistudio/js/vendor/jquery-3.7.1.min.js"></script>
    <!-- API 工具类 -->
    <script src="/aistudio/js/common/api-utils.js"></script>
    <!-- 表格工具类 -->
    <script src="/aistudio/js/common/table-utils.js"></script>
    
    <script src="/aistudio/js/download-stats.js"></script>
</body>
</html>