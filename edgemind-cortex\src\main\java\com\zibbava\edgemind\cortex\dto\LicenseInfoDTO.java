package com.zibbava.edgemind.cortex.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 许可证信息数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LicenseInfoDTO {

    /**
     * 硬件指纹
     */
    private String hardwareFingerprint;

    /**
     * 系统唯一标识
     */
    private String systemIdentifier;

    /**
     * 许可证类型：trial-试用版，standard-标准版，professional-专业版，enterprise-企业版
     */
    private String licenseType;

    /**
     * 过期时间，null表示永不过期
     */
    private LocalDateTime expireTime;
}
