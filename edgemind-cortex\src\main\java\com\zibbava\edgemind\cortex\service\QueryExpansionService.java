package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.dto.ollama.OllamaChatRequest;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaMessage;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaOptions;
import com.zibbava.edgemind.cortex.util.OllamaUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 查询扩展服务
 * 通过一次性生成子查询和假设文档来扩展原始查询文本
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QueryExpansionService {

    @Value("${ai.model.name:deepseek-r1:7b}")
    private String modelName;

    @Value("${ai.model.baseUrl:http://localhost:11434}")
    private String baseUrl;

    /**
     * 扩展查询文本，生成子查询和假设文档
     */
    public String expandQueryText(String originalQuery, int numSubQueries, int numHypotheticalDocs) {
        if (originalQuery == null || originalQuery.trim().isEmpty()) {
            log.warn("📝 查询扩展失败：原始查询为空");
            return originalQuery;
        }

        try {
            // 构建查询扩展提示词
            String prompt = buildQueryExpansionPrompt(originalQuery, numSubQueries, numHypotheticalDocs);

            // 配置LLM请求（禁用思考模式以提升速度）
            OllamaOptions options = OllamaOptions.builder()
                    .temperature(0.7f)  // 适中的创造性
                    .topP(0.8f)         // 控制多样性
                    .seed(UUID.randomUUID().hashCode()) // 随机种子
                    .build();

            OllamaChatRequest request = OllamaChatRequest.builder()
                    .model(modelName)
                    .messages(Arrays.asList(OllamaMessage.user(prompt)))
                    .options(options)
                    .stream(false)  // 非流式响应
                    .think(false)
                    .build();

            // 执行LLM调用（快速模式）
            log.info("🚀 执行查询扩展（快速模式）：子查询数={}, 假设文档数={}", numSubQueries, numHypotheticalDocs);
            String response = collectStreamResponse(request);

            if (response == null || response.trim().isEmpty()) {
                log.warn("📝 查询扩展响应为空，返回原始查询");
                return originalQuery;
            }

            // 解析并构建扩展查询
            String expandedQuery = buildExpandedQuery(originalQuery, response);

            log.info("✅ 查询扩展完成：原始长度={}, 扩展长度={}",
                    originalQuery.length(), expandedQuery.length());

            return expandedQuery;

        } catch (Exception e) {
            log.error("❌ 查询扩展失败：{}", e.getMessage(), e);
            return originalQuery; // 失败时返回原始查询
        }
    }

    /**
     * 构建组合提示词，一次性生成子查询和假设文档
     */
    private String buildCombinedPrompt(int numSubQueries, int numHypotheticalDocs) {
        StringBuilder promptBuilder = new StringBuilder();
        promptBuilder.append("你是一个专业的查询扩展专家。给定一个用户查询，你需要生成以下内容：\n\n");

        if (numSubQueries > 0) {
            promptBuilder.append(String.format("第一部分：生成 %d 个不同的子查询\n", numSubQueries));
            promptBuilder.append("- 每个子查询应该从不同角度或使用不同词汇表达相同的信息需求\n");
            promptBuilder.append("- 保持原始查询的核心意图\n");
            promptBuilder.append("- 使用不同的表达方式和同义词\n\n");
        }

        if (numHypotheticalDocs > 0) {
            promptBuilder.append(String.format("第二部分：生成 %d 个假设文档片段\n", numHypotheticalDocs));
            promptBuilder.append("- 每个文档片段应该是可能回答该查询的完整段落\n");
            promptBuilder.append("- 使用不同的角度和表达方式\n");
            promptBuilder.append("- 模拟真实文档的写作风格\n\n");
        }

        promptBuilder.append("输出格式要求：\n");
        if (numSubQueries > 0) {
            promptBuilder.append("=== 子查询 ===\n");
            promptBuilder.append("1. [子查询1]\n2. [子查询2]\n...\n\n");
        }
        if (numHypotheticalDocs > 0) {
            promptBuilder.append("=== 假设文档 ===\n");
            promptBuilder.append("1. [假设文档1]\n\n2. [假设文档2]\n...\n");
        }

        return promptBuilder.toString();
    }

    /**
     * 构建查询扩展提示词
     */
    private String buildQueryExpansionPrompt(String originalQuery, int numSubQueries, int numHypotheticalDocs) {
        StringBuilder promptBuilder = new StringBuilder();
        promptBuilder.append("你是一个专业的查询扩展专家。给定一个用户查询，你需要生成以下内容：\n\n");

        if (numSubQueries > 0) {
            promptBuilder.append(String.format("第一部分：生成 %d 个不同的子查询\n", numSubQueries));
            promptBuilder.append("- 每个子查询应该从不同角度或使用不同词汇表达相同的信息需求\n");
            promptBuilder.append("- 保持原始查询的核心意图\n");
            promptBuilder.append("- 使用不同的表达方式和同义词\n\n");
        }

        if (numHypotheticalDocs > 0) {
            promptBuilder.append(String.format("第二部分：生成 %d 个假设文档片段\n", numHypotheticalDocs));
            promptBuilder.append("- 每个文档片段应该是可能回答该查询的完整段落\n");
            promptBuilder.append("- 使用不同的角度和表达方式\n");
            promptBuilder.append("- 模拟真实文档的写作风格\n\n");
        }

        promptBuilder.append("输出格式要求：\n");
        if (numSubQueries > 0) {
            promptBuilder.append("=== 子查询 ===\n");
            promptBuilder.append("1. [子查询1]\n2. [子查询2]\n...\n\n");
        }
        if (numHypotheticalDocs > 0) {
            promptBuilder.append("=== 假设文档 ===\n");
            promptBuilder.append("1. [假设文档1]\n\n2. [假设文档2]\n...\n");
        }

        promptBuilder.append("原查询：").append(originalQuery);

        return promptBuilder.toString();
    }

    /**
     * 收集流式响应的完整内容
     */
    private String collectStreamResponse(OllamaChatRequest chatRequest) {
        StringBuilder responseBuilder = new StringBuilder();

        try {
            OllamaUtils.streamChat(chatRequest)
                    .doOnNext(responseBuilder::append)
                    .doOnError(error -> log.error("流式响应错误: {}", error.getMessage()))
                    .blockLast(); // 等待流式响应完成
        } catch (Exception e) {
            log.error("收集流式响应失败: {}", e.getMessage(), e);
            throw new RuntimeException("LLM 调用失败", e);
        }

        return responseBuilder.toString();
    }

    /**
     * 构建扩展后的查询文本
     */
    private String buildExpandedQuery(String originalQuery, String response) {
        StringBuilder expandedQuery = new StringBuilder();
        expandedQuery.append(originalQuery);

        try {
            // 提取子查询
            List<String> subQueries = extractSubQueries(response);
            if (!subQueries.isEmpty()) {
                expandedQuery.append("\n\n相关查询：");
                for (String subQuery : subQueries) {
                    expandedQuery.append("\n- ").append(subQuery);
                }
            }

            // 提取假设文档
            List<String> hypotheticalDocs = extractHypotheticalDocuments(response);
            if (!hypotheticalDocs.isEmpty()) {
                expandedQuery.append("\n\n相关内容：");
                for (String doc : hypotheticalDocs) {
                    expandedQuery.append("\n").append(doc);
                }
            }

            log.info("查询扩展完成: 生成{}个子查询, {}个假设文档", subQueries.size(), hypotheticalDocs.size());

        } catch (Exception e) {
            log.warn("解析扩展内容失败，使用原始查询: {}", e.getMessage());
        }

        return expandedQuery.toString();
    }

    /**
     * 提取子查询
     */
    private List<String> extractSubQueries(String response) {
        try {
            String subQuerySection = extractSection(response, "=== 子查询 ===", "=== 假设文档 ===");
            if (subQuerySection.isEmpty()) {
                return List.of();
            }

            return Arrays.stream(subQuerySection.split("\n"))
                    .map(String::trim)
                    .filter(line -> line.matches("^\\d+\\.\\s+.+"))
                    .map(line -> line.replaceFirst("^\\d+\\.\\s+", ""))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("提取子查询失败: {}", e.getMessage());
            return List.of();
        }
    }

    /**
     * 提取假设文档
     */
    private List<String> extractHypotheticalDocuments(String response) {
        try {
            String docSection = extractSection(response, "=== 假设文档 ===", null);
            if (docSection.isEmpty()) {
                return List.of();
            }

            // 按编号分割文档
            String[] docs = docSection.split("(?=\\n\\d+\\.)");
            return Arrays.stream(docs)
                    .map(String::trim)
                    .filter(doc -> !doc.isEmpty())
                    .map(doc -> doc.replaceFirst("^\\d+\\.\\s+", ""))
                    .filter(doc -> doc.length() > 30) // 过滤太短的文档
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.warn("提取假设文档失败: {}", e.getMessage());
            return List.of();
        }
    }

    /**
     * 提取指定区间的文本
     */
    private String extractSection(String text, String startMarker, String endMarker) {
        int startIndex = text.indexOf(startMarker);
        if (startIndex == -1) {
            return "";
        }

        startIndex += startMarker.length();

        int endIndex;
        if (endMarker != null) {
            endIndex = text.indexOf(endMarker, startIndex);
            if (endIndex == -1) {
                endIndex = text.length();
            }
        } else {
            endIndex = text.length();
        }

        return text.substring(startIndex, endIndex).trim();
    }
} 