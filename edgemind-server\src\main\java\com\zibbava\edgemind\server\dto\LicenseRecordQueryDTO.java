package com.zibbava.edgemind.server.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 许可证记录查询参数DTO
 */
@Data
public class LicenseRecordQueryDTO {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 许可证类型：1-免费试用，2-管理员生成
     */
    private Integer licenseType;

    /**
     * 状态：0-未激活，1-已激活，2-已过期，3-已禁用
     */
    private Integer status;

    /**
     * 硬件指纹（模糊查询）
     */
    private String hardwareFingerprint;

    /**
     * 系统标识符（模糊查询）
     */
    private String systemIdentifier;

    /**
     * 生成者（模糊查询）
     */
    private String generatedBy;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 排序字段
     */
    private String sortField = "createTime";

    /**
     * 排序方向：asc/desc
     */
    private String sortOrder = "desc";
}