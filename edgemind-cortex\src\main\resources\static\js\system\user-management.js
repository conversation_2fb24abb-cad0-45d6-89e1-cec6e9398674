/**
 * 用户管理页面JavaScript - 使用EdgeMind组件系统
 */

// 全局变量
let currentPage = 1;
let pageSize = 10;
let selectedUserIds = [];

// 获取上下文路径
const CONTEXT_PATH = window.location.pathname.startsWith('/wkg') ? '/wkg' : '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPage();
});

/**
 * 初始化页面
 */
function initPage() {
    loadUserList();
    loadDepartments();
    loadRoles();
    bindEvents();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 搜索表单提交
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadUserList();
    });

    // 全选复选框
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="userCheckbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedUsers();
    });
}

/**
 * 统一的API请求方法 - 使用EdgeMind标准
 */
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include'
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(`${CONTEXT_PATH}${url}`, finalOptions);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.code !== 200) {
            throw new Error(data.message || '操作失败');
        }

        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

/**
 * 显示Toast提示 - 使用EdgeMind组件
 */
function showToast(message, type = 'info') {
    // 检查是否有EdgeMind的showToast函数
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        // 回退到简单的alert
        alert(`${type.toUpperCase()}: ${message}`);
    }
}

/**
 * 加载用户列表
 */
async function loadUserList() {
    try {
        const searchParams = {
            pageNum: currentPage,
            pageSize: pageSize,
            username: document.getElementById('searchUsername').value,
            nickname: document.getElementById('searchNickname').value,
            deptId: document.getElementById('searchDept').value,
            status: document.getElementById('searchStatus').value
        };

        // 移除空值参数
        Object.keys(searchParams).forEach(key => {
            if (!searchParams[key]) {
                delete searchParams[key];
            }
        });

        const queryString = new URLSearchParams(searchParams).toString();
        const data = await apiRequest(`${CONTEXT_PATH}/api/system/user/list?${queryString}`);

        renderUserTable(data.data.records || []);
        renderPagination(data.data);
    } catch (error) {
        showToast('加载用户列表失败: ' + error.message, 'danger');
    }
}

/**
 * 渲染用户表格
 */
function renderUserTable(users) {
    const tbody = document.getElementById('userTableBody');
    tbody.innerHTML = '';

    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input" name="userCheckbox" value="${user.id}" onchange="updateSelectedUsers()">
            </td>
            <td>
                <div class="avatar-placeholder">
                    ${user.avatar ? `<img src="${user.avatar}" class="rounded-circle" width="32" height="32">` : 
                      `<i class="bi bi-person-fill text-muted"></i>`}
                </div>
            </td>
            <td>${user.username}</td>
            <td>${user.nickname || '-'}</td>
            <td>${user.email || '-'}</td>
            <td>${user.phone || '-'}</td>
            <td>${user.deptName || '-'}</td>
            <td>
                <span class="badge status-badge ${user.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${user.status === 1 ? '启用' : '禁用'}
                </span>
                ${user.accountLocked ? '<span class="badge bg-warning ms-1">锁定</span>' : ''}
            </td>
            <td>
                ${user.lastLoginTime ? formatDateTime(user.lastLoginTime) : '-'}
                ${user.lastLoginIp ? `<br><small class="text-muted">${user.lastLoginIp}</small>` : ''}
            </td>
            <td>${formatDateTime(user.createTime)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="editUser(${user.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="viewUserRoles(${user.id})" title="角色">
                        <i class="bi bi-people"></i>
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="showResetPasswordModal(${user.id}, '${user.username}')" title="重置密码">
                        <i class="bi bi-key"></i>
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="showChangePasswordModal(${user.id}, '${user.username}')" title="修改密码">
                        <i class="bi bi-shield-lock"></i>
                    </button>
                    ${user.accountLocked ? 
                        `<button type="button" class="btn btn-outline-success" onclick="unlockUser(${user.id})" title="解锁">
                            <i class="bi bi-unlock"></i>
                        </button>` :
                        `<button type="button" class="btn btn-outline-secondary" onclick="lockUser(${user.id})" title="锁定">
                            <i class="bi bi-lock"></i>
                        </button>`
                    }
                    <button type="button" class="btn btn-outline-danger" onclick="deleteUser(${user.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

/**
 * 渲染分页
 */
function renderPagination(pageData) {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';

    const totalPages = pageData.pages;
    const current = pageData.current;

    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${current === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${current - 1})">上一页</a>`;
    pagination.appendChild(prevLi);

    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(totalPages, current + 2);

    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === current ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${current === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${current + 1})">下一页</a>`;
    pagination.appendChild(nextLi);
}

/**
 * 切换页面
 */
function changePage(page) {
    if (page < 1) return;
    currentPage = page;
    loadUserList();
}

/**
 * 更新选中的用户
 */
function updateSelectedUsers() {
    const checkboxes = document.querySelectorAll('input[name="userCheckbox"]:checked');
    selectedUserIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
    
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    batchDeleteBtn.disabled = selectedUserIds.length === 0;
}

/**
 * 显示创建用户模态框
 */
function showCreateModal() {
    document.getElementById('userModalTitle').textContent = '新增用户';
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';
    
    // 显示密码字段
    document.getElementById('password').parentElement.style.display = 'block';
    document.getElementById('confirmPassword').parentElement.style.display = 'block';
    
    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

/**
 * 编辑用户
 */
function editUser(userId) {
    fetch(`${CONTEXT_PATH}/api/system/user/${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success || data.code === 200) {
                const user = data.data;
                document.getElementById('userModalTitle').textContent = '编辑用户';
                document.getElementById('userId').value = user.id;
                document.getElementById('username').value = user.username;
                document.getElementById('nickname').value = user.nickname || '';
                document.getElementById('email').value = user.email || '';
                document.getElementById('phone').value = user.phone || '';
                document.getElementById('deptId').value = user.deptId || '';
                document.getElementById('status').value = user.status;
                document.getElementById('remark').value = user.remark || '';
                
                // 隐藏密码字段
                document.getElementById('password').parentElement.style.display = 'none';
                document.getElementById('confirmPassword').parentElement.style.display = 'none';
                
                // 设置角色
                setUserRoles(user.roles || []);
                
                const modal = new bootstrap.Modal(document.getElementById('userModal'));
                modal.show();
            } else {
                showToast('获取用户信息失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 保存用户
 */
function saveUser() {
    const userId = document.getElementById('userId').value;
    const isEdit = !!userId;
    
    // 表单验证
    if (!validateUserForm(isEdit)) {
        return;
    }
    
    const formData = {
        username: document.getElementById('username').value,
        nickname: document.getElementById('nickname').value,
        email: document.getElementById('email').value,
        phone: document.getElementById('phone').value,
        deptId: document.getElementById('deptId').value || null,
        status: parseInt(document.getElementById('status').value),
        remark: document.getElementById('remark').value,
        roleIds: getSelectedRoles()
    };
    
    if (!isEdit) {
        formData.password = document.getElementById('password').value;
    } else {
        formData.id = parseInt(userId);
    }
    
    const url = isEdit ? `${CONTEXT_PATH}/api/system/user/${userId}` : `${CONTEXT_PATH}/api/system/user`;
    const method = isEdit ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(isEdit ? '用户更新成功' : '用户创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
            loadUserList();
        } else {
            showToast((isEdit ? '用户更新失败: ' : '用户创建失败: ') + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

// showAlert函数已移除，使用EdgeMind Toast组件

/**
 * 加载部门列表
 */
function loadDepartments() {
    fetch(`${CONTEXT_PATH}/api/system/dept/all`)
        .then(response => response.json())
        .then(data => {
            if (data.success || data.code === 200) {
                const deptSelects = document.querySelectorAll('#searchDept, #deptId');
                deptSelects.forEach(select => {
                    // 保留第一个选项
                    const firstOption = select.firstElementChild;
                    select.innerHTML = '';
                    select.appendChild(firstOption);

                    data.data.forEach(dept => {
                        const option = document.createElement('option');
                        option.value = dept.id;
                        option.textContent = dept.deptName;
                        select.appendChild(option);
                    });
                });
            }
        })
        .catch(error => console.error('加载部门列表失败:', error));
}

/**
 * 加载角色列表
 */
function loadRoles() {
    fetch(`${CONTEXT_PATH}/api/system/role/all`)
        .then(response => response.json())
        .then(data => {
            if (data.success || data.code === 200) {
                const roleContainer = document.getElementById('roleCheckboxes');
                roleContainer.innerHTML = '';

                data.data.forEach(role => {
                    const div = document.createElement('div');
                    div.className = 'col-md-4 mb-2';
                    div.innerHTML = `
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="${role.id}" id="role_${role.id}">
                            <label class="form-check-label" for="role_${role.id}">
                                ${role.roleName}
                            </label>
                        </div>
                    `;
                    roleContainer.appendChild(div);
                });
            }
        })
        .catch(error => console.error('加载角色列表失败:', error));
}

/**
 * 设置用户角色
 */
function setUserRoles(roles) {
    // 清除所有选中状态
    const checkboxes = document.querySelectorAll('#roleCheckboxes input[type="checkbox"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);

    // 设置选中状态
    roles.forEach(role => {
        const checkbox = document.getElementById(`role_${role.id}`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });
}

/**
 * 获取选中的角色
 */
function getSelectedRoles() {
    const checkboxes = document.querySelectorAll('#roleCheckboxes input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

/**
 * 表单验证
 */
function validateUserForm(isEdit) {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const email = document.getElementById('email').value.trim();
    const phone = document.getElementById('phone').value.trim();

    if (!username) {
        showToast('请输入用户名', 'warning');
        return false;
    }

    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        showToast('用户名只能包含字母、数字和下划线', 'warning');
        return false;
    }

    if (!isEdit) {
        if (!password) {
            showToast('请输入密码', 'warning');
            return false;
        }

        if (password.length < 6 || password.length > 20) {
            showToast('密码长度必须在6-20位之间', 'warning');
            return false;
        }

        if (password !== confirmPassword) {
            showToast('两次输入的密码不一致', 'warning');
            return false;
        }
    }

    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        showToast('邮箱格式不正确', 'warning');
        return false;
    }

    if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
        showToast('手机号格式不正确', 'warning');
        return false;
    }

    return true;
}

/**
 * 重置搜索
 */
function resetSearch() {
    document.getElementById('searchForm').reset();
    currentPage = 1;
    loadUserList();
}

// 旧版本密码管理函数已删除，使用文件末尾的新版本

/**
 * 删除用户
 */
function deleteUser(userId) {
    if (!confirm('确定要删除这个用户吗？此操作不可恢复。')) {
        return;
    }

    fetch(`${CONTEXT_PATH}/api/system/user/${userId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success || data.code === 200) {
            showToast('用户删除成功', 'success');
            loadUserList();
        } else {
            showToast('用户删除失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 批量删除用户
 */
function batchDelete() {
    if (selectedUserIds.length === 0) {
        showToast('请选择要删除的用户', 'warning');
        return;
    }

    if (!confirm(`确定要删除选中的${selectedUserIds.length}个用户吗？此操作不可恢复。`)) {
        return;
    }

    fetch(`${CONTEXT_PATH}/api/system/user/batch`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(selectedUserIds)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success || data.code === 200) {
            showToast('用户批量删除成功', 'success');
            selectedUserIds = [];
            updateSelectedUsers();
            loadUserList();
        } else {
            showToast('用户批量删除失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 锁定用户
 */
function lockUser(userId) {
    if (!confirm('确定要锁定这个用户吗？')) {
        return;
    }

    fetch(`${CONTEXT_PATH}/api/system/user/${userId}/lock`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success || data.code === 200) {
            showToast('用户已锁定', 'success');
            loadUserList();
        } else {
            showToast('锁定失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 解锁用户
 */
function unlockUser(userId) {
    fetch(`${CONTEXT_PATH}/api/system/user/${userId}/unlock`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success || data.code === 200) {
            showToast('用户已解锁', 'success');
            loadUserList();
        } else {
            showToast('解锁失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 查看用户角色
 */
function viewUserRoles(userId) {
    fetch(`${CONTEXT_PATH}/api/system/user/${userId}/roles`)
        .then(response => response.json())
        .then(data => {
            if (data.success || data.code === 200) {
                const roles = data.data.map(role => role.roleName).join(', ');
                alert('用户角色: ' + (roles || '无'));
            } else {
                showToast('获取用户角色失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 导出用户数据
 */
async function exportUsers() {
    try {
        const searchParams = {
            username: document.getElementById('searchUsername').value,
            nickname: document.getElementById('searchNickname').value,
            deptId: document.getElementById('searchDept').value,
            status: document.getElementById('searchStatus').value
        };

        // 移除空值参数
        Object.keys(searchParams).forEach(key => {
            if (!searchParams[key]) {
                delete searchParams[key];
            }
        });

        const data = await apiRequest(`${CONTEXT_PATH}/api/system/user/export`, {
            method: 'POST',
            body: JSON.stringify(searchParams)
        });

        if (data.code === 200) {
            showToast('导出成功，共 ' + (data.data?.length || 0) + ' 条记录', 'success');
            // 这里可以实现实际的文件下载逻辑
        } else {
            showToast('导出失败: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('导出失败: ' + error.message, 'danger');
    }
}

/**
 * 显示重置密码模态框
 */
function showResetPasswordModal(userId, username) {
    document.getElementById('resetUserId').value = userId;
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmNewPassword').value = '';

    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

/**
 * 重置用户密码（管理员操作）
 */
async function resetPassword() {
    try {
        const userId = document.getElementById('resetUserId').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmNewPassword = document.getElementById('confirmNewPassword').value;

        // 验证输入
        if (!newPassword) {
            showToast('请输入新密码', 'warning');
            return;
        }

        if (newPassword.length < 6 || newPassword.length > 20) {
            showToast('密码长度必须在6-20个字符之间', 'warning');
            return;
        }

        if (newPassword !== confirmNewPassword) {
            showToast('两次输入的密码不一致', 'warning');
            return;
        }

        const data = await apiRequest(`${CONTEXT_PATH}/api/system/user/${userId}/reset-password`, {
            method: 'POST',
            body: JSON.stringify({
                newPassword: newPassword
            })
        });

        if (data.code === 200) {
            showToast('密码重置成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
        } else {
            showToast('密码重置失败: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('密码重置失败: ' + error.message, 'danger');
    }
}

/**
 * 显示修改密码模态框（用户自己操作）
 */
function showChangePasswordModal(userId, username) {
    document.getElementById('changeUserId').value = userId;
    document.getElementById('oldPassword').value = '';
    document.getElementById('changeNewPassword').value = '';
    document.getElementById('changeConfirmPassword').value = '';

    const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
    modal.show();
}

/**
 * 修改用户密码（用户自己操作）
 */
async function changePassword() {
    try {
        const userId = document.getElementById('changeUserId').value;
        const oldPassword = document.getElementById('oldPassword').value;
        const newPassword = document.getElementById('changeNewPassword').value;
        const confirmPassword = document.getElementById('changeConfirmPassword').value;

        // 验证输入
        if (!oldPassword) {
            showToast('请输入原密码', 'warning');
            return;
        }

        if (!newPassword) {
            showToast('请输入新密码', 'warning');
            return;
        }

        if (newPassword.length < 6 || newPassword.length > 20) {
            showToast('密码长度必须在6-20个字符之间', 'warning');
            return;
        }

        if (newPassword !== confirmPassword) {
            showToast('两次输入的新密码不一致', 'warning');
            return;
        }

        const data = await apiRequest(`${CONTEXT_PATH}/api/system/user/${userId}/change-password`, {
            method: 'POST',
            body: JSON.stringify({
                oldPassword: oldPassword,
                newPassword: newPassword,
                confirmPassword: confirmPassword
            })
        });

        if (data.code === 200) {
            showToast('密码修改成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
        } else {
            showToast('密码修改失败: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('密码修改失败: ' + error.message, 'danger');
    }
}
