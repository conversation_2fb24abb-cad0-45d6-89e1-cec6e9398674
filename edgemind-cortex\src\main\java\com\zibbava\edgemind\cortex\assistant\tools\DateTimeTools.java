package com.zibbava.edgemind.cortex.assistant.tools;

import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * 日期时间工具类，提供日期时间相关的操作
 */
@Component
public class DateTimeTools implements Tools {

    private static final DateTimeFormatter DEFAULT_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Tool("获取当前日期时间")
    public String getCurrentDateTime() {
        return LocalDateTime.now().format(DEFAULT_DATETIME_FORMATTER);
    }

    @Tool("获取当前日期")
    public String getCurrentDate() {
        return LocalDate.now().format(DEFAULT_DATE_FORMATTER);
    }

    @Tool("获取指定日期是星期几")
    public String getDayOfWeek(
            @P("日期，格式为yyyy-MM-dd") String dateStr
    ) {
        LocalDate date = LocalDate.parse(dateStr, DEFAULT_DATE_FORMATTER);
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        String[] weekdays = {"星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"};
        System.out.println(">>>>>>>>>>>>>>>>>获取指定日期是星期几");
        return weekdays[dayOfWeek.getValue() - 1];
    }

    @Tool("计算两个日期之间的天数差")
    public long daysBetween(
            @P("起始日期，格式为yyyy-MM-dd") String startDateStr,
            @P("结束日期，格式为yyyy-MM-dd") String endDateStr
    ) {
        LocalDate startDate = LocalDate.parse(startDateStr, DEFAULT_DATE_FORMATTER);
        LocalDate endDate = LocalDate.parse(endDateStr, DEFAULT_DATE_FORMATTER);
        return ChronoUnit.DAYS.between(startDate, endDate);
    }

    @Tool("将日期增加指定天数")
    public String addDays(
            @P("原始日期，格式为yyyy-MM-dd") String dateStr,
            @P("需要增加的天数") int days
    ) {
        LocalDate date = LocalDate.parse(dateStr, DEFAULT_DATE_FORMATTER);
        return date.plusDays(days).format(DEFAULT_DATE_FORMATTER);
    }

    @Tool("检查给定年份是否为闰年")
    public boolean isLeapYear(
            @P("年份") int year
    ) {
        return LocalDate.of(year, 1, 1).isLeapYear();
    }
}