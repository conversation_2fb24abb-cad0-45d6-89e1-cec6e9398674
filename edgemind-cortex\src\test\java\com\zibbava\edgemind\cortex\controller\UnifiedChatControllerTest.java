package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.exception.NotLoginException;
import com.zibbava.edgemind.cortex.base.BaseControllerTest;
import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import com.zibbava.edgemind.cortex.common.exception.AccessDeniedException;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.common.exception.ResourceNotFoundException;
import com.zibbava.edgemind.cortex.dto.UnifiedChatRequest;
import com.zibbava.edgemind.cortex.service.UnifiedChatService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * UnifiedChatController 单元测试
 * 测试统一聊天接口功能，包括流式聊天、异常处理等
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
@DisplayName("统一聊天控制器测试")
class UnifiedChatControllerTest extends BaseControllerTest {

    @MockBean
    private UnifiedChatService unifiedChatService;

    private UnifiedChatRequest validRequest;

    @Override
    protected void setUp() {
        validRequest = new UnifiedChatRequest();
        validRequest.setPrompt("测试问题");
        validRequest.setModel("gpt-3.5-turbo");
        validRequest.setConversationId(1L);
        validRequest.setTitle("测试对话");
    }

    @Test
    @DisplayName("流式聊天 - 成功响应")
    void streamChat_Success() throws Exception {
        // Given
        Flux<String> responseFlux = Flux.just("data: 测试响应");
        when(unifiedChatService.chat(any(UnifiedChatRequest.class))).thenReturn(responseFlux);

        // When & Then
        mockMvc.perform(withAuth(post("/api/unified-chat/stream"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "text/event-stream;charset=UTF-8"));
    }

    @Test
    @DisplayName("流式聊天 - 知识库检索模式")
    void streamChat_WithKnowledgeBase() throws Exception {
        // Given
        validRequest.setKnowledgeNodeId("kb-001");
        Flux<String> responseFlux = Flux.just("data: 基于知识库的回答");
        when(unifiedChatService.chat(any(UnifiedChatRequest.class))).thenReturn(responseFlux);

        // When & Then
        mockMvc.perform(withAuth(post("/api/unified-chat/stream"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "text/event-stream;charset=UTF-8"));
    }

    @Test
    @DisplayName("流式聊天 - 未登录异常处理")
    void streamChat_NotLoginException() throws Exception {
        // Given
        NotLoginException exception = new NotLoginException("", "", NotLoginException.NOT_TOKEN);
        when(unifiedChatService.chat(any(UnifiedChatRequest.class))).thenReturn(Flux.error(exception));

        // When & Then
        mockMvc.perform(withAuth(post("/api/unified-chat/stream"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("error: 未提供认证令牌，请重新登录")));
    }

    @Test
    @DisplayName("流式聊天 - Token无效异常处理")
    void streamChat_InvalidTokenException() throws Exception {
        // Given
        NotLoginException exception = new NotLoginException("", "", NotLoginException.INVALID_TOKEN);
        when(unifiedChatService.chat(any(UnifiedChatRequest.class))).thenReturn(Flux.error(exception));

        // When & Then
        mockMvc.perform(withAuth(post("/api/unified-chat/stream"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("error: 认证令牌无效，请重新登录")));
    }

    @Test
    @DisplayName("流式聊天 - 访问权限异常处理")
    void streamChat_AccessDeniedException() throws Exception {
        // Given
        AccessDeniedException exception = new AccessDeniedException("权限不足");
        when(unifiedChatService.chat(any(UnifiedChatRequest.class))).thenReturn(Flux.error(exception));

        // When & Then
        mockMvc.perform(withAuth(post("/api/unified-chat/stream"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("error: 无权访问资源: 权限不足")));
    }

    @Test
    @DisplayName("流式聊天 - 资源不存在异常处理")
    void streamChat_ResourceNotFoundException() throws Exception {
        // Given
        ResourceNotFoundException exception = new ResourceNotFoundException("模型不存在");
        when(unifiedChatService.chat(any(UnifiedChatRequest.class))).thenReturn(Flux.error(exception));

        // When & Then
        mockMvc.perform(withAuth(post("/api/unified-chat/stream"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("error: 资源不存在: 模型不存在")));
    }

    @Test
    @DisplayName("流式聊天 - 业务异常处理")
    void streamChat_BusinessException() throws Exception {
        // Given
        BusinessException exception = new BusinessException(ResultCode.PARAM_INVALID, "参数无效");
        when(unifiedChatService.chat(any(UnifiedChatRequest.class))).thenReturn(Flux.error(exception));

        // When & Then
        mockMvc.perform(withAuth(post("/api/unified-chat/stream"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("error: 参数无效")));
    }

    @Test
    @DisplayName("流式聊天 - 参数验证异常处理")
    void streamChat_IllegalArgumentException() throws Exception {
        // Given
        IllegalArgumentException exception = new IllegalArgumentException("参数错误");
        when(unifiedChatService.chat(any(UnifiedChatRequest.class))).thenReturn(Flux.error(exception));

        // When & Then
        mockMvc.perform(withAuth(post("/api/unified-chat/stream"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("error: 参数错误")));
    }

    @Test
    @DisplayName("流式聊天 - 请求参数验证失败")
    void streamChat_ValidationFailed() throws Exception {
        // Given
        UnifiedChatRequest invalidRequest = new UnifiedChatRequest();
        invalidRequest.setPrompt(""); // 空提示词
        invalidRequest.setModel("gpt-3.5-turbo");

        // When & Then
        mockMvc.perform(withAuth(post("/api/unified-chat/stream"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isOk())
                .andExpect(content().string(org.hamcrest.Matchers.containsString("error:")));
    }
} 