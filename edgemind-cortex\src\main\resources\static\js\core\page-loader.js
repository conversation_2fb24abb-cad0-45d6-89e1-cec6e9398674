/**
 * 页面加载器 - 根据URL参数处理iframe内容加载
 */

// 保存当前页面类型
let currentPageType = null;

/**
 * 初始化页面加载器
 */
function initialize() {

    
    document.addEventListener('DOMContentLoaded', () => {
        // 获取iframe元素
        const contentFrame = document.getElementById('contentFrame');
        if (!contentFrame) {
            console.error("[PageLoader] 找不到contentFrame元素");
            return;
        }
        
        // 处理URL参数
        handleUrlParameters(contentFrame);
        
        // 监听popstate事件，以处理浏览器的前进和后退
        window.addEventListener('popstate', () => {
            handleUrlParameters(contentFrame);
        });
    });
}

/**
 * 处理URL参数并加载相应的内容
 * @param {HTMLIFrameElement} iframe - 内容iframe元素
 */
function handleUrlParameters(iframe) {
    const urlParams = new URLSearchParams(window.location.search);
    const page = urlParams.get('page');
    
    // 如果存在page参数，则加载对应的内容
    if (page) {
        loadPageContent(iframe, page);
    } else {
        // 默认加载聊天页面
        if (!currentPageType || currentPageType !== 'chat') {
            iframe.src = '/wkg/chat/content';
            currentPageType = 'chat';
        }
    }
}

/**
 * 根据页面类型加载对应的内容
 * @param {HTMLIFrameElement} iframe - 内容iframe元素
 * @param {string} pageType - 页面类型
 */
function loadPageContent(iframe, pageType) {

    
    // 如果当前已经是请求的页面类型，则不重新加载
    if (currentPageType === pageType) {

        return;
    }
    
    // 根据页面类型设置iframe的src
    switch (pageType) {
        case 'chat':
            iframe.src = '/wkg/chat/content';
            break;
        case 'tools':
            iframe.src = '/wkg/tools/content';
            break;
        case 'permission_users':
            iframe.src = '/wkg/permission/users/content';
            break;
        case 'permission_departments':
            iframe.src = '/wkg/permission/departments/content';
            break;
        case 'permission_roles':
            iframe.src = '/wkg/permission/roles/content';
            break;
        case 'permission_permissions':
            iframe.src = '/wkg/permission/permissions/content';
            break;
        case 'permission_logs':
            iframe.src = '/wkg/permission/logs/content';
            break;
        default:
            console.warn(`[PageLoader] 未知的页面类型: ${pageType}`);
            iframe.src = '/wkg/chat/content';
            pageType = 'chat';
    }
    
    // 更新当前页面类型
    currentPageType = pageType;
    
    // 更新URL参数，但不刷新页面
    const url = new URL(window.location);
    url.searchParams.set('page', pageType);
    window.history.pushState({}, '', url);
}

// 导出函数
export { initialize, loadPageContent };