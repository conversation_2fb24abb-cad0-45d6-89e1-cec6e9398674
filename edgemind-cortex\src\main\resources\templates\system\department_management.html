<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部门管理 - 端智AI助手</title>
    <!-- 使用EdgeMind本地资源 -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <!-- EdgeMind设计系统样式 -->
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/components/modal-unified.css">
</head>
<body style="background-color: #f8f9fa; padding: 20px;">
    <div class="container-fluid">
        <!-- 页面标题 - 使用EdgeMind标准样式 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-primary fw-semibold mb-0">
                <i class="bi bi-diagram-3 me-2"></i>
                部门管理
            </h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="#" class="text-decoration-none" style="color: var(--text-secondary);">系统管理</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">部门管理</li>
                </ol>
            </nav>
        </div>
        <div class="row">
            <!-- 部门树 -->
            <div class="col-md-8">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="fw-semibold mb-0" style="color: var(--text-primary);">
                                <i class="bi bi-diagram-3 me-2"></i>组织架构
                            </h5>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="expandAll()">
                                    <i class="bi bi-arrows-expand me-1"></i>展开全部
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="collapseAll()">
                                    <i class="bi bi-arrows-collapse me-1"></i>收起全部
                                </button>
                                <button type="button" class="btn btn-sm btn-success ms-2" onclick="showCreateModal()">
                                    <i class="bi bi-plus-lg me-1"></i>新增部门
                                </button>
                            </div>
                        </div>
                        <div id="deptTreeContainer" style="max-height: 600px; overflow-y: auto;">
                            <!-- 部门树将通过JS动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 部门详情 -->
            <div class="col-md-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h5 class="fw-semibold mb-3" style="color: var(--text-primary);">
                            <i class="bi bi-info-circle me-2"></i>部门详情
                        </h5>
                        <div id="deptDetail">
                            <div class="text-center text-muted py-5">
                                <i class="bi bi-building" style="font-size: 3rem; color: var(--text-secondary);"></i>
                                <p class="mt-2" style="color: var(--text-secondary);">请选择一个部门查看详情</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建/编辑部门模态框 - 使用EdgeMind模态框样式 -->
    <div class="modal fade modal-unified" id="deptModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content border-0 shadow">
                <div class="modal-header" style="background-color: var(--primary-light); border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title fw-semibold" id="deptModalTitle" style="color: var(--text-primary);">
                        <i class="bi bi-building-add me-2"></i>新增部门
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="deptForm">
                        <input type="hidden" id="deptId">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">
                                    部门名称 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control border-1" id="deptName" required
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">
                                    部门编码 <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control border-1" id="deptCode" required
                                       style="border-color: var(--border-color);">
                                <div class="form-text" style="color: var(--text-secondary);">
                                    部门编码必须唯一
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">上级部门</label>
                                <select class="form-select border-1" id="parentId" style="border-color: var(--border-color);">
                                    <option value="0">顶级部门</option>
                                    <!-- 父部门选项将通过JS动态加载 -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">部门负责人</label>
                                <select class="form-select border-1" id="managerId" style="border-color: var(--border-color);">
                                    <option value="">请选择负责人</option>
                                    <!-- 用户选项将通过JS动态加载 -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">联系电话</label>
                                <input type="tel" class="form-control border-1" id="phone"
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">邮箱</label>
                                <input type="email" class="form-control border-1" id="email"
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">排序</label>
                                <input type="number" class="form-control border-1" id="sortOrder" value="0"
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">状态</label>
                                <select class="form-select border-1" id="status" style="border-color: var(--border-color);">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">部门地址</label>
                                <input type="text" class="form-control border-1" id="address"
                                       style="border-color: var(--border-color);">
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-medium" style="color: var(--text-primary);">部门描述</label>
                                <textarea class="form-control border-1" id="description" rows="3"
                                          style="border-color: var(--border-color);"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="background-color: #f8f9fa; border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary px-4" onclick="saveDept()"
                            style="background-color: var(--primary-color); border-color: var(--primary-color);">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用EdgeMind本地JavaScript资源 -->
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script type="module" src="/wkg/js/shared/components/modal-component.js"></script>
    <script src="/wkg/js/system/department-management.js"></script>
</body>
</html>
