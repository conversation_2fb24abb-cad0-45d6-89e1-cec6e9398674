/* 聊天历史面板样式 */
.chat-history-panel {
    width: 240px;
    background: #f5f6fa;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    min-width: 200px;
    max-width: 320px;
    box-shadow: none;
}

.conversations-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 0 0 8px 0;
    /* 隐藏滚动条但保留滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏Webkit浏览器的滚动条 */
.conversations-list::-webkit-scrollbar {
    display: none;
}

.conversation-item {
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 6px;
    margin: 4px 8px;
    transition: background 0.15s, color 0.15s;
    color: #333;
    display: flex;
    align-items: center;
    font-size: 15px;
    position: relative;
}

.conversation-main-link {
    display: flex;
    align-items: center;
    width: 100%;
}

.conversation-item.active {
    background: #e3f0ff;
    font-weight: 600;
    color: #1976d2;
}

.conversation-item:hover {
    background: #eaedf1;
}

/* 加载更多按钮样式 */
.conversation-loading-indicator {
    padding: 8px;
    color: #6c757d;
    font-size: 13px;
}

/* 空列表提示样式 */
.conversation-empty-indicator {
    padding: 16px;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    font-size: 14px;
}

/* 响应式布局调整 */
@media (max-width: 900px) {
    .chat-main-layout { 
        flex-direction: column; 
    }
    .chat-history-panel { 
        width: 100%; 
        max-width: none; 
        border-right: none; 
        border-bottom: 1px solid #e0e0e0; 
    }
}

/* 会话选项下拉菜单 */
.conversation-options-dropdown {
    opacity: 0;
    transition: opacity 0.15s ease;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
}

.conversation-item:hover .conversation-options-dropdown {
    opacity: 1;
}

.conversation-item.active .conversation-options-dropdown {
    opacity: 1;
}

/* 确保下拉菜单按钮在鼠标悬停时一直可见 */
.conversation-options-dropdown:hover,
.dropdown-menu.show + .conversation-options-btn,
.conversation-options-btn:hover,
.conversation-options-btn:focus {
    opacity: 1 !important;
}

.conversation-options-btn {
    padding: 0.15rem 0.3rem !important;
    font-size: 0.75rem;
    background-color: transparent;
    border: none;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer !important;
    width: auto !important;
    height: auto !important;
    border-radius: 0.25rem !important;
}

.conversation-item.active .conversation-options-btn {
    color: #1976d2;
}

.conversation-options-btn:hover {
    background-color: rgba(108, 117, 125, 0.1);
    color: #495057;
}

/* 列表加载指示器 */
.conversation-loading-indicator,
.conversation-error-indicator,
.conversation-empty-indicator {
    margin: 10px 0;
    padding: 8px;
    text-align: center;
    font-size: 14px;
}

.conversation-empty-indicator {
    color: #999;
    padding: 20px 0;
}

.conversation-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex-grow: 1;
    margin-right: 30px; /* 增加空间给操作按钮，原来是24px */
    max-width: calc(100% - 40px); /* 防止超长标题导致按钮挤压 */
    font-size: 14px; /* 稍微减小字体大小，以便显示更多文本 */
}

/* 确保下拉菜单在显示时按钮保持可见状态 */
.dropdown.show .conversation-options-btn,
.conversation-options-dropdown.show,
.conversation-options-dropdown .show {
    opacity: 1 !important;
}

/* 给下拉菜单项添加鼠标悬停效果 */
.dropdown-item:hover {
    background-color: #f0f7ff;
}

/* AI对话下拉菜单宽度调整 */
.conversation-options-dropdown .dropdown-menu {
    min-width: 6rem !important;
    width: auto !important;
    font-size: 0.85rem !important;
    padding: 0.3rem 0 !important;
}

.conversation-options-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0.35rem 0.75rem !important;
    white-space: nowrap;
    font-size: 0.85rem !important;
    line-height: 1.3 !important;
}

.conversation-options-dropdown .dropdown-item i {
    font-size: 0.8rem !important;
    margin-right: 0.4rem !important;
}

.conversation-options-dropdown .dropdown-divider {
    margin: 0.3rem 0 !important;
}

/* 确保当下拉菜单打开时，整个菜单组件保持可见 */
.conversation-item:has(.dropdown-menu.show) .conversation-options-dropdown,
.conversation-item:has(.show) .conversation-options-dropdown {
    opacity: 1 !important;
}

/* 提升正在交互的对话项的层级 */
.conversation-item.z-index-high {
    position: relative; /* 确保创建堆叠上下文 */
    z-index: 10;       /* 高于普通对话项 (默认为 auto 或 1) */
}

/* 如果有相关样式，在这里调整会话项目样式，使其更好地适应更宽的面板 */