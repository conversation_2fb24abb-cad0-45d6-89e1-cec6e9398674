package com.zibbava.edgemind.cortex.aspect;

import cn.dev33.satoken.stp.StpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zibbava.edgemind.cortex.annotation.OperationLog;
import com.zibbava.edgemind.cortex.service.OperationLogService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;

/**
 * 操作日志切面
 * 
 * <AUTHOR>
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class OperationLogAspect {

    private final OperationLogService operationLogService;
    private final ObjectMapper objectMapper;

    @Pointcut("@annotation(com.zibbava.edgemind.cortex.annotation.OperationLog)")
    public void operationLogPointcut() {
    }

    @Around("operationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        // 获取注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperationLog operationLogAnnotation = method.getAnnotation(OperationLog.class);
        
        // 获取请求信息
        HttpServletRequest request = getHttpServletRequest();
        String requestUrl = null;
        String requestMethod = null;
        String ipAddress = null;
        String userAgent = null;
        
        if (request != null) {
            requestUrl = request.getRequestURI();
            requestMethod = request.getMethod();
            ipAddress = getClientIpAddress(request);
            userAgent = request.getHeader("User-Agent");
        }
        
        // 获取用户信息
        Long userId = null;
        String username = null;
        try {
            if (StpUtil.isLogin()) {
                userId = StpUtil.getLoginIdAsLong();
                username = StpUtil.getSession().getString("username");
            }
        } catch (Exception e) {
            log.warn("获取用户信息失败: {}", e.getMessage());
        }
        
        // 获取请求参数
        String requestParams = null;
        if (operationLogAnnotation.recordParams()) {
            try {
                Object[] args = joinPoint.getArgs();
                if (args != null && args.length > 0) {
                    // 过滤敏感参数（如密码）
                    requestParams = filterSensitiveParams(objectMapper.writeValueAsString(args));
                }
            } catch (Exception e) {
                log.warn("序列化请求参数失败: {}", e.getMessage());
                requestParams = "参数序列化失败";
            }
        }
        
        Object result = null;
        String errorMessage = null;
        int status = com.zibbava.edgemind.cortex.entity.OperationLog.Status.SUCCESS;
        
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            status = com.zibbava.edgemind.cortex.entity.OperationLog.Status.FAILED;
            errorMessage = e.getMessage();
            throw e;
        } finally {
            try {
                // 计算执行时间
                long executionTime = operationLogAnnotation.recordExecutionTime() ? 
                    System.currentTimeMillis() - startTime : null;
                
                // 获取响应结果
                String responseResult = null;
                if (operationLogAnnotation.recordResult() && result != null) {
                    try {
                        responseResult = objectMapper.writeValueAsString(result);
                        // 限制响应结果长度
                        if (responseResult.length() > 1000) {
                            responseResult = responseResult.substring(0, 1000) + "...";
                        }
                    } catch (Exception e) {
                        responseResult = "响应结果序列化失败";
                    }
                }
                
                // 记录操作日志
                operationLogService.recordLog(
                    userId,
                    username,
                    operationLogAnnotation.operationType(),
                    operationLogAnnotation.module(),
                    operationLogAnnotation.description(),
                    requestUrl,
                    requestMethod,
                    requestParams,
                    responseResult,
                    ipAddress,
                    userAgent,
                    executionTime,
                    status,
                    errorMessage
                );
            } catch (Exception e) {
                log.error("记录操作日志失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 获取HttpServletRequest
     */
    private HttpServletRequest getHttpServletRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 过滤敏感参数
     */
    private String filterSensitiveParams(String params) {
        if (params == null) {
            return null;
        }
        
        // 简单的敏感信息过滤，实际项目中可以更复杂
        return params.replaceAll("\"password\"\\s*:\\s*\"[^\"]*\"", "\"password\":\"***\"")
                    .replaceAll("\"oldPassword\"\\s*:\\s*\"[^\"]*\"", "\"oldPassword\":\"***\"")
                    .replaceAll("\"newPassword\"\\s*:\\s*\"[^\"]*\"", "\"newPassword\":\"***\"");
    }
}
