package com.zibbava.edgemind.cortex.manager;

import com.onlyoffice.manager.security.DefaultJwtManager;
import com.onlyoffice.manager.settings.SettingsManager;
import org.springframework.stereotype.Component;

/**
 * JWT管理器实现
 * 处理ONLYOFFICE JWT令牌的生成与验证
 */
@Component
public class JwtManagerImpl extends DefaultJwtManager {
    public JwtManagerImpl(final SettingsManager settingsManager) {
        super(settingsManager);
    }
} 