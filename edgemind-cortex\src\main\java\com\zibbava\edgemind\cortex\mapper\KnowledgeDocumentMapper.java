package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.KnowledgeDocument;
import org.apache.ibatis.annotations.Mapper;

/**
 * 知识库文档表（kb_knowledge_documents）的MyBatis Mapper接口。
 * 继承自 BaseMapper，提供了基础的 CRUD 功能。
 */
@Mapper
public interface KnowledgeDocumentMapper extends BaseMapper<KnowledgeDocument> {
    // MP BaseMapper 提供了基本的 CRUD 方法
} 