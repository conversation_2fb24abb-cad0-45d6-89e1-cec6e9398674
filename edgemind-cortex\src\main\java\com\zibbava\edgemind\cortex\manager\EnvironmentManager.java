package com.zibbava.edgemind.cortex.manager;

import com.zibbava.edgemind.cortex.config.EnvironmentConfig;

import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.net.URL;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

import static com.zibbava.edgemind.cortex.config.EnvironmentConfig.DEV_BASE_PATH;
import static com.zibbava.edgemind.cortex.config.EnvironmentConfig.IS_DEVELOPMENT;

/**
 * 环境管理器
 * 负责启动前的各种环境检测和启动
 */
public class EnvironmentManager {

    // 环境配置
//    private static final String DEV_BASE_PATH = EnvironmentConfig.

    /**
     * 获取基础路径（根据环境决定）
     */
    private static String getBasePath() {
        String basePath = IS_DEVELOPMENT ? DEV_BASE_PATH : System.getProperty("user.dir");
        System.out.println("EnvironmentManager - 当前环境: " + (IS_DEVELOPMENT ? "开发环境" : "生产环境") + ", 基础路径: " + basePath);
        return basePath;
    }

    private static JLabel statusLabel;
    private static JProgressBar progressBar;
    private static JFrame startupFrame;
    private static SystemTray systemTray;
    private static TrayIcon trayIcon;
    private static final CountDownLatch environmentReadyLatch = new CountDownLatch(1);

    /**
     * 显示启动GUI并检测环境
     *
     * @return CountDownLatch 用于等待环境就绪
     */
    public static CountDownLatch showStartupGuiAndCheckEnvironment() {
        SwingUtilities.invokeLater(() -> {
            createStartupGui();
            // 异步检测环境状态
            CompletableFuture.runAsync(() -> {
                checkAndStartEnvironment();
                // 环境检测完成后创建系统托盘
                SwingUtilities.invokeLater(() -> createSystemTray());
            });
        });
        return environmentReadyLatch;
    }

    /**
     * 关闭启动窗口
     */
    public static void closeStartupGui() {
        SwingUtilities.invokeLater(() -> {
            if (startupFrame != null) {
                startupFrame.dispose();
            }
        });
    }

    /**
     * 更新状态标签
     */
    public static void updateStatus(String status) {
        SwingUtilities.invokeLater(() -> {
            if (statusLabel != null) {
                statusLabel.setText(status);
            }
        });
    }

    /**
     * 创建启动GUI界面
     */
    private static void createStartupGui() {
        try {
            // 设置系统外观
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            // 忽略外观设置异常
        }

        // 创建主窗口
        startupFrame = new JFrame("端智 AI 助手 - 启动检查");
        startupFrame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        startupFrame.setSize(600, 400);
        startupFrame.setLocationRelativeTo(null); // 居中显示
        startupFrame.setResizable(false);

        // 创建主面板
        JPanel mainPanel = new JPanel();
        mainPanel.setBackground(Color.WHITE);
        mainPanel.setLayout(new BorderLayout(20, 20));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(30, 30, 30, 30));

        // 标题面板
        JPanel titlePanel = new JPanel();
        titlePanel.setBackground(Color.WHITE);
        JLabel titleLabel = new JLabel("端智 AI 助手", SwingConstants.CENTER);
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 28));
        titleLabel.setForeground(new Color(64, 128, 255));
        titlePanel.add(titleLabel);

        // 状态面板
        JPanel statusPanel = new JPanel();
        statusPanel.setBackground(Color.WHITE);
        statusPanel.setLayout(new BoxLayout(statusPanel, BoxLayout.Y_AXIS));

        statusLabel = new JLabel("正在检测环境状态...", SwingConstants.CENTER);
        statusLabel.setFont(new Font("微软雅黑", Font.PLAIN, 16));
        statusLabel.setAlignmentX(Component.CENTER_ALIGNMENT);

        progressBar = new JProgressBar();
        progressBar.setIndeterminate(true);
        progressBar.setStringPainted(true);
        progressBar.setString("检测中...");
        progressBar.setMaximumSize(new Dimension(400, 25));
        progressBar.setAlignmentX(Component.CENTER_ALIGNMENT);

        statusPanel.add(Box.createVerticalGlue());
        statusPanel.add(statusLabel);
        statusPanel.add(Box.createVerticalStrut(20));
        statusPanel.add(progressBar);
        statusPanel.add(Box.createVerticalGlue());

        // 按钮面板
        JPanel buttonPanel = new JPanel();
        buttonPanel.setBackground(Color.WHITE);
        JButton retryButton = new JButton("重新检测");
        retryButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        retryButton.setVisible(false);

        // 添加重试按钮事件监听
        retryButton.addActionListener(e -> {
            CompletableFuture.runAsync(() -> {
                SwingUtilities.invokeLater(() -> {
                    statusLabel.setText("正在重新检测环境状态...");
                    progressBar.setString("检测中...");
                    progressBar.setIndeterminate(true);
                    retryButton.setVisible(false);
                });
                // 重新检测环境
                checkAndStartEnvironment();
            });
        });

        buttonPanel.add(retryButton);

        mainPanel.add(titlePanel, BorderLayout.NORTH);
        mainPanel.add(statusPanel, BorderLayout.CENTER);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        startupFrame.add(mainPanel);
        startupFrame.setVisible(true);
    }

    /**
     * 检测并启动环境（WSL2、Docker和Ollama）
     */
    private static void checkAndStartEnvironment() {
        try {
            // 第一步：检查WSL2环境
            SwingUtilities.invokeLater(() -> {
                progressBar.setIndeterminate(false);
                progressBar.setValue(5);
                statusLabel.setText("正在检查WSL2环境...");
            });

            // 设置WSL2Manager的GUI组件引用
            WSL2Manager.setGuiComponents(progressBar, statusLabel);
            WSL2Manager.checkAndInstallWSL2();

            // 第二步：检查Docker环境
            SwingUtilities.invokeLater(() -> {
                progressBar.setValue(30);
                statusLabel.setText("正在检查Docker环境...");
            });

            // 设置DockerManager的GUI组件
            DockerManager.setGuiComponents(statusLabel, progressBar, null);
            boolean dockerReady = checkDockerEnvironment();

            if (!dockerReady) {
                showErrorAndRetryOption("Docker环境检测失败，请检查Docker安装");
                return;
            }

            // 第三步：检查Ollama环境
            SwingUtilities.invokeLater(() -> {
                progressBar.setValue(70);
                statusLabel.setText("正在检查Ollama环境...");
            });

            boolean ollamaReady = checkOllamaEnvironment();

            if (ollamaReady) {
                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(100);
                    statusLabel.setText("所有环境检测完成！");
                    progressBar.setString("环境就绪");
                });
                environmentReadyLatch.countDown();
            } else {
                showErrorAndRetryOption("Ollama环境检测失败，请检查Ollama安装");
            }

        } catch (Exception e) {
            System.err.println("环境检测过程发生错误: " + e.getMessage());
            showErrorAndRetryOption("环境检测过程发生错误: " + e.getMessage());
        }
    }

    /**
     * 检查Docker环境
     */
    private static boolean checkDockerEnvironment() {
        try {
            DockerManager.checkAndStartDocker();
            return DockerManager.isDockerRunning();
        } catch (Exception e) {
            System.err.println("Docker环境检查失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查Ollama环境
     */
    private static boolean checkOllamaEnvironment() {
        try {
            // 设置OllamaManager的GUI组件引用
            OllamaManager.setGuiComponents(progressBar, statusLabel);

            // 使用OllamaManager检查和启动Ollama
            boolean ollamaReady = OllamaManager.checkAndStartOllama();

            if (ollamaReady) {
                System.out.println("Ollama环境检查成功");
                return true;
            } else {
                System.err.println("Ollama环境检查失败");
                return false;
            }

        } catch (Exception e) {
            System.err.println("检测Ollama时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 显示错误信息和重试选项
     */
    private static void showErrorAndRetryOption(String errorMessage) {
        SwingUtilities.invokeLater(() -> {
            statusLabel.setText(errorMessage);
            progressBar.setString("检测失败");
            progressBar.setIndeterminate(false);

            // 显示重试按钮
            Container buttonPanel = null;
            Component[] components = startupFrame.getContentPane().getComponents();
            for (Component comp : components) {
                if (comp instanceof JPanel) {
                    JPanel panel = (JPanel) comp;
                    Component[] panelComponents = panel.getComponents();
                    for (Component panelComp : panelComponents) {
                        if (panelComp instanceof JPanel) {
                            JPanel subPanel = (JPanel) panelComp;
                            if (subPanel.getComponentCount() > 0 &&
                                    subPanel.getComponent(0) instanceof JButton) {
                                buttonPanel = subPanel;
                                break;
                            }
                        }
                    }
                }
            }

            if (buttonPanel != null) {
                Component[] buttons = buttonPanel.getComponents();
                if (buttons.length > 0 && buttons[0] instanceof JButton) {
                    buttons[0].setVisible(true);
                }
                buttonPanel.revalidate();
                buttonPanel.repaint();
            }
        });
    }

    /**
     * 创建系统托盘图标
     */
    private static void createSystemTray() {
        if (!SystemTray.isSupported()) {
            System.out.println("系统不支持托盘功能");
            return;
        }

        try {
            systemTray = SystemTray.getSystemTray();

            // 创建托盘图标
            Image trayIconImage = createTrayIcon();
            trayIcon = new TrayIcon(trayIconImage, "端智AI助手 - 运行中");
            trayIcon.setImageAutoSize(true);

            // 使用鼠标监听器来显示JPopupMenu，解决中文乱码问题
            trayIcon.addMouseListener(new java.awt.event.MouseAdapter() {
                @Override
                public void mouseReleased(java.awt.event.MouseEvent e) {
                    if (e.isPopupTrigger() || e.getButton() == java.awt.event.MouseEvent.BUTTON3) {
                        showJPopupMenu();
                    }
                }
            });

            // 添加到系统托盘
            systemTray.add(trayIcon);

            System.out.println("系统托盘图标创建成功");

        } catch (AWTException e) {
            System.err.println("无法创建系统托盘图标: " + e.getMessage());
        }
    }

    /**
     * 显示JPopupMenu来解决中文乱码问题
     */
    private static void showJPopupMenu() {
        // 获取鼠标当前位置
        Point mouseLocation = MouseInfo.getPointerInfo().getLocation();

        // 创建JDialog作为JPopupMenu的载体
        JDialog popupDialog = new JDialog();
        popupDialog.setUndecorated(true);
        popupDialog.setSize(1, 1);
        popupDialog.setLocation(mouseLocation.x, mouseLocation.y);

        // 创建JPopupMenu
        JPopupMenu popup = new JPopupMenu() {
            @Override
            public void firePopupMenuWillBecomeInvisible() {
                super.firePopupMenuWillBecomeInvisible();
                popupDialog.setVisible(false);
                popupDialog.dispose();
            }
        };

        // 创建菜单项
        JMenuItem restartItem = new JMenuItem("重启Web应用");
        restartItem.setHorizontalAlignment(SwingConstants.LEFT);
        restartItem.addActionListener(e -> {
            popup.setVisible(false);
            restartWebApplication();
        });
        popup.add(restartItem);

        popup.addSeparator();

        JMenuItem exitItem = new JMenuItem("退出");
        exitItem.setHorizontalAlignment(SwingConstants.LEFT);
        exitItem.addActionListener(e -> {
            popup.setVisible(false);
            exitApplication();
        });
        popup.add(exitItem);

        // 显示对话框和菜单
        popupDialog.setVisible(true);
        popup.show(popupDialog, 0, 0);
    }

    /**
     * 创建托盘图标图像
     */
    private static Image createTrayIcon() {
        // PNG图标资源路径
        String iconPath = "/static/images/ai-assistant-logo.png";
        URL iconResource = EnvironmentManager.class.getResource(iconPath);

        if (iconResource == null) {
            System.err.println("错误：找不到托盘图标资源 " + iconPath + "。将使用备用图标。");
            return createFallbackTrayIcon();
        }

        try {
            // 使用ImageIcon加载PNG图像，这是最简单可靠的方式
            return new ImageIcon(iconResource).getImage();
        } catch (Exception e) {
            System.err.println("加载PNG图标时发生错误: " + e.getMessage());
            e.printStackTrace();
            return createFallbackTrayIcon(); // 加载失败时使用备用图标
        }
    }

    /**
     * 创建备用的托盘图标（如果PNG加载失败）
     */
    private static Image createFallbackTrayIcon() {
        int size = 16;
        BufferedImage image = new BufferedImage(size, size, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();

        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setColor(new Color(0, 123, 255));
        g2d.fillOval(0, 0, size, size);
        g2d.setColor(Color.WHITE);
        g2d.setFont(new Font("SansSerif", Font.BOLD, 12));
        FontMetrics fm = g2d.getFontMetrics();
        g2d.drawString("A", (size - fm.stringWidth("A")) / 2, (size - fm.getHeight()) / 2 + fm.getAscent());

        g2d.dispose();
        return image;
    }

    /**
     * 退出应用程序
     */
    private static void exitApplication() {
        try {
            System.out.println("正在退出应用程序...");

            // 移除托盘图标
            if (systemTray != null && trayIcon != null) {
                systemTray.remove(trayIcon);
            }

            // 直接退出，清理工作由shutdown hook处理
            System.exit(0);

        } catch (Exception e) {
            System.err.println("退出应用程序时发生错误: " + e.getMessage());
            System.exit(1);
        }
    }

    /**
     * 停止Web进程
     */
    private static void stopWebProcess() {
        try {
            System.out.println("正在停止Web进程...");

            // 通过端口8080查找并终止进程
            ProcessBuilder pb = new ProcessBuilder(
                    "powershell", "-Command",
                    "Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue }"
            );
            Process process = pb.start();
            process.waitFor();

            System.out.println("Web进程已停止");

        } catch (Exception e) {
            System.err.println("停止Web进程失败: " + e.getMessage());
        }
    }


    /**
     * 重启Web应用
     */
    private static void restartWebApplication() {
        CompletableFuture.runAsync(() -> {
            try {
                System.out.println("正在重启Web应用...");

                // 显示重启确认对话框
                SwingUtilities.invokeAndWait(() -> {
                    int option = JOptionPane.showConfirmDialog(
                            null,
                            "确定要重启Web应用吗？\n这将停止当前服务并重新启动。",
                            "重启确认",
                            JOptionPane.YES_NO_OPTION,
                            JOptionPane.QUESTION_MESSAGE
                    );

                    if (option != JOptionPane.YES_OPTION) {
                        return;
                    }

                    // 执行重启
                    performWebApplicationRestart();
                });

            } catch (Exception e) {
                System.err.println("重启Web应用时发生错误: " + e.getMessage());
                SwingUtilities.invokeLater(() -> {
                    JOptionPane.showMessageDialog(
                            null,
                            "重启Web应用失败:\n" + e.getMessage(),
                            "重启失败",
                            JOptionPane.ERROR_MESSAGE
                    );
                });
            }
        });
    }

    /**
     * 执行Web应用重启
     */
    private static void performWebApplicationRestart() {
        try {
            System.out.println("开始重启Web应用流程...");

            // 第一步：停止当前Web进程
            System.out.println("步骤1: 停止当前Web服务...");
            stopWebProcess();

            // 等待进程完全停止
            Thread.sleep(3000);

            // 第二步：重新启动Spring Boot应用
            System.out.println("步骤2: 重新启动Web应用...");
            restartSpringBootApplication();

            System.out.println("Web应用重启完成");

            SwingUtilities.invokeLater(() -> {
                JOptionPane.showMessageDialog(
                        null,
                        "Web应用重启成功！\n服务已重新启动，请稍候访问。",
                        "重启成功",
                        JOptionPane.INFORMATION_MESSAGE
                );
            });

        } catch (Exception e) {
            System.err.println("执行Web应用重启时发生错误: " + e.getMessage());
            throw new RuntimeException("重启失败", e);
        }
    }

    /**
     * 重新启动Spring Boot应用
     */
    private static void restartSpringBootApplication() {
        try {
            // 获取当前工作目录
            String basePath = getBasePath();
            File exeFile = new File(basePath, "端智AI助手.exe");

            if (!exeFile.exists()) {
                // 如果exe文件不存在，尝试使用JAR方式重启
                System.out.println("未找到exe文件，尝试JAR方式重启...");
                restartJarApplication();
                return;
            }

            System.out.println("找到exe文件: " + exeFile.getAbsolutePath());

            // 构建重启命令
            ProcessBuilder pb = new ProcessBuilder(exeFile.getAbsolutePath());

            // 设置工作目录
            pb.directory(new File(basePath));

            // 启动新进程
            Process process = pb.start();

            System.out.println("新的Web应用进程已启动 (EXE模式)");

            // 等待一段时间确保新进程启动
            Thread.sleep(2000);

            // 退出当前进程
            System.out.println("当前进程即将退出...");
            System.exit(0);

        } catch (Exception e) {
            System.err.println("重新启动EXE应用时发生错误: " + e.getMessage());
            throw new RuntimeException("重启EXE应用失败", e);
        }
    }

    /**
     * JAR方式重启（备用方案）
     */
    private static void restartJarApplication() {
        try {
            // 获取当前JAR文件路径
            String jarPath = System.getProperty("java.class.path");
            String javaHome = System.getProperty("java.home");
            String javaBin = javaHome + File.separator + "bin" + File.separator + "java";

            // 构建重启命令
            ProcessBuilder pb = new ProcessBuilder(
                    javaBin,
                    "-jar",
                    jarPath
            );

            // 设置工作目录
            pb.directory(new File(getBasePath()));

            // 重定向输出
            pb.inheritIO();

            // 启动新进程
            Process process = pb.start();

            System.out.println("新的Web应用进程已启动 (JAR模式)");

            // 退出当前进程
            System.exit(0);

        } catch (Exception e) {
            System.err.println("重新启动JAR应用时发生错误: " + e.getMessage());
            throw new RuntimeException("重启JAR应用失败", e);
        }
    }

}