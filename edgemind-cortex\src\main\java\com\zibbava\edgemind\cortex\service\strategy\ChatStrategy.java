package com.zibbava.edgemind.cortex.service.strategy;

import com.zibbava.edgemind.cortex.dto.ChatContext;
import com.zibbava.edgemind.cortex.entity.ChatMessage;
import reactor.core.publisher.Flux;

/**
 * 聊天策略接口 - 定义不同类型聊天的通用流程
 */
public interface ChatStrategy {
    
    /**
     * 检查用户权限
     * @param context 聊天上下文
     * @return 是否有权限访问
     */
    boolean checkPermission(ChatContext context);
    
    /**
     * 准备对话上下文
     * @param context 聊天上下文
     * @return 准备好的上下文
     */
    ChatContext prepareContext(ChatContext context);
    
    /**
     * 构建提示词
     * @param context 聊天上下文
     * @return 构建好的提示词
     */
    String buildPrompt(ChatContext context);
    
    /**
     * 执行聊天
     * @param context 聊天上下文
     * @return 聊天结果流
     */
    Flux<String> executeChat(ChatContext context);
    
    /**
     * 保存聊天记录
     * @param context 聊天上下文
     * @param userContent 用户内容
     * @param aiContent AI内容
     * @param thinkingContent 思考内容
     * @return 保存的AI消息
     */
    ChatMessage saveChatHistory(ChatContext context, String userContent, String aiContent, String thinkingContent);
    
    /**
     * 检查策略是否适用于当前请求
     * @param context 聊天上下文
     * @return 是否适用
     */
    boolean isApplicable(ChatContext context);
}
