package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.exception.NotLoginException;
import com.zibbava.edgemind.cortex.common.exception.AccessDeniedException;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.common.exception.ResourceNotFoundException;
import com.zibbava.edgemind.cortex.dto.UnifiedChatRequest;
import com.zibbava.edgemind.cortex.service.UnifiedChatService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

/**
 * 统一聊天接口 API
 */
@RestController
@RequestMapping("/api/unified-chat")
@RequiredArgsConstructor
@Slf4j
public class UnifiedChatController {

    private final UnifiedChatService unifiedChatService;

    /**
     * 处理统一的流式聊天请求
     * 支持混合检索（Dense + BM25）的知识库问答
     *
     * @param request 聊天请求对象
     * @return SSE 事件流
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> streamChat(@RequestBody @Valid UnifiedChatRequest request) {
        String searchInfo = request.getKnowledgeNodeId() != null ? 
                "（支持混合检索：语义搜索 + 关键词匹配）" : "";
        
        log.info("收到统一聊天请求: prompt={}, model={}, knowledgeNodeId={} {}",
                request.getPrompt(), request.getModel(), request.getKnowledgeNodeId(), searchInfo);

        try {
            // 调用统一服务处理
            return unifiedChatService.chat(request)
                    .onErrorResume(this::handleStreamError); // 添加统一的流错误处理
        } catch (Exception e) {
            // 捕获 Controller 层的即时异常 (如参数绑定、校验异常)
            return handleStreamError(e);
        }
    }

    /**
     * 处理流处理过程中的各类异常，并转换为适合 SSE 的错误事件
     */
    private Flux<String> handleStreamError(Throwable e) {
        log.error("统一聊天流错误: {}", e.getMessage(), e);

        String errorMessage;
        if (e instanceof NotLoginException) {
            NotLoginException nle = (NotLoginException) e;
            switch (nle.getType()) {
                case NotLoginException.NOT_TOKEN: errorMessage = "未提供认证令牌，请重新登录"; break;
                case NotLoginException.INVALID_TOKEN: errorMessage = "认证令牌无效，请重新登录"; break;
                case NotLoginException.TOKEN_TIMEOUT: errorMessage = "会话已过期，请重新登录"; break;
                default: errorMessage = "会话验证失败，请重新登录"; break;
            }
        } else if (e instanceof AccessDeniedException) {
            errorMessage = "无权访问资源: " + e.getMessage();
        } else if (e instanceof ResourceNotFoundException) {
            errorMessage = "请求的资源不存在: " + e.getMessage();
        } else if (e instanceof BusinessException) {
            BusinessException be = (BusinessException) e;
            errorMessage = "[" + be.getCode() + "] " + be.getDetailMessage();
        } else if (e instanceof IllegalArgumentException) {
            errorMessage = "请求参数错误: " + e.getMessage();
        } else if (e instanceof RuntimeException) { // 检查 RuntimeException 或您定义的通用业务异常
            errorMessage = "业务处理失败: " + e.getMessage();
        } else {
            // 通用错误处理
            errorMessage = "处理聊天请求时发生错误";
        }

        // 返回包含错误信息的单个 SSE 事件
        // 前端应该能识别这种特殊的错误事件格式
        return Flux.just("error: " + errorMessage);
    }
}
