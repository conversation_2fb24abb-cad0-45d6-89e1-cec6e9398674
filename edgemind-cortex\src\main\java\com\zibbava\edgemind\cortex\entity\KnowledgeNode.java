package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.zibbava.edgemind.cortex.common.enums.NodeType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 知识库节点实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("kb_knowledge_nodes")
public class KnowledgeNode {

    /**
     * 节点ID (UUID字符串)
     */
    @TableId(value = "node_id", type = IdType.ASSIGN_UUID)
    private String nodeId;

    /**
     * 所属知识空间 ID
     */
    @TableField("space_id")
    private String spaceId;

    /**
     * 父节点 ID，根节点时为 NULL
     */
    @TableField("parent_node_id")
    private String parentNodeId;

    /**
     * 节点名称
     */
    @TableField("name")
    private String name;

    /**
     * 节点类型: FOLDER, FILE
     */
    @TableField("type")
    @EnumValue // MyBatis-Plus 枚举映射 (通常映射为枚举名字符串)
    private NodeType type;

    /**
     * 创建者用户ID (关联 sys_user.id)
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 最后更新者用户ID (关联 sys_user.id)
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 创建时间 (由数据库或MyBatis-Plus自动填充)
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间 (由数据库或MyBatis-Plus自动填充)
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    // --- 非数据库字段 --- 

    /**
     * 子节点列表 (用于构建或响应树状结构)
     */
    @TableField(exist = false)
    private List<KnowledgeNode> children;
} 