package com.zibbava.edgemind.cortex.manager;

import com.onlyoffice.manager.settings.DefaultSettingsManager;
import com.onlyoffice.model.settings.SettingsConstants;
import com.zibbava.edgemind.cortex.config.OnlyOfficeConfig;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * 设置管理器实现
 * 管理ONLYOFFICE相关配置
 */
@Component
public class SettingsManagerImpl extends DefaultSettingsManager {
    private static Properties properties;

    static {
        properties = new Properties();
    }

    public SettingsManagerImpl(OnlyOfficeConfig config) {
        // 设置ONLYOFFICE服务器URL
        properties.put(SettingsConstants.URL, config.getServerUrl());
        // 设置安全密钥
        properties.put(SettingsConstants.SECURITY_KEY, config.getSecurityKey());
        
        // 语言和区域设置
        properties.put("languages", config.getLanguage());
        properties.put("region", config.getRegion());
        
        // 编辑器设置
        properties.put("mode", config.getMode());
        properties.put("type", config.getType());
        
        // 共同编辑设置
        properties.put("coEditing.mode", config.getCoEditingMode());
        properties.put("coEditing.change", String.valueOf(config.isCoEditingModeChangeable()));
        
        // 权限设置
        properties.put("permissions.comment", String.valueOf(config.isShowComments()));
        properties.put("permissions.chat", String.valueOf(config.isShowChat()));
        
        // 界面自定义
        properties.put("customization.compactToolbar", String.valueOf(config.isCompactToolbar()));
        properties.put("customization.feedback", String.valueOf(config.isShowFeedbackButton()));
        properties.put("customization.help", String.valueOf(config.isShowHelpButton()));
        properties.put("customization.toolbarNoTabs", String.valueOf(config.isHideToolbar()));
        properties.put("customization.statusBar", String.valueOf(!config.isHideStatusbar()));
        properties.put("customization.hideRightMenu", String.valueOf(config.isHideRightMenu()));
        
        // 其他通用设置
        properties.put("verify-peer-off", "false");
        properties.put("timeout", "120000");
    }

    @Override
    public String getSetting(final String name) {
        return properties.getProperty(name);
    }

    @Override
    public void setSetting(final String name, final String value) {
        properties.setProperty(name, value);
    }
} 