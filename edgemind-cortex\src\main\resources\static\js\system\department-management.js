/**
 * 部门管理页面JavaScript - 使用EdgeMind组件系统
 */

// 全局变量
let deptTree = [];
let allDepts = [];
let allUsers = [];

// 获取上下文路径
const CONTEXT_PATH = window.location.pathname.startsWith('/wkg') ? '/wkg' : '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPage();
});

/**
 * 初始化页面
 */
function initPage() {
    loadDeptTree();
    loadUsers();
    bindEvents();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 可以添加其他事件绑定
}

/**
 * 统一的API请求方法 - 使用EdgeMind标准
 */
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include'
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(`${CONTEXT_PATH}${url}`, finalOptions);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.code !== 200 && !data.success) {
            throw new Error(data.message || '操作失败');
        }

        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

/**
 * 显示Toast提示 - 使用EdgeMind组件
 */
function showToast(message, type = 'info') {
    // 检查是否有EdgeMind的showToast函数
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        // 回退到简单的alert
        alert(`${type.toUpperCase()}: ${message}`);
    }
}

/**
 * 加载部门树
 */
async function loadDeptTree() {
    try {
        const data = await apiRequest('/api/system/dept/tree');
        deptTree = data.data || [];
        allDepts = flattenDeptTree(deptTree);
        renderDeptTree(deptTree);
        updateParentDeptOptions();
    } catch (error) {
        showToast('加载部门树失败: ' + error.message, 'danger');
        // 显示空状态
        document.getElementById('deptTreeContainer').innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
                <p class="mt-2">部门数据加载失败</p>
            </div>
        `;
    }
}

/**
 * 扁平化部门树
 */
function flattenDeptTree(nodes, result = []) {
    nodes.forEach(node => {
        result.push(node);
        if (node.children && node.children.length > 0) {
            flattenDeptTree(node.children, result);
        }
    });
    return result;
}

/**
 * 加载用户列表（用于负责人选择）
 */
async function loadUsers() {
    try {
        const data = await apiRequest('/api/system/user/list?pageSize=1000');
        allUsers = data.data.records || [];
        updateManagerOptions(allUsers);
    } catch (error) {
        console.error('Error loading users:', error);
        showToast('加载用户列表失败', 'warning');
    }
}

/**
 * 渲染部门树
 */
function renderDeptTree(nodes, level = 0) {
    const container = document.getElementById('deptTreeContainer');
    if (level === 0) {
        container.innerHTML = '';
    }

    nodes.forEach(node => {
        const div = document.createElement('div');
        div.className = `dept-node level-${level}`;
        div.setAttribute('data-dept-id', node.id);

        const hasChildren = node.children && node.children.length > 0;
        const expandIcon = hasChildren ?
            `<i class="bi bi-chevron-right expand-icon me-2" onclick="toggleNode(this)" style="cursor: pointer;"></i>` :
            `<span class="me-4"></span>`;

        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-center p-3 rounded mb-2"
                 style="cursor: pointer; transition: all 0.2s; margin-left: ${level * 20}px;
                        background-color: ${level === 0 ? 'var(--primary-light)' : 'white'};
                        border: 1px solid var(--border-color);"
                 onclick="selectDept(${node.id})"
                 onmouseover="this.style.backgroundColor='var(--hover-bg)'"
                 onmouseout="this.style.backgroundColor='${level === 0 ? 'var(--primary-light)' : 'white'}'">
                <div class="d-flex align-items-center flex-grow-1">
                    ${expandIcon}
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-building me-2" style="color: var(--primary-color);"></i>
                            <strong style="color: var(--text-primary);">${node.deptName || node.name}</strong>
                            <span class="badge bg-secondary ms-2" style="font-size: 0.7rem;">${node.deptCode || node.code}</span>
                            ${node.status === 0 ? '<span class="badge bg-danger ms-2" style="font-size: 0.7rem;">禁用</span>' : ''}
                        </div>
                        <div class="mt-1">
                            <small style="color: var(--text-secondary);">
                                <i class="bi bi-people me-1"></i>员工: ${node.userCount || 0}
                                ${node.managerName ? `<i class="bi bi-person-badge ms-2 me-1"></i>负责人: ${node.managerName}` : ''}
                            </small>
                        </div>
                    </div>
                </div>
                <div class="dept-actions" style="opacity: 0; transition: opacity 0.2s;">
                    <button type="button" class="btn btn-sm btn-outline-primary me-1" onclick="event.stopPropagation(); editDept(${node.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success me-1" onclick="event.stopPropagation(); addChildDept(${node.id})" title="添加子部门">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); deleteDept(${node.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;

        // 添加悬停效果显示操作按钮
        div.addEventListener('mouseenter', function() {
            const actions = this.querySelector('.dept-actions');
            if (actions) actions.style.opacity = '1';
        });

        div.addEventListener('mouseleave', function() {
            const actions = this.querySelector('.dept-actions');
            if (actions) actions.style.opacity = '0';
        });
        
        container.appendChild(div);

        if (hasChildren) {
            const childContainer = document.createElement('div');
            childContainer.className = 'dept-children';
            childContainer.style.display = level === 0 ? 'block' : 'none'; // 默认展开第一层
            container.appendChild(childContainer);

            // 递归渲染子节点
            renderDeptTreeChildren(node.children, childContainer, level + 1);
        }
    });
}

/**
 * 渲染部门树子节点
 */
function renderDeptTreeChildren(nodes, container, level) {
    nodes.forEach(node => {
        const div = document.createElement('div');
        div.className = `dept-node level-${level}`;
        div.setAttribute('data-dept-id', node.id);
        
        const hasChildren = node.children && node.children.length > 0;
        const expandIcon = hasChildren ? 
            `<i class="bi bi-chevron-right expand-icon me-2" onclick="toggleNode(this)" style="cursor: pointer;"></i>` : 
            `<span class="me-4"></span>`;
        
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center flex-grow-1" onclick="selectDept(${node.id})">
                    ${expandIcon}
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-building me-2"></i>
                            <strong>${node.deptName}</strong>
                            <span class="badge bg-secondary ms-2">${node.deptCode}</span>
                            ${node.status === 0 ? '<span class="badge bg-danger ms-2">禁用</span>' : ''}
                        </div>
                        <div class="dept-stats mt-1">
                            <small>
                                <i class="bi bi-people me-1"></i>员工: ${node.userCount || 0}
                                ${node.managerName ? `<i class="bi bi-person-badge ms-2 me-1"></i>负责人: ${node.managerName}` : ''}
                            </small>
                        </div>
                    </div>
                </div>
                <div class="dept-actions">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editDept(${node.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="addChildDept(${node.id})" title="添加子部门">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteDept(${node.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;

        container.appendChild(div);

        if (hasChildren) {
            const childContainer = document.createElement('div');
            childContainer.className = 'dept-children';
            childContainer.style.display = 'none';
            container.appendChild(childContainer);

            // 递归渲染子节点
            renderDeptTreeChildren(node.children, childContainer, level + 1);
        }
    });
}

/**
 * 切换节点展开/收起
 */
function toggleNode(icon) {
    const isExpanded = icon.classList.contains('bi-chevron-down');
    const childContainer = icon.closest('.dept-node').nextElementSibling;
    
    if (isExpanded) {
        icon.classList.remove('bi-chevron-down');
        icon.classList.add('bi-chevron-right');
        if (childContainer && childContainer.classList.contains('dept-children')) {
            childContainer.style.display = 'none';
        }
    } else {
        icon.classList.remove('bi-chevron-right');
        icon.classList.add('bi-chevron-down');
        if (childContainer && childContainer.classList.contains('dept-children')) {
            childContainer.style.display = 'block';
        }
    }
}

/**
 * 展开全部节点
 */
function expandAll() {
    const expandIcons = document.querySelectorAll('.expand-icon.bi-chevron-right');
    expandIcons.forEach(icon => {
        toggleNode(icon);
    });
}

/**
 * 收起全部节点
 */
function collapseAll() {
    const expandIcons = document.querySelectorAll('.expand-icon.bi-chevron-down');
    expandIcons.forEach(icon => {
        toggleNode(icon);
    });
}

/**
 * 选择部门
 */
function selectDept(deptId) {
    // 移除之前的选中状�?
    document.querySelectorAll('.dept-node').forEach(node => {
        node.classList.remove('selected');
    });
    
    // 添加选中状�?
    const selectedNode = document.querySelector(`[data-dept-id="${deptId}"]`);
    if (selectedNode) {
        selectedNode.classList.add('selected');
    }
    
    // 加载部门详情
    loadDeptDetail(deptId);
}

/**
 * 加载部门详情
 */
async function loadDeptDetail(deptId) {
    try {
        const data = await apiRequest(`/api/system/dept/${deptId}`);
        renderDeptDetail(data.data);
    } catch (error) {
        console.error('Error:', error);
        showToast('加载部门详情失败: ' + error.message, 'danger');
    }
}

/**
 * 渲染部门详情
 */
function renderDeptDetail(dept) {
    const detailContainer = document.getElementById('deptDetail');
    detailContainer.innerHTML = `
        <div class="mb-3">
            <label class="form-label fw-bold">部门名称</label>
            <div class="form-control-plaintext">${dept.deptName}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">部门编码</label>
            <div class="form-control-plaintext"><code>${dept.deptCode}</code></div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">部门路径</label>
            <div class="form-control-plaintext">${dept.deptPath || '-'}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">部门层级</label>
            <div class="form-control-plaintext">第${dept.deptLevel}级</div>
        </div>
        ${dept.managerName ? `
        <div class="mb-3">
            <label class="form-label fw-bold">部门负责人</label>
            <div class="form-control-plaintext">${dept.managerName}</div>
        </div>
        ` : ''}
        ${dept.phone ? `
        <div class="mb-3">
            <label class="form-label fw-bold">联系电话</label>
            <div class="form-control-plaintext">${dept.phone}</div>
        </div>
        ` : ''}
        ${dept.email ? `
        <div class="mb-3">
            <label class="form-label fw-bold">邮箱</label>
            <div class="form-control-plaintext">${dept.email}</div>
        </div>
        ` : ''}
        ${dept.address ? `
        <div class="mb-3">
            <label class="form-label fw-bold">部门地址</label>
            <div class="form-control-plaintext">${dept.address}</div>
        </div>
        ` : ''}
        <div class="mb-3">
            <label class="form-label fw-bold">状态</label>
            <div class="form-control-plaintext">
                <span class="badge ${dept.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${dept.status === 1 ? '启用' : '禁用'}
                </span>
            </div>
        </div>
        ${dept.description ? `
        <div class="mb-3">
            <label class="form-label fw-bold">部门描述</label>
            <div class="form-control-plaintext">${dept.description}</div>
        </div>
        ` : ''}
        <div class="mb-3">
            <label class="form-label fw-bold">创建时间</label>
            <div class="form-control-plaintext">${formatDateTime(dept.createTime)}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">更新时间</label>
            <div class="form-control-plaintext">${formatDateTime(dept.updateTime)}</div>
        </div>
    `;
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

// showToast函数已移除，使用showToast替代

/**
 * 显示创建部门模态框
 */
function showCreateModal() {
    document.getElementById('deptModalTitle').textContent = '新增部门';
    document.getElementById('deptForm').reset();
    document.getElementById('deptId').value = '';
    document.getElementById('parentId').value = '0';

    const modal = new bootstrap.Modal(document.getElementById('deptModal'));
    modal.show();
}

/**
 * 添加子部门
 */
function addChildDept(parentId) {
    document.getElementById('deptModalTitle').textContent = '新增子部门';
    document.getElementById('deptForm').reset();
    document.getElementById('deptId').value = '';
    document.getElementById('parentId').value = parentId;

    const modal = new bootstrap.Modal(document.getElementById('deptModal'));
    modal.show();
}

/**
 * 编辑部门
 */
async function editDept(deptId) {
    try {
        const data = await apiRequest(`/api/system/dept/${deptId}`);
        const dept = data.data;
        document.getElementById('deptModalTitle').textContent = '编辑部门';
        document.getElementById('deptId').value = dept.id;
        document.getElementById('deptName').value = dept.deptName;
        document.getElementById('deptCode').value = dept.deptCode;
        document.getElementById('parentId').value = dept.parentId || '0';
        document.getElementById('managerId').value = dept.managerId || '';
        document.getElementById('phone').value = dept.phone || '';
        document.getElementById('email').value = dept.email || '';
        document.getElementById('sortOrder').value = dept.sortOrder || 0;
        document.getElementById('status').value = dept.status;
        document.getElementById('address').value = dept.address || '';
        document.getElementById('description').value = dept.description || '';

        const modal = new bootstrap.Modal(document.getElementById('deptModal'));
        modal.show();
    } catch (error) {
        console.error('Error:', error);
        showToast('获取部门信息失败: ' + error.message, 'danger');
    }
}

/**
 * 保存部门
 */
async function saveDept() {
    const deptId = document.getElementById('deptId').value;
    const isEdit = !!deptId;

    // 表单验证
    if (!validateDeptForm()) {
        return;
    }

    const formData = {
        deptName: document.getElementById('deptName').value,
        deptCode: document.getElementById('deptCode').value,
        parentId: parseInt(document.getElementById('parentId').value) || 0,
        managerId: parseInt(document.getElementById('managerId').value) || null,
        phone: document.getElementById('phone').value || null,
        email: document.getElementById('email').value || null,
        sortOrder: parseInt(document.getElementById('sortOrder').value) || 0,
        status: parseInt(document.getElementById('status').value),
        address: document.getElementById('address').value || null,
        description: document.getElementById('description').value || null
    };

    if (isEdit) {
        formData.id = parseInt(deptId);
    }

    try {
        const url = isEdit ? `/api/system/dept/${deptId}` : '/api/system/dept';
        const method = isEdit ? 'PUT' : 'POST';

        const data = await apiRequest(url, {
            method: method,
            body: JSON.stringify(formData)
        });

        if (data.code === 200) {
            showToast(isEdit ? '部门更新成功' : '部门创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('deptModal')).hide();
            loadDeptTree();
        } else {
            showToast((isEdit ? '部门更新失败: ' : '部门创建失败: ') + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('保存失败: ' + error.message, 'danger');
    }
}

/**
 * 删除部门
 */
async function deleteDept(deptId) {
    try {
        // 先检查是否可以删除
        const checkData = await apiRequest(`/api/system/dept/${deptId}/can-delete`);
        if (!checkData.data) {
            showToast('该部门下还有子部门或用户，无法删除', 'warning');
            return;
        }

        if (!confirm('确定要删除这个部门吗？此操作不可恢复。')) {
            return;
        }

        const data = await apiRequest(`/api/system/dept/${deptId}`, {
            method: 'DELETE'
        });

        showToast('部门删除成功', 'success');
        loadDeptTree();
        // 清空详情面板
        document.getElementById('deptDetail').innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="bi bi-building" style="font-size: 3rem;"></i>
                <p class="mt-2">请选择一个部门查看详情</p>
            </div>
        `;
    } catch (error) {
        console.error('Error:', error);
        showToast('删除失败: ' + error.message, 'danger');
    }
}

/**
 * 更新父部门选项
 */
function updateParentDeptOptions() {
    const parentSelect = document.getElementById('parentId');
    const currentOptions = parentSelect.innerHTML;

    // 保留顶级部门选项
    parentSelect.innerHTML = '<option value="0">顶级部门</option>';

    // 添加其他部门作为父部门选项
    allDepts.forEach(dept => {
        const option = document.createElement('option');
        option.value = dept.id;
        option.textContent = dept.deptName;
        parentSelect.appendChild(option);
    });
}

/**
 * 更新负责人选项
 */
function updateManagerOptions(users) {
    const managerSelect = document.getElementById('managerId');
    managerSelect.innerHTML = '<option value="">请选择负责�?/option>';

    users.forEach(user => {
        const option = document.createElement('option');
        option.value = user.id;
        option.textContent = `${user.nickname || user.username} (${user.username})`;
        managerSelect.appendChild(option);
    });
}

/**
 * 表单验证
 */
function validateDeptForm() {
    const deptName = document.getElementById('deptName').value.trim();
    const deptCode = document.getElementById('deptCode').value.trim();
    const email = document.getElementById('email').value.trim();
    const phone = document.getElementById('phone').value.trim();

    if (!deptName) {
        showToast('请输入部门名称', 'warning');
        return false;
    }

    if (!deptCode) {
        showToast('请输入部门编码', 'warning');
        return false;
    }

    // 部门编码格式验证
    if (!/^[A-Z0-9_]+$/.test(deptCode)) {
        showToast('部门编码只能包含大写字母、数字和下划线', 'warning');
        return false;
    }

    // 邮箱格式验证
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        showToast('邮箱格式不正确', 'warning');
        return false;
    }

    // 手机号格式验证
    if (phone && !/^1[3-9]\d{9}$/.test(phone)) {
        showToast('手机号格式不正确', 'warning');
        return false;
    }

    return true;
}

/**
 * 更新父部门选择选项
 */
function updateParentDeptOptions() {
    const parentSelect = document.getElementById('parentId');
    parentSelect.innerHTML = '<option value="0">顶级部门</option>';

    function addOptions(depts, level = 0) {
        depts.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept.id;
            option.textContent = '　'.repeat(level) + dept.deptName;
            parentSelect.appendChild(option);

            if (dept.children && dept.children.length > 0) {
                addOptions(dept.children, level + 1);
            }
        });
    }

    addOptions(deptTree);
}

/**
 * 更新负责人选择选项
 */
function updateManagerOptions(users) {
    const managerSelect = document.getElementById('managerId');
    managerSelect.innerHTML = '<option value="">请选择负责人</option>';

    users.forEach(user => {
        const option = document.createElement('option');
        option.value = user.id;
        option.textContent = `${user.nickname || user.username} (${user.username})`;
        managerSelect.appendChild(option);
    });
}

/**
 * 显示Toast提示 - 使用EdgeMind组件
 */
function showToast(message, type = 'info') {
    // 检查是否有EdgeMind的showToast函数
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        // 回退到简单的alert
        alert(`${type.toUpperCase()}: ${message}`);
    }
}

/**
 * 绑定事件监听器
 */
function bindEvents() {
    // 保存部门按钮
    document.getElementById('saveDeptBtn')?.addEventListener('click', saveDept);

    // 新增部门按钮
    document.getElementById('addDeptBtn')?.addEventListener('click', showCreateModal);

    // 表单提交事件
    document.getElementById('deptForm')?.addEventListener('submit', function(e) {
        e.preventDefault();
        saveDept();
    });
}

/**
 * 切换节点展开/收起
 */
function toggleNode(icon) {
    const isExpanded = icon.classList.contains('bi-chevron-down');
    const deptNode = icon.closest('.dept-node');
    const childContainer = deptNode.nextElementSibling;

    if (childContainer && childContainer.classList.contains('dept-children')) {
        if (isExpanded) {
            // 收起
            icon.classList.remove('bi-chevron-down');
            icon.classList.add('bi-chevron-right');
            childContainer.style.display = 'none';
        } else {
            // 展开
            icon.classList.remove('bi-chevron-right');
            icon.classList.add('bi-chevron-down');
            childContainer.style.display = 'block';
        }
    }
}

/**
 * 选择部门
 */
function selectDept(deptId) {
    // 移除之前的选中状态
    document.querySelectorAll('.dept-node .selected').forEach(el => {
        el.classList.remove('selected');
    });

    // 添加选中状态
    const selectedNode = document.querySelector(`[data-dept-id="${deptId}"]`);
    if (selectedNode) {
        selectedNode.querySelector('div').classList.add('selected');
    }

    // 加载部门详情
    loadDeptDetail(deptId);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initPage);
