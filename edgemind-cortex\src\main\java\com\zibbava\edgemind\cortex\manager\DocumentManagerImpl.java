package com.zibbava.edgemind.cortex.manager;

import com.onlyoffice.manager.document.DefaultDocumentManager;
import com.onlyoffice.manager.settings.SettingsManager;
import com.zibbava.edgemind.cortex.entity.KnowledgeDocument;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.service.KnowledgeBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 文档管理器实现
 * 处理ONLYOFFICE文档的标识、名称等信息
 */
@Component
@Slf4j
public class DocumentManagerImpl extends DefaultDocumentManager {

    private final KnowledgeBaseService knowledgeBaseService;

    public DocumentManagerImpl(final SettingsManager settingsManager, KnowledgeBaseService knowledgeBaseService) {
        super(settingsManager);
        this.knowledgeBaseService = knowledgeBaseService;
    }

    /**
     * 获取文档唯一标识
     * 使用节点ID+文档哈希/更新时间生成唯一标识
     */
    @Override
    public String getDocumentKey(final String nodeId, final boolean embedded) {
        try {
            KnowledgeNode node = knowledgeBaseService.findNodeById(nodeId);
            KnowledgeDocument document = knowledgeBaseService.findDocumentByNodeId(nodeId);

            if (document != null) {
                // 使用内容哈希值作为版本标识(如果可用)
                if (StringUtils.hasText(document.getContentHash())) {
                    return nodeId + "_" + document.getContentHash();
                }
                // 回退到使用节点ID+更新时间
                LocalDateTime updateTime = node.getUpdateTime() != null ? node.getUpdateTime() : node.getCreateTime();
                return nodeId + "_" + (updateTime != null ? updateTime.toString() : "initial");
            }
            return String.valueOf(nodeId.hashCode());
        } catch (Exception e) {
            log.error("获取文档Key失败", e);
            return String.valueOf(nodeId.hashCode()); // 回退方案
        }
    }

    /**
     * 获取文档名称
     * 使用节点名称和文件类型构建完整的文件名
     */
    @Override
    public String getDocumentName(final String nodeId) {
        try {
            // 获取节点信息
            KnowledgeNode node = knowledgeBaseService.findNodeById(nodeId);
            if (node == null) {
                log.warn("无法找到节点: {}", nodeId);
                return "未知文档";
            }

            // 获取文档信息
            KnowledgeDocument document = knowledgeBaseService.findDocumentByNodeId(nodeId);
            if (document == null) {
                log.warn("无法找到文档记录: nodeId={}", nodeId);
                return node.getName(); // 如果没有文档记录，直接返回节点名称
            }

            // 如果节点名称已包含扩展名，直接返回
            return node.getName();
        } catch (Exception e) {
            log.error("获取文档名称失败", e);
            return "未知文档";
        }
    }

}