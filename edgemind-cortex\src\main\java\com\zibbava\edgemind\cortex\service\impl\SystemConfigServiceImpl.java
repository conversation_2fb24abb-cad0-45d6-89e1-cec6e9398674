package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.cortex.entity.SystemConfig;
import com.zibbava.edgemind.cortex.mapper.SystemConfigMapper;
import com.zibbava.edgemind.cortex.service.SystemConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统配置服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SystemConfigServiceImpl extends ServiceImpl<SystemConfigMapper, SystemConfig> implements SystemConfigService {

    private final SystemConfigMapper systemConfigMapper;

    @Override
    @Cacheable(value = "systemConfig", key = "#configKey")
    public String getConfigValue(String configKey) {
        SystemConfig config = systemConfigMapper.selectByConfigKey(configKey);
        return config != null ? config.getConfigValue() : null;
    }

    @Override
    public <T> T getConfigValue(String configKey, Class<T> type) {
        SystemConfig config = systemConfigMapper.selectByConfigKey(configKey);
        return config != null ? config.getValueAs(type) : null;
    }

    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        String value = getConfigValue(configKey);
        return value != null ? value : defaultValue;
    }

    @Override
    public <T> T getConfigValue(String configKey, Class<T> type, T defaultValue) {
        T value = getConfigValue(configKey, type);
        return value != null ? value : defaultValue;
    }

    @Override
    @Transactional
    @CacheEvict(value = "systemConfig", key = "#configKey")
    public void setConfigValue(String configKey, String configValue) {
        setConfigValue(configKey, configValue, null);
    }

    @Override
    @Transactional
    @CacheEvict(value = "systemConfig", key = "#configKey")
    public void setConfigValue(String configKey, String configValue, String description) {
        SystemConfig existingConfig = systemConfigMapper.selectByConfigKey(configKey);
        
        if (existingConfig != null) {
            // 更新现有配置
            existingConfig.setConfigValue(configValue);
            if (StringUtils.hasText(description)) {
                existingConfig.setDescription(description);
            }
            this.updateById(existingConfig);
        } else {
            // 创建新配置
            SystemConfig newConfig = new SystemConfig();
            newConfig.setConfigKey(configKey);
            newConfig.setConfigValue(configValue);
            newConfig.setConfigType(SystemConfig.ConfigType.STRING);
            newConfig.setCategory("custom");
            newConfig.setDescription(description);
            newConfig.setIsSystem(false);
            newConfig.setStatus(SystemConfig.Status.ENABLED);
            
            this.save(newConfig);
        }
    }

    @Override
    @Transactional
    @CacheEvict(value = "systemConfig", allEntries = true)
    public void batchSetConfig(Map<String, String> configs) {
        for (Map.Entry<String, String> entry : configs.entrySet()) {
            setConfigValue(entry.getKey(), entry.getValue());
        }
    }

    @Override
    public List<SystemConfig> getConfigsByCategory(String category) {
        return systemConfigMapper.selectByCategory(category);
    }

    @Override
    public List<SystemConfig> getAllEnabledConfigs() {
        return systemConfigMapper.selectAllEnabled();
    }

    @Override
    public List<SystemConfig> getSystemConfigs() {
        return systemConfigMapper.selectSystemConfigs();
    }

    @Override
    public List<SystemConfig> getUserConfigs() {
        return systemConfigMapper.selectUserConfigs();
    }

    @Override
    @Transactional
    @CacheEvict(value = "systemConfig", key = "#configKey")
    public void deleteConfig(String configKey) {
        LambdaQueryWrapper<SystemConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemConfig::getConfigKey, configKey);
        this.remove(wrapper);
    }

    @Override
    public boolean isConfigExists(String configKey) {
        SystemConfig config = systemConfigMapper.selectByConfigKey(configKey);
        return config != null;
    }

    @Override
    @CacheEvict(value = "systemConfig", allEntries = true)
    public void refreshCache() {
        log.info("系统配置缓存已刷新");
    }

    @Override
    public List<String> getConfigCategories() {
        LambdaQueryWrapper<SystemConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(SystemConfig::getCategory)
               .eq(SystemConfig::getStatus, SystemConfig.Status.ENABLED)
               .groupBy(SystemConfig::getCategory);
        
        List<SystemConfig> configs = this.list(wrapper);
        return configs.stream()
                .map(SystemConfig::getCategory)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<SystemConfig> exportConfigs(String category) {
        if (StringUtils.hasText(category)) {
            return getConfigsByCategory(category);
        } else {
            return getAllEnabledConfigs();
        }
    }

    @Override
    @Transactional
    public ImportResult importConfigs(List<SystemConfig> configs, boolean overwrite) {
        ImportResult result = new ImportResult();
        result.setTotalCount(configs.size());
        result.setErrorMessages(new ArrayList<>());
        
        int successCount = 0;
        int failureCount = 0;
        int skippedCount = 0;
        
        for (SystemConfig config : configs) {
            try {
                boolean exists = isConfigExists(config.getConfigKey());
                
                if (exists && !overwrite) {
                    skippedCount++;
                    continue;
                }
                
                if (exists) {
                    // 更新现有配置
                    SystemConfig existingConfig = systemConfigMapper.selectByConfigKey(config.getConfigKey());
                    existingConfig.setConfigValue(config.getConfigValue());
                    existingConfig.setConfigType(config.getConfigType());
                    existingConfig.setDescription(config.getDescription());
                    this.updateById(existingConfig);
                } else {
                    // 创建新配置
                    config.setId(null); // 确保ID为空，让数据库自动生成
                    config.setStatus(SystemConfig.Status.ENABLED);
                    this.save(config);
                }
                
                successCount++;
            } catch (Exception e) {
                failureCount++;
                result.getErrorMessages().add("配置 " + config.getConfigKey() + " 导入失败: " + e.getMessage());
                log.error("导入配置失败: {}", config.getConfigKey(), e);
            }
        }
        
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setSkippedCount(skippedCount);
        
        // 刷新缓存
        refreshCache();
        
        return result;
    }
}
