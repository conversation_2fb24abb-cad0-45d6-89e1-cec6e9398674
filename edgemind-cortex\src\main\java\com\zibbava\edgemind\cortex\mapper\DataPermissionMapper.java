package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.DataPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据权限 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DataPermissionMapper extends BaseMapper<DataPermission> {

    /**
     * 根据权限ID查询数据权限配置
     * 
     * @param permissionId 权限ID
     * @return 数据权限列表
     */
    @Select("SELECT * FROM sys_data_permission WHERE permission_id = #{permissionId}")
    List<DataPermission> selectByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 根据角色ID查询数据权限配置
     * 
     * @param roleId 角色ID
     * @return 数据权限列表
     */
    @Select("SELECT dp.* FROM sys_data_permission dp " +
            "INNER JOIN sys_role_data_permission rdp ON dp.id = rdp.data_permission_id " +
            "WHERE rdp.role_id = #{roleId}")
    List<DataPermission> selectByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查询数据权限配置
     * 
     * @param userId 用户ID
     * @return 数据权限列表
     */
    @Select("SELECT DISTINCT dp.* FROM sys_data_permission dp " +
            "INNER JOIN sys_role_data_permission rdp ON dp.id = rdp.data_permission_id " +
            "INNER JOIN sys_user_role ur ON rdp.role_id = ur.role_id " +
            "WHERE ur.user_id = #{userId}")
    List<DataPermission> selectByUserId(@Param("userId") Long userId);
}
