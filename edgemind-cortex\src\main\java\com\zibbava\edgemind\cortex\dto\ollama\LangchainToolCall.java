package com.zibbava.edgemind.cortex.dto.ollama;

import com.fasterxml.jackson.databind.ObjectMapper;
import dev.langchain4j.agent.tool.ToolExecutionRequest;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * LangChain4j兼容的工具调用类
 */
@Data
@Builder
@Slf4j
public class LangchainToolCall {
    private String id;                    // 工具调用ID
    private String type;                  // 工具类型，通常是"function"
    private LangchainFunctionCall function;  // 函数调用详情

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 从ToolExecutionRequest创建
     */
    public static LangchainToolCall fromToolExecutionRequest(ToolExecutionRequest request) {
        return LangchainToolCall.builder()
                .id(request.id())
                .type("function")
                .function(LangchainFunctionCall.builder()
                        .name(request.name())
                        .arguments(parseArguments(request.arguments()))
                        .build())
                .build();
    }

    private static Map<String, Object> parseArguments(String arguments) {
        try {
            return objectMapper.readValue(arguments, Map.class);
        } catch (Exception e) {
            log.warn("解析工具参数失败: {}", arguments, e);
            return new HashMap<>();
        }
    }
} 