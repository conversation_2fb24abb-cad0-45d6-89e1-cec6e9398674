package com.zibbava.edgemind.cortex.model;

import java.time.LocalDateTime;

public class WeeklyReport {
    private Long id;
    private String originalContent;
    private String optimizedContent;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private boolean optimizationComplete = false;

    // Constructors
    public WeeklyReport() {
    }

    public WeeklyReport(String originalContent) {
        this.originalContent = originalContent;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOriginalContent() {
        return originalContent;
    }

    public void setOriginalContent(String originalContent) {
        this.originalContent = originalContent;
    }

    public String getOptimizedContent() {
        return optimizedContent;
    }

    public void setOptimizedContent(String optimizedContent) {
        this.optimizedContent = optimizedContent;
        this.updatedAt = LocalDateTime.now();
        this.optimizationComplete = true;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    /**
     * 检查优化是否完成
     * @return 如果优化已完成则返回true
     */
    public boolean isOptimizationComplete() {
        return optimizationComplete;
    }
    
    /**
     * 手动设置优化完成状态
     * @param complete 优化完成状态
     */
    public void setOptimizationComplete(boolean complete) {
        this.optimizationComplete = complete;
    }
} 