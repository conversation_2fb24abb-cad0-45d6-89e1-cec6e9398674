/* license.css - 系统授权页面样式 */

:root {
    --primary-color: #4285f4;
    --primary-light: #e8f0fe;
    --primary-dark: #3367d6;
    --text-primary: #2c3e50;
    --text-secondary: #5f6368;
    --hover-bg: #f5f8ff;
    --active-bg: #e8f0fe;
    --border-color: #e6f0ff;
    --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

body {
    background-color: #f8f9fa;
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Chrome, Safari 和 Opera 的滚动条 */
body::-webkit-scrollbar {
    display: none;
}

.license-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
}

.license-header h2 {
    color: var(--text-primary);
    font-weight: 600;
}

.license-card {
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    transition: box-shadow 0.3s ease;
}

.license-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.license-card .card-header {
    padding: 1.25rem 1.5rem;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.license-card .card-body {
    padding: 1.5rem;
}

.license-card .card-title {
    color: var(--text-primary);
    font-weight: 600;
}

.license-form-label {
    font-weight: 500;
}

.fingerprint-text {
    font-family: monospace;
    letter-spacing: 1px;
}

/* 按钮样式 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    border-color: var(--primary-dark);
}

/* 提示框样式 */
.alert {
    border-radius: 6px;
    border-left-width: 4px;
}

.alert-info {
    border-left-color: var(--primary-color);
    background-color: #f0f7ff;
}

/* 输入框样式 */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .license-container {
        padding: 0 15px;
    }
    
    .license-card .card-header,
    .license-card .card-body {
        padding: 1.25rem;
    }
}
