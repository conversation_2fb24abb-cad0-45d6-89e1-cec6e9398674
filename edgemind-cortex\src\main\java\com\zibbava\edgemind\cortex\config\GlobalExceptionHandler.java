package com.zibbava.edgemind.cortex.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import com.zibbava.edgemind.cortex.common.Result;
import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import com.zibbava.edgemind.cortex.common.exception.AccessDeniedException;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.common.exception.ResourceNotFoundException;
import com.zibbava.edgemind.cortex.exception.ApiException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Set;

/**
 * 全局异常处理器
 * 统一捕获系统异常并转换为标准Result格式返回
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public Result<?> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return Result.error(e);
    }

    /**
     * 处理旧版API异常
     */
    @ExceptionHandler(ApiException.class)
    public Result<?> handleApiException(ApiException e) {
        log.warn("API异常 [{}]: {}", e.getStatus(), e.getMessage());
        // 将HttpStatus映射到合适的ResultCode
        switch (e.getStatus()) {
            case BAD_REQUEST:
                return Result.error(ResultCode.PARAM_ERROR, e.getMessage());
            case UNAUTHORIZED:
                return Result.error(ResultCode.UNAUTHORIZED, e.getMessage());
            case FORBIDDEN:
                return Result.error(ResultCode.FORBIDDEN, e.getMessage());
            case NOT_FOUND:
                return Result.error(ResultCode.RESOURCE_NOT_FOUND, e.getMessage());
            case METHOD_NOT_ALLOWED:
                return Result.error(ResultCode.METHOD_NOT_ALLOWED, e.getMessage());
            default:
                return Result.error(ResultCode.OPERATION_FAILED, e.getMessage());
        }
    }

    /**
     * 处理资源不存在异常
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public Result<?> handleResourceNotFoundException(ResourceNotFoundException e) {
        log.warn("资源不存在: {}", e.getMessage());
        return Result.error(ResultCode.RESOURCE_NOT_FOUND, e.getMessage());
    }

    /**
     * 处理访问拒绝异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<?> handleAccessDeniedException(AccessDeniedException e) {
        log.warn("访问拒绝: {}", e.getMessage());
        return Result.error(ResultCode.FORBIDDEN, e.getMessage());
    }

    /**
     * 处理Sa-Token的未登录异常
     */
    @ExceptionHandler(NotLoginException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Result<?> handleNotLoginException(NotLoginException e) {
        log.warn("未登录: {}", e.getMessage());
        String message;
        switch (e.getType()) {
            case NotLoginException.NOT_TOKEN:
                message = "未提供token";
                break;
            case NotLoginException.INVALID_TOKEN:
                message = "token无效";
                break;
            case NotLoginException.TOKEN_TIMEOUT:
                message = "token已过期";
                break;
            case NotLoginException.BE_REPLACED:
                message = "账号已在其他地方登录";
                break;
            case NotLoginException.KICK_OUT:
                message = "账号已被踢下线";
                break;
            default:
                message = "当前会话未登录";
                break;
        }
        return Result.error(ResultCode.UNAUTHORIZED, message);
    }

    /**
     * 处理Sa-Token的权限不足异常
     */
    @ExceptionHandler(NotPermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<?> handleNotPermissionException(NotPermissionException e) {
        log.warn("权限不足: {}", e.getMessage());
        return Result.error(ResultCode.FORBIDDEN, "权限不足，无法访问该资源");
    }

    /**
     * 处理Sa-Token的角色不足异常
     */
    @ExceptionHandler(NotRoleException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result<?> handleNotRoleException(NotRoleException e) {
        log.warn("角色不足: {}", e.getMessage());
        return Result.error(ResultCode.FORBIDDEN, "角色权限不足，无法访问该资源");
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        // 只返回第一个错误，不返回所有错误
        if (!bindingResult.getFieldErrors().isEmpty()) {
            FieldError fieldError = bindingResult.getFieldErrors().get(0);
            String fieldName = getDisplayName(fieldError.getField());
            String errorMessage = fieldError.getDefaultMessage();
            // 格式化错误信息，不显示字段名括号
            String formattedMessage = errorMessage;
            
            log.warn("参数校验失败: 字段[{}], 错误[{}]", fieldName, errorMessage);
            return Result.error(ResultCode.PARAM_INVALID, formattedMessage);
        }
        
        return Result.error(ResultCode.PARAM_INVALID, "参数校验失败");
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleBindException(BindException e) {
        BindingResult bindingResult = e.getBindingResult();
        // 只返回第一个错误，不返回所有错误
        if (!bindingResult.getFieldErrors().isEmpty()) {
            FieldError fieldError = bindingResult.getFieldErrors().get(0);
            String fieldName = getDisplayName(fieldError.getField());
            String errorMessage = fieldError.getDefaultMessage();
            // 格式化错误信息，不显示字段名括号
            String formattedMessage = errorMessage;
            
            log.warn("参数绑定失败: 字段[{}], 错误[{}]", fieldName, errorMessage);
            return Result.error(ResultCode.PARAM_INVALID, formattedMessage);
        }
        
        return Result.error(ResultCode.PARAM_INVALID, "参数绑定失败");
    }

    /**
     * 将字段名映射为更友好的显示名称
     * @param fieldName 原始字段名
     * @return 显示用的字段名
     */
    private String getDisplayName(String fieldName) {
        // 字段名映射表，可以根据需要扩展
        switch (fieldName) {
            case "name":
                return "节点名称";
            case "spaceId":
                return "空间标识";
            case "parentNodeId":
                return "父节点标识";
            case "type":
                return "节点类型";
            default:
                return "参数"; // 默认不暴露具体字段名，统一显示为"参数"
        }
    }

    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleConstraintViolationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        // 只返回第一个错误，不返回所有错误
        if (!violations.isEmpty()) {
            ConstraintViolation<?> violation = violations.iterator().next();
            // 提取字段名（路径的最后一部分通常是字段名）
            String path = violation.getPropertyPath().toString();
            String fieldName = path.contains(".") ? path.substring(path.lastIndexOf('.') + 1) : path;
            String displayName = getDisplayName(fieldName);
            String errorMessage = violation.getMessage();
            // 格式化错误信息，不显示字段名括号
            String formattedMessage = errorMessage;
            
            log.warn("参数验证失败: 字段[{}], 错误[{}]", displayName, errorMessage);
            return Result.error(ResultCode.PARAM_INVALID, formattedMessage);
        }
        
        return Result.error(ResultCode.PARAM_INVALID, "参数验证失败");
    }

    /**
     * 处理请求参数缺失异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        log.warn("请求参数缺失: {}", e.getMessage());
        return Result.error(ResultCode.PARAM_MISSING, "缺少必要参数: " + e.getParameterName());
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        log.warn("参数类型不匹配: {}", e.getMessage());
        return Result.error(ResultCode.PARAM_INVALID, "参数[" + e.getName() + "]类型不匹配，应为" + e.getRequiredType().getSimpleName());
    }

    /**
     * 处理请求体解析异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        log.warn("请求体解析异常: {}", e.getMessage());
        return Result.error(ResultCode.PARAM_INVALID, "请求体格式不正确");
    }

    /**
     * 处理请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public Result<?> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        log.warn("请求方法不支持: {}", e.getMessage());
        return Result.error(ResultCode.METHOD_NOT_ALLOWED, "不支持" + e.getMethod() + "请求方法");
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.warn("上传文件大小超限: {}", e.getMessage());
        return Result.error(ResultCode.FILE_SIZE_EXCEED, "上传文件过大");
    }

    /**
     * 处理SQL异常
     */
    @ExceptionHandler(SQLException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleSQLException(SQLException e) {
        log.error("数据库异常", e);
        return Result.error(ResultCode.DATABASE_ERROR, "数据库操作异常");
    }

    /**
     * 处理IO异常
     */
    @ExceptionHandler(IOException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleIOException(IOException e) {
        log.error("IO异常", e);
        return Result.error(ResultCode.SYSTEM_ERROR, "文件读写异常");
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常", e);
        return Result.error(ResultCode.SYSTEM_ERROR, "系统运行异常");
    }

    /**
     * 处理未知异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleException(Exception e) {
        log.error("未知异常", e);
        return Result.error(ResultCode.UNKNOWN_ERROR, "系统发生未知异常");
    }
} 