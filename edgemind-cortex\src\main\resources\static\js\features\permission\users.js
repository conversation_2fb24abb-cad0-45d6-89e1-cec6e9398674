// src/main/resources/static/js/features/permission/users.js

document.addEventListener('DOMContentLoaded', () => {
    

    const userTableBody = document.getElementById('userTableBody');
    const paginationContainer = document.getElementById('paginationContainer');
    const addUserModalElement = document.getElementById('addUserModal');
    const addUserModal = new bootstrap.Modal(addUserModalElement);
    const editUserModalElement = document.getElementById('editUserModal');
    const editUserModal = new bootstrap.Modal(editUserModalElement);
    const assignRoleModalElement = document.getElementById('assignRoleModal'); // Assign Role Modal element
    const assignRoleModal = new bootstrap.Modal(assignRoleModalElement); // Assign Role Modal instance
    const resetPasswordModalElement = document.getElementById('resetPasswordModal'); // Reset Password Modal element
    const resetPasswordModal = new bootstrap.Modal(resetPasswordModalElement); // Reset Password Modal instance

    const addUserForm = document.getElementById('addUserForm');
    const editUserForm = document.getElementById('editUserForm');
    const assignRoleForm = document.getElementById('assignRoleForm');
    const resetPasswordForm = document.getElementById('resetPasswordForm');

    // Search elements
    const searchInput = document.getElementById('userSearchInput'); 
    const searchButton = document.getElementById('userSearchButton');

    let currentPage = 1;
    const pageSize = 10;
    let currentFilters = {};

    // --- Function to fetch user data --- 
    async function fetchUsers(page = 1, filters = {}) {
        currentPage = page;
        currentFilters = filters;
        const params = new URLSearchParams({
            current: page,
            size: pageSize,
            ...filters
        });
        userTableBody.innerHTML = `<tr><td colspan="9" class="text-center p-5"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div></td></tr>`;
        paginationContainer.innerHTML = '<span class="text-muted small">Loading...</span>';
        try {
            const response = await fetch(`/wkg/api/permission/users?${params.toString()}`);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const data = await response.json();
            renderTable(data.records || []);
            renderPagination(data);
        } catch (error) {
            console.error('Error fetching users:', error);
            showToast('Error fetching user list.', 'error');
            userTableBody.innerHTML = '<tr><td colspan="9" class="text-center text-danger">Failed to load users.</td></tr>';
            paginationContainer.innerHTML = '<span class="text-danger small">Failed to load</span>';
        }
    }

    // --- Function to render the user table --- 
    function renderTable(users) {
        userTableBody.innerHTML = '';
        if (!users || users.length === 0) {
            userTableBody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">No users found.</td></tr>';
            return;
        }
        users.forEach(user => {
            const row = document.createElement('tr');
            const statusBadgeClass = user.status === 1 ? 'bg-success-subtle text-success-emphasis' : 'bg-danger-subtle text-danger-emphasis';
            const statusText = user.status === 1 ? '启用' : '禁用';
            const rolesHtml = user.roles && user.roles.length > 0 
                ? user.roles.map(role => `<span class="badge bg-secondary-subtle text-secondary-emphasis me-1">${role.roleName || 'N/A'}</span>`).join('')
                : '<span class="text-muted small">-</span>';
            const departmentName = user.department ? user.department.deptName : '<span class="text-muted small">-</span>';
            row.innerHTML = `
                <td class="ps-3"><input type="checkbox" class="form-check-input user-checkbox" data-user-id="${user.id}"></td>
                <td>${user.username || '-'}</td>
                <td>${user.nickname || '-'}</td>
                <td>${departmentName}</td>
                <td>${rolesHtml}</td>
                <td>${user.email || '-'}</td>
                <td><span class="badge ${statusBadgeClass}">${statusText}</span></td>
                <td><small>${user.createTime ? new Date(user.createTime).toLocaleString() : '-'}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary btn-edit" data-user-id="${user.id}" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-delete" data-user-id="${user.id}" data-user-name="${user.username}" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-reset-pwd" data-user-id="${user.id}" title="重置密码">
                            <i class="bi bi-key"></i>
                        </button>
                         <button class="btn btn-outline-info btn-assign-role" data-user-id="${user.id}" title="分配角色">
                            <i class="bi bi-person-check"></i>
                        </button>
                    </div>
                </td>
            `;
            userTableBody.appendChild(row);
        });
        addTableButtonListeners(); 
    }

    // --- Function to render pagination --- 
    function renderPagination(pageData) {
        paginationContainer.innerHTML = '';
        const { current, pages, total } = pageData;
        if (!pages || pages <= 0) {
             paginationContainer.innerHTML = `<span class="text-muted small">无用户数据</span>`;
            return;
        }
         if (pages === 1 && total > 0) {
             paginationContainer.innerHTML = `<span class="text-muted small">共 ${total} 条记录</span>`;
             return;
        }
         if (pages === 1 && total === 0) {
              paginationContainer.innerHTML = `<span class="text-muted small">无用户数据</span>`;
              return;
         }
        let paginationHtml = `<span class="text-muted small me-3">共 ${total} 条记录</span>`;
        paginationHtml += '<ul class="pagination pagination-sm mb-0">';
        paginationHtml += `
            <li class="page-item ${current === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${current - 1}" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>
            </li>`;
        const maxPagesToShow = 5;
        const delta = Math.floor(maxPagesToShow / 2);
        let startPage = Math.max(1, current - delta);
        let endPage = Math.min(pages, current + delta);
         if (current - delta <= 1) {
             endPage = Math.min(pages, maxPagesToShow);
         }
         if (current + delta >= pages) {
             startPage = Math.max(1, pages - maxPagesToShow + 1);
         }
        if (startPage > 1) {
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>`;
            if (startPage > 2) {
                 paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `<li class="page-item ${i === current ? 'active' : ''}"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
        }
        if (endPage < pages) {
             if (endPage < pages - 1) {
                 paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
             }
            paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${pages}">${pages}</a></li>`;
        }
        paginationHtml += `
            <li class="page-item ${current === pages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${current + 1}" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>
            </li>`;
        paginationHtml += '</ul>';
        paginationContainer.innerHTML = paginationHtml;
        paginationContainer.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const pageNum = parseInt(link.dataset.page);
                if (!isNaN(pageNum) && pageNum !== currentPage) {
                    fetchUsers(pageNum, currentFilters);
                }
            });
        });
    }

     // --- Function to fetch data for modal dropdowns --- 
    async function fetchModalData() {

        try {
    
            const [deptResponse, roleResponse] = await Promise.all([
                fetch('/wkg/api/permission/departments'),
                fetch('/wkg/api/permission/roles/get/all')
            ]);
    
    
            
            if (!deptResponse.ok || !roleResponse.ok) {
                console.error('Error fetching modal data:', deptResponse.status, roleResponse.status);
                showToast('Failed to load data for form.', 'error');
                return null;
            }
            
            const departments = await deptResponse.json();
            const roles = await roleResponse.json();
            
    
    
            
            return { departments, roles };
        } catch (error) {
            console.error('Error fetching modal data:', error);
            showToast('Failed to load data for form.', 'error');
            return null;
        }
    }

    // --- Function to populate select options --- 
    function populateSelect(selectElementId, options, valueField = 'id', textField = 'name', descriptionField = null) {

        const selectElement = document.getElementById(selectElementId);
        if (!selectElement) {
            console.error(`找不到选择器元素: #${selectElementId}`);
            return;
        }
        
        const label = document.querySelector(`label[for='${selectElementId}']`);
        const placeholderText = label ? `请选择${label.innerText.replace('*','').trim()}` : '请选择';
        selectElement.innerHTML = `<option value="">${placeholderText}</option>`;
        
        if (!options || !Array.isArray(options) || options.length === 0) {
            console.warn(`${selectElementId} 没有可用选项数据`);
            return;
        }
        
        try {
            options.forEach((option, index) => {
                if (!option || typeof option !== 'object') {
                    console.warn(`选项 #${index} 无效:`, option);
                    return;
                }
                
                const optionElement = document.createElement('option');
                optionElement.value = option[valueField] !== undefined ? option[valueField] : '';
                if (optionElement.value === '') {
                    console.warn(`选项 #${index} 的 ${valueField} 值为空:`, option);
                }
                
                let text = option[textField] || `未命名选项 (ID: ${option[valueField] || '?'})`;
                if (descriptionField && option[descriptionField]) {
                     text += ` (${option[descriptionField]})`;
                }
                optionElement.textContent = text;
                selectElement.appendChild(optionElement);
            });
    
        } catch (error) {
            console.error(`填充 ${selectElementId} 选项时出错:`, error);
        }
    }

    // --- Function to add listeners to table action buttons --- 
    function addTableButtonListeners() {
        userTableBody.querySelectorAll('.btn-edit').forEach(button => {
            button.addEventListener('click', handleEditClick);
        });
        userTableBody.querySelectorAll('.btn-delete').forEach(button => {
            button.addEventListener('click', handleDeleteClick);
        });
        userTableBody.querySelectorAll('.btn-reset-pwd').forEach(button => {
            button.addEventListener('click', handleResetPasswordClick);
        });
        userTableBody.querySelectorAll('.btn-assign-role').forEach(button => {
            button.addEventListener('click', handleAssignRoleClick);
        });
    }

    // --- Event Handlers --- 
    async function handleEditClick(event) { 
        const userId = event.currentTarget.dataset.userId;

        editUserForm.reset();
        editUserForm.classList.remove('was-validated');
        try {
            const userResponse = await fetch(`/wkg/api/permission/users/${userId}`);
            if (!userResponse.ok) {
                 if (userResponse.status === 404) throw new Error(`用户 ID ${userId} 未找到.`);
                 throw new Error(`获取用户详情失败: ${userResponse.status}`);
            }
            const userData = await userResponse.json();
            const modalData = await fetchModalData(); 
            if (!modalData || !modalData.departments) {
                 throw new Error('加载编辑所需的部门列表失败');
            }
            document.getElementById('editUserIdField').value = userData.id;
            document.getElementById('editUsername').value = userData.username || '';
            document.getElementById('editNickname').value = userData.nickname || '';
            document.getElementById('editEmail').value = userData.email || '';
            document.getElementById('editPhone').value = userData.phone || '';
            document.getElementById('editRemark').value = userData.remark || '';
            populateSelect('editDeptId', modalData.departments, 'id', 'deptName');
            document.getElementById('editDeptId').value = userData.deptId || ''; 
            if (userData.status === 0) {
                document.getElementById('editStatusDisabled').checked = true;
            } else {
                document.getElementById('editStatusEnabled').checked = true;
            }
            editUserModal.show();
        } catch (error) {
            console.error('Error preparing edit modal:', error);
            showToast(`错误: ${error.message}`, 'error');
        }
    }

    async function handleDeleteClick(event) {
        const userId = event.currentTarget.dataset.userId;
        const username = event.currentTarget.dataset.userName;

        if (!confirm(`确定要删除用户 "${username}" 吗？此操作无法撤销。`)) {
            return;
        }
        event.currentTarget.disabled = true;
        event.currentTarget.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
        try {
            const response = await fetch(`/wkg/api/permission/users/${userId}`, {
                method: 'DELETE',
                headers: { /* 'Sa-Token': ... */ }
            });
            if (response.ok) {
                showToast(`用户 "${username}" 删除成功!`, 'success');
                fetchUsers(currentPage, currentFilters);
            } else {
                 const errorData = await response.json().catch(() => ({ message: '删除失败，请重试' }));
                 console.error("Delete user error:", errorData);
                 showToast(`错误: ${errorData.message || response.statusText}`, 'error');
                 event.currentTarget.disabled = false;
                 event.currentTarget.innerHTML = '<i class="bi bi-trash"></i>';
            }
        } catch (error) {
            console.error('Error deleting user:', error);
            showToast('发生意外错误，请稍后重试', 'error');
             event.currentTarget.disabled = false;
             event.currentTarget.innerHTML = '<i class="bi bi-trash"></i>';
        }
    }

    function handleResetPasswordClick(event) { 
        const userId = event.currentTarget.dataset.userId;
        const userRow = event.currentTarget.closest('tr');
        const username = userRow?.cells[1]?.textContent || `User ${userId}`;

        const resetPasswordModalElement = document.getElementById('resetPasswordModal');
        const resetPasswordModalInstance = bootstrap.Modal.getInstance(resetPasswordModalElement) || new bootstrap.Modal(resetPasswordModalElement);
        const userNameSpan = document.getElementById('resetPasswordUserName');
        const hiddenUserIdInput = document.getElementById('resetPasswordUserId');
        const form = document.getElementById('resetPasswordForm');
        userNameSpan.textContent = username;
        hiddenUserIdInput.value = userId;
        form.reset();
        form.classList.remove('was-validated');
        form.querySelectorAll('input[type="password"]').forEach(input => input.setCustomValidity(''));
        resetPasswordModalInstance.show();
    }

    async function handleAssignRoleClick(event) {
        const userId = event.currentTarget.dataset.userId;
        const userRow = event.currentTarget.closest('tr');
        const username = userRow?.cells[1]?.textContent || `User ${userId}`;

        const assignRoleModalElement = document.getElementById('assignRoleModal');
        const assignRoleModalInstance = bootstrap.Modal.getInstance(assignRoleModalElement) || new bootstrap.Modal(assignRoleModalElement);
        const userNameSpan = document.getElementById('assignRoleUserName');
        const checkboxContainer = document.getElementById('assignRoleCheckboxContainer');
        const hiddenUserIdInput = document.getElementById('assignRoleUserId');
        userNameSpan.textContent = username;
        hiddenUserIdInput.value = userId;
        checkboxContainer.innerHTML = `<div class="text-center text-muted p-3"><div class="spinner-border spinner-border-sm" role="status"><span class="visually-hidden">Loading...</span></div></div>`; 
        assignRoleModalInstance.show();
        try {
            const [rolesResponse, userRolesResponse] = await Promise.all([
                fetch('/wkg/api/permission/roles/get/all'),
                fetch(`/wkg/api/permission/users/${userId}/roles`)
            ]);
            if (!rolesResponse.ok || !userRolesResponse.ok) {
                 throw new Error('Failed to load role data.');
            }
            const allRoles = await rolesResponse.json();
            const currentUserRoleIds = await userRolesResponse.json();
            checkboxContainer.innerHTML = ''; 
            if (!allRoles || allRoles.length === 0) {
                checkboxContainer.innerHTML = '<p class="text-muted text-center">No roles available.</p>';
                return;
            }
            allRoles.forEach(role => {
                const isChecked = currentUserRoleIds.includes(role.id);
                const div = document.createElement('div');
                div.classList.add('form-check');
                div.innerHTML = `
                    <input class="form-check-input" type="checkbox" value="${role.id}" id="role-${role.id}" name="roleIds" ${isChecked ? 'checked' : ''}>
                    <label class="form-check-label" for="role-${role.id}">
                        ${role.roleName || 'Unnamed Role'} 
                        <small class="text-muted">(${role.roleCode || 'NO_CODE'})</small>
                    </label>
                `;
                checkboxContainer.appendChild(div);
            });
            // Ensure save button is enabled after loading
            assignRoleModalElement.querySelector('button[type="submit"]').disabled = false;
        } catch (error) {
            console.error('Error preparing assign role modal:', error);
            showToast(`Error loading roles: ${error.message}`, 'error');
            checkboxContainer.innerHTML = '<p class="text-danger text-center">Failed to load roles.</p>';
            assignRoleModalElement.querySelector('button[type="submit"]').disabled = true;
        }
    }

    // --- Handle Add User Modal Show --- 
    addUserModalElement.addEventListener('show.bs.modal', async event => {
        addUserForm.reset();
        addUserForm.classList.remove('was-validated');
        const modalData = await fetchModalData();
        if (modalData) {
            populateSelect('addDeptId', modalData.departments, 'id', 'deptName');
            populateSelect('addRoleIds', modalData.roles, 'id', 'roleName', 'roleCode');
        } else {
             showToast('Could not load departments or roles.', 'error');
        }
    });

    // --- Add User Form Submission --- 
    addUserForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        event.stopPropagation();

        
        const passwordInput = document.getElementById('addPassword');
        const confirmPasswordInput = document.getElementById('addConfirmPassword');
        confirmPasswordInput.setCustomValidity('');
        if (passwordInput.value !== confirmPasswordInput.value) {
            confirmPasswordInput.setCustomValidity('两次输入的密码不匹配');
            console.warn("密码不匹配");
        }
        if (!addUserForm.checkValidity()) {
             addUserForm.classList.add('was-validated');
             const firstInvalidField = addUserForm.querySelector(':invalid');
             console.warn("表单验证失败，第一个无效字段:", firstInvalidField?.id);
             firstInvalidField?.focus();
             firstInvalidField?.reportValidity(); 
             return;
        }
        addUserForm.classList.add('was-validated');
        const formData = new FormData(addUserForm);
        const userData = {};
        const roleIds = [];
        for (const [key, value] of formData.entries()) {
             if (key === 'roleIds') {
                 const selectedOptions = Array.from(document.getElementById('addRoleIds').selectedOptions);
                 selectedOptions.forEach(opt => roleIds.push(parseInt(opt.value)));
     
             } else {
                 userData[key] = value;
     
             }
         }
         if (formData.has('roleIds')) {
              userData.roleIds = [...new Set(roleIds)];
         } else {
            userData.roleIds = [];
            console.warn("未选择任何角色");
         }
        userData.status = parseInt(formData.get('status') || '1');
        userData.deptId = formData.get('deptId') ? parseInt(formData.get('deptId')) : null;
        

        
        const submitButton = addUserForm.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
        try {
    
            const response = await fetch('/wkg/api/permission/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    // 'Sa-Token': StpUtil?.getTokenValue?.() || '' 
                },
                body: JSON.stringify(userData),
            });
    
            
            if (response.ok) {
    
                addUserModal.hide();
                showToast('用户添加成功!', 'success');
                fetchUsers(1);
            } else {
                try {
                    const errorData = await response.json();
                    console.error("请求失败，错误数据:", errorData);
                    showToast(`错误: ${errorData.message || response.statusText}`, 'error');
                } catch (jsonError) {
                    const errorText = await response.text().catch(() => '无响应内容');
                    console.error("请求失败，无法解析JSON, 原始响应:", errorText);
                    console.error("解析JSON错误:", jsonError);
                    showToast(`添加用户失败: ${response.statusText}`, 'error');
                }
            }
        } catch (error) {
            console.error('Error submitting new user:', error);
            showToast('发生意外错误，请稍后重试', 'error');
        } finally {
            submitButton.disabled = false;
            submitButton.innerHTML = '保存';
        }
    });

    // --- Edit User Form Submission --- 
    editUserForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        event.stopPropagation();
        if (!editUserForm.checkValidity()) {
            editUserForm.classList.add('was-validated');
            const firstInvalidField = editUserForm.querySelector(':invalid');
            firstInvalidField?.focus();
            firstInvalidField?.reportValidity();
            return;
        }
        editUserForm.classList.add('was-validated');
        const formData = new FormData(editUserForm);
        const userData = {};
        for (const [key, value] of formData.entries()) {
            userData[key] = value;
        }
        userData.id = parseInt(userData.id);
        userData.status = parseInt(formData.get('status') || '1');
        userData.deptId = formData.get('deptId') ? parseInt(formData.get('deptId')) : null;
        const submitButton = editUserForm.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
        try {
            const response = await fetch(`/wkg/api/permission/users/${userData.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    // 'Sa-Token': StpUtil?.getTokenValue?.() || ''
                },
                body: JSON.stringify(userData),
            });
            if (response.ok) {
                editUserModal.hide();
                showToast('用户信息更新成功!', 'success');
                fetchUsers(currentPage, currentFilters);
            } else {
                const errorData = await response.json().catch(() => ({ message: '更新失败，请检查输入' }));
                console.error("Update user error:", errorData);
                showToast(`错误: ${errorData.message || response.statusText}`, 'error');
            }
        } catch (error) {
            console.error('Error submitting user update:', error);
            showToast('发生意外错误，请稍后重试', 'error');
        } finally {
            submitButton.disabled = false;
            submitButton.innerHTML = '保存更改';
        }
    });

     // --- Assign Role Form Submission --- 
    assignRoleForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        event.stopPropagation();
        const userId = document.getElementById('assignRoleUserId').value;
        if (!userId) {
            showToast('Error: User ID not found.', 'error');
            return;
        }
        const selectedRoleIds = Array.from(assignRoleForm.querySelectorAll('input[name="roleIds"]:checked'))
                                     .map(checkbox => parseInt(checkbox.value));

        const submitButton = assignRoleForm.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
        try {
            const response = await fetch(`/wkg/api/permission/users/${userId}/roles`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    // 'Sa-Token': StpUtil?.getTokenValue?.() || ''
                },
                body: JSON.stringify(selectedRoleIds),
            });
             const assignRoleModalElement = document.getElementById('assignRoleModal');
             const assignRoleModalInstance = bootstrap.Modal.getInstance(assignRoleModalElement);
            if (response.ok) {
                if(assignRoleModalInstance) assignRoleModalInstance.hide();
                showToast('用户角色分配成功!', 'success');
                fetchUsers(currentPage, currentFilters);
            } else {
                 const errorData = await response.json().catch(() => ({ message: '分配角色失败，请重试' }));
                 console.error("Assign roles error:", errorData);
                 showToast(`错误: ${errorData.message || response.statusText}`, 'error');
            }
        } catch (error) {
            console.error('Error assigning roles:', error);
            showToast('发生意外错误，请稍后重试', 'error');
        } finally {
            submitButton.disabled = false;
            submitButton.innerHTML = '保存分配';
        }
    });

    // --- Reset Password Form Submission ---
    resetPasswordForm.addEventListener('submit', async (event) => {
        event.preventDefault();
        event.stopPropagation();
        const userId = document.getElementById('resetPasswordUserId').value;
        const newPasswordInput = document.getElementById('resetNewPassword');
        const confirmPasswordInput = document.getElementById('resetConfirmPassword');
        newPasswordInput.setCustomValidity('');
        confirmPasswordInput.setCustomValidity('');
        if (newPasswordInput.value !== confirmPasswordInput.value) {
            confirmPasswordInput.setCustomValidity('两次输入的密码不匹配');
        }
        if (!resetPasswordForm.checkValidity()) {
            resetPasswordForm.classList.add('was-validated');
            const firstInvalidField = resetPasswordForm.querySelector(':invalid');
            firstInvalidField?.focus();
            firstInvalidField?.reportValidity();
            return;
        }
        resetPasswordForm.classList.add('was-validated');

        const submitButton = resetPasswordForm.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 重置中...';
        try {
            const response = await fetch(`/wkg/api/permission/users/${userId}/reset-password`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    // 'Sa-Token': StpUtil?.getTokenValue?.() || ''
                },
                body: JSON.stringify({ newPassword: newPasswordInput.value }),
            });
             const resetPasswordModalElement = document.getElementById('resetPasswordModal');
             const resetPasswordModalInstance = bootstrap.Modal.getInstance(resetPasswordModalElement);
            if (response.ok) {
                 if(resetPasswordModalInstance) resetPasswordModalInstance.hide();
                showToast('密码重置成功!', 'success');
            } else {
                 const errorData = await response.json().catch(() => ({ message: '密码重置失败，请重试' }));
                 console.error("Reset password error:", errorData);
                 showToast(`错误: ${errorData.message || response.statusText}`, 'error');
            }
        } catch (error) {
            console.error('Error resetting password:', error);
            showToast('发生意外错误，请稍后重试', 'error');
        } finally {
            submitButton.disabled = false;
            submitButton.innerHTML = '确认重置';
        }
    });

    // --- Search and Filter Event Listeners ---
    function applyFilters() {
        currentFilters = {
            username: searchInput.value.trim() || null,
            nickname: searchInput.value.trim() || null, 
        };
        Object.keys(currentFilters).forEach(key => {
            if (currentFilters[key] === null || currentFilters[key] === '') {
                delete currentFilters[key];
            }
        });

        fetchUsers(1, currentFilters); 
    }
    if (searchButton && searchInput) {
        searchButton.addEventListener('click', applyFilters);
        searchInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                applyFilters();
            }
        });
    }

    // --- Toast Notification Helper - 使用全局组件 ---
    function showToast(message, type = 'info') {
        // 使用全局Toast组件显示消息
        window.parent?.postMessage({
            type: 'SHOW_TOAST',
            payload: { message: message, type: type }
        }, '*');
    }

    // --- Initial Load --- 
    fetchUsers(currentPage, currentFilters);

});