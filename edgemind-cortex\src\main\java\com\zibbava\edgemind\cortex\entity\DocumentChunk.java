package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 文档分片实体类
 * 用于记录文档分片与向量存储的映射关系
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("kb_document_chunks")
public class DocumentChunk {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联的文档ID
     */
    @TableField("document_id")
    private String documentId;

    /**
     * 关联的节点ID
     */
    @TableField("node_id")
    private String nodeId;

    /**
     * 向量存储中的分片ID
     */
    @TableField("chunk_id")
    private String chunkId;

    /**
     * 分片索引号
     */
    @TableField("chunk_index")
    private Integer chunkIndex;

    /**
     * 分片文本长度
     */
    @TableField("chunk_size")
    private Integer chunkSize;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
} 