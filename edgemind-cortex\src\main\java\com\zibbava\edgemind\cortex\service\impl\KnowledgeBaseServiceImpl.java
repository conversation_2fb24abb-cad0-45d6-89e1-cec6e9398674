package com.zibbava.edgemind.cortex.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zibbava.edgemind.cortex.common.enums.NodeType;
import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import com.zibbava.edgemind.cortex.common.enums.VectorStatus;
import com.zibbava.edgemind.cortex.common.exception.AccessDeniedException;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.common.exception.ResourceNotFoundException;
import com.zibbava.edgemind.cortex.dto.knowledgebase.KnowledgeNodeDto;
import com.zibbava.edgemind.cortex.dto.knowledgebase.NodeCreateRequest;
import com.zibbava.edgemind.cortex.dto.knowledgebase.NodeUpdateRequest;
import com.zibbava.edgemind.cortex.entity.KnowledgeDocument;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import com.zibbava.edgemind.cortex.mapper.KnowledgeDocumentMapper;
import com.zibbava.edgemind.cortex.mapper.KnowledgeNodeMapper;
import com.zibbava.edgemind.cortex.mapper.KnowledgeSpaceMapper;
import com.zibbava.edgemind.cortex.service.DocumentIndexingService;
import com.zibbava.edgemind.cortex.service.KnowledgeBaseService;
import com.zibbava.edgemind.cortex.service.KnowledgeNodeClosureService;
import com.zibbava.edgemind.cortex.util.FileUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    private final KnowledgeSpaceMapper spaceMapper;
    private final KnowledgeNodeMapper nodeMapper;
    private final KnowledgeDocumentMapper documentMapper;
    @Lazy
    private final DocumentIndexingService indexingService; // Inject async service
    private final FileUtils fileUtils; // Inject file utility
    private final KnowledgeNodeClosureService closureService; // Inject closure table service


    @Override
    public List<KnowledgeSpace> getAccessibleSpaces(Long userId) {
        // Use mapper method directly or implement logic here if more complex filtering needed
        return spaceMapper.findAccessibleSpaces(userId);
    }

    @Override
    public KnowledgeSpace getOrCreateKnowledgeSpace(Long userId, boolean isPrivate) {
        KnowledgeSpace space = null;

        if (isPrivate) {
            // 查询是否已经有该用户的私人空间
            space = spaceMapper.selectOne(
                    new LambdaQueryWrapper<KnowledgeSpace>()
                            .eq(KnowledgeSpace::getOwnerUserId, userId)
                            .eq(KnowledgeSpace::getIsPrivate, true)
            );

            // 如果没有则创建
            if (space == null) {
                space = new KnowledgeSpace();
                space.setSpaceId(UUID.randomUUID().toString());
                space.setName("个人库");
                space.setDescription("个人私有知识空间");
                space.setIsPrivate(true);
                space.setOwnerUserId(userId);
                space.setCreateBy(userId);
                space.setUpdateBy(userId);

                spaceMapper.insert(space);
                log.info("为用户 {} 创建了私人知识空间: {}", userId, space.getSpaceId());
            }
        } else {
            // 查询是否已有团队知识空间（公共空间）
            space = spaceMapper.selectOne(
                    new LambdaQueryWrapper<KnowledgeSpace>()
                            .eq(KnowledgeSpace::getIsPrivate, false)
            );

            // 如果没有则创建一个公共团队空间
            if (space == null) {
                space = new KnowledgeSpace();
                space.setSpaceId(UUID.randomUUID().toString());
                space.setName("知识库");
                space.setDescription("公共团队知识空间");
                space.setIsPrivate(false);
                space.setOwnerUserId(null); // 团队空间没有特定所有者
                space.setCreateBy(userId);
                space.setUpdateBy(userId);

                spaceMapper.insert(space);
                log.info("创建了团队知识空间: {}", space.getSpaceId());
            }
        }

        return space;
    }

    @Override
    public List<KnowledgeNodeDto> getSpaceTree(String spaceId) {
        Long userId = StpUtil.getLoginIdAsLong(); // Get current user ID
        checkAccess(spaceId, userId); // Check access first

        // 1. 使用闭包表服务获取空间的根节点
        List<KnowledgeNode> rootNodes = closureService.getRootNodes(spaceId);

        // 2. 构建树结构
        List<KnowledgeNodeDto> rootNodeDtos = new ArrayList<>();
        for (KnowledgeNode rootNode : rootNodes) {
            KnowledgeNodeDto rootDto = convertToDto(rootNode);
            //todo 查询量过大
            buildNodeTree(rootDto);
            rootNodeDtos.add(rootDto);
        }

        return rootNodeDtos;
    }

    /**
     * 将节点实体转换为DTO
     */
    private KnowledgeNodeDto convertToDto(KnowledgeNode node) {
        KnowledgeNodeDto dto = new KnowledgeNodeDto();
        BeanUtils.copyProperties(node, dto);
        dto.setChildren(new ArrayList<>());
        return dto;
    }

    /**
     * 递归构建节点的子树
     */
    private void buildNodeTree(KnowledgeNodeDto parentDto) {
        // 获取直接子节点
        List<KnowledgeNode> children = closureService.getDirectChildren(parentDto.getNodeId());

        // 递归构建子树
        for (KnowledgeNode child : children) {
            KnowledgeNodeDto childDto = convertToDto(child);
            parentDto.getChildren().add(childDto);
            buildNodeTree(childDto); // 递归处理
        }
    }

    @Override
    @Transactional
    public KnowledgeNodeDto createNode(NodeCreateRequest request) {
        // 检查权限 (检查用户是否有权访问指定的空间)
        Long userId = StpUtil.getLoginIdAsLong();
        checkAccess(request.getSpaceId(), userId);

        // 名称规范化处理（即使在DTO层已有校验，这里也做保险处理）
        String nodeName = sanitizeNodeName(request.getName());
        request.setName(nodeName);

        // 父节点检查
        if (StringUtils.hasText(request.getParentNodeId())) {
            KnowledgeNode parentNode = nodeMapper.selectById(request.getParentNodeId());
            if (parentNode == null) {
                throw new BusinessException(ResultCode.RESOURCE_NOT_FOUND, "指定的父节点不存在");
            }

            // 如果父节点不是文件夹，不能添加子节点
            if (parentNode.getType() != NodeType.FOLDER) {
                throw new BusinessException(ResultCode.NODE_TYPE_MISMATCH, "只能在文件夹下创建节点");
            }
        }

        // 检查同层级是否有同名节点
        LambdaQueryWrapper<KnowledgeNode> nameCheckWrapper = new LambdaQueryWrapper<>();
        nameCheckWrapper.eq(KnowledgeNode::getName, request.getName())
                .eq(KnowledgeNode::getSpaceId, request.getSpaceId())
                .eq(KnowledgeNode::getType, request.getType());

        // 根据父节点ID是否为空添加不同的条件
        if (StringUtils.hasText(request.getParentNodeId())) {
            // 父节点不为空，添加父节点相等条件
            nameCheckWrapper.eq(KnowledgeNode::getParentNodeId, request.getParentNodeId());
        } else {
            // 父节点为空，添加父节点为null的条件
            nameCheckWrapper.isNull(KnowledgeNode::getParentNodeId);
        }

        if (nodeMapper.selectCount(nameCheckWrapper) > 0) {
            throw new BusinessException(ResultCode.NODE_DUPLICATE_NAME, "同一层级下已存在同名节点");
        }

        // 创建新节点
        KnowledgeNode newNode = new KnowledgeNode();
        newNode.setNodeId(UUID.randomUUID().toString());
        newNode.setSpaceId(request.getSpaceId());
        newNode.setName(request.getName());
        newNode.setType(request.getType());
        newNode.setParentNodeId(request.getParentNodeId());
        newNode.setCreateBy(userId);
        newNode.setUpdateBy(userId);

        nodeMapper.insert(newNode);

        log.info("知识库节点创建成功: spaceId={}, name={}, type={}, nodeId={}",
                newNode.getSpaceId(), newNode.getName(), newNode.getType(), newNode.getNodeId());

        // 初始化节点的闭包表关系
        closureService.initNodeClosure(newNode.getNodeId());

        // 如果是文件夹节点，创建对应的物理目录
        if (newNode.getType() == NodeType.FOLDER) {
            try {
                fileUtils.createNodeDirectory(newNode.getNodeId());
                log.info("为文件夹节点创建了物理目录: nodeId={}", newNode.getNodeId());
            } catch (IOException e) {
                log.error("为文件夹节点创建物理目录时出错: nodeId={}, error={}", newNode.getNodeId(), e.getMessage(), e);
                // 不抛出异常，允许节点创建成功但物理目录创建失败的情况
            }
        }

        // 如果是文件节点，创建关联的文档记录
        if (newNode.getType() == NodeType.FILE) {
            KnowledgeDocument newDoc = new KnowledgeDocument();
            newDoc.setDocumentId(UUID.randomUUID().toString());
            newDoc.setNodeId(newNode.getNodeId());
            newDoc.setVectorStatus(VectorStatus.PENDING); // 初始状态
            documentMapper.insert(newDoc);
            log.info("关联文档记录创建成功: nodeId={}, documentId={}", newDoc.getNodeId(), newDoc.getDocumentId());
        }

        KnowledgeNodeDto dto = new KnowledgeNodeDto();
        BeanUtils.copyProperties(newNode, dto);
        return dto;
    }

    @Override
    @Transactional
    public KnowledgeNodeDto updateNode(String nodeId, NodeUpdateRequest request) {
        Long userId = StpUtil.getLoginIdAsLong();
        KnowledgeNode existingNode = nodeMapper.selectById(nodeId);
        if (existingNode == null) {
            throw new BusinessException(ResultCode.RESOURCE_NOT_FOUND, "节点不存在: " + nodeId);
        }
        checkAccess(existingNode.getSpaceId(), userId); // Check access

        // 名称规范化处理（即使在DTO层已有校验，这里也做保险处理）
        String nodeName = sanitizeNodeName(request.getName());
        request.setName(nodeName);

        // Check for duplicate name if name is changed
        if (!existingNode.getName().equals(request.getName())) {
            // 检查同层级是否有同名节点
            LambdaQueryWrapper<KnowledgeNode> nameCheckWrapper = new LambdaQueryWrapper<>();
            nameCheckWrapper.eq(KnowledgeNode::getName, request.getName())
                    .eq(KnowledgeNode::getSpaceId, existingNode.getSpaceId())
                    .eq(KnowledgeNode::getType, existingNode.getType())
                    .ne(KnowledgeNode::getNodeId, nodeId); // 排除自身

            // 根据父节点ID是否为空添加不同的条件
            if (existingNode.getParentNodeId() != null) {
                // 父节点不为空，添加父节点相等条件
                nameCheckWrapper.eq(KnowledgeNode::getParentNodeId, existingNode.getParentNodeId());
            } else {
                // 父节点为空，添加父节点为null的条件
                nameCheckWrapper.isNull(KnowledgeNode::getParentNodeId);
            }

            if (nodeMapper.selectCount(nameCheckWrapper) > 0) {
                throw new BusinessException(ResultCode.NODE_DUPLICATE_NAME, "同一层级下已存在同名节点");
            }
        }

        String oldName = existingNode.getName();
        String oldParentId = existingNode.getParentNodeId();

        // 检查是否需要更新父节点
        boolean parentChanged = request.getParentNodeId() != null &&
                !request.getParentNodeId().equals(oldParentId);

        // 更新节点信息
        existingNode.setName(request.getName());
        existingNode.setUpdateBy(userId);

        // 如果父节点发生变化，更新父节点引用
        if (parentChanged) {
            existingNode.setParentNodeId(request.getParentNodeId());
        }

        nodeMapper.updateById(existingNode);
        log.info("知识库节点更新成功: nodeId={}, newName={}", nodeId, request.getName());

        // 如果父节点发生变化，更新闭包表关系
        if (parentChanged) {
            closureService.updateNodeClosure(nodeId, request.getParentNodeId());
            log.info("节点父节点关系更新成功: nodeId={}, newParentId={}", nodeId, request.getParentNodeId());
        }

        // 如果名称发生变化，同步更新物理文件或目录名称
        if (!oldName.equals(request.getName())) {
            try {
                boolean renamed = fileUtils.renameNodeFileOrDirectory(nodeId, oldName, request.getName());
                if (renamed) {
                    log.info("成功重命名节点对应的物理文件或目录: nodeId={}, oldName={}, newName={}",
                            nodeId, oldName, request.getName());
                } else {
                    log.warn("重命名节点对应的物理文件或目录失败: nodeId={}, oldName={}, newName={}",
                            nodeId, oldName, request.getName());
                }
            } catch (Exception e) {
                log.error("重命名节点对应的物理文件或目录时出错: nodeId={}, oldName={}, newName={}, error={}",
                        nodeId, oldName, request.getName(), e.getMessage(), e);
                // 不抛出异常，允许节点重命名成功但物理文件/目录重命名失败的情况
            }
        }

        KnowledgeNodeDto dto = new KnowledgeNodeDto();
        BeanUtils.copyProperties(existingNode, dto);
        return dto;
    }

    @Override
    @Transactional
    public void deleteNode(String nodeId) {
        KnowledgeNode node = nodeMapper.selectById(nodeId);
        if (node == null) {
            throw new BusinessException(ResultCode.RESOURCE_NOT_FOUND, "要删除的节点不存在");
        }

        // 检查权限
        Long userId = StpUtil.getLoginIdAsLong();
        checkAccess(node.getSpaceId(), userId);

        // 如果是文件夹，检查是否为空
        if (NodeType.FOLDER.equals(node.getType())) {
            LambdaQueryWrapper<KnowledgeNode> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KnowledgeNode::getParentNodeId, nodeId);
            long childCount = nodeMapper.selectCount(queryWrapper);
            if (childCount > 0) {
                throw new BusinessException(ResultCode.FOLDER_NOT_EMPTY, "文件夹不为空，请先删除其中的内容");
            }

            // 删除对应的物理目录
            try {
                boolean deleted = fileUtils.deleteNodeDirectory(nodeId);
                if (deleted) {
                    log.info("成功删除节点对应的物理目录: nodeId={}", nodeId);
                } else {
                    log.warn("删除节点对应的物理目录失败: nodeId={}", nodeId);
                }
            } catch (Exception e) {
                log.error("删除节点对应的物理目录时出错: nodeId={}, error={}", nodeId, e.getMessage(), e);
                // 不抛出异常，允许节点删除成功但物理目录删除失败的情况
            }
        }


        // 如果是文件，处理关联的文档和向量
        if (NodeType.FILE.equals(node.getType())) {
            // 查找关联的文档
            LambdaQueryWrapper<KnowledgeDocument> docWrapper = new LambdaQueryWrapper<>();
            docWrapper.eq(KnowledgeDocument::getNodeId, nodeId);
            KnowledgeDocument document = documentMapper.selectOne(docWrapper);

            if (document != null) {

                // 删除物理文件
                try {
                    fileUtils.deleteFileByNodeId(nodeId);
                } catch (Exception e) {
                    // 使用通用Exception捕获所有可能的异常
                    log.error("删除节点 {} 对应的物理文件时出错", nodeId, e);
                    // 记录日志但继续流程
                    throw new BusinessException(ResultCode.OPERATION_FAILED, "删除节点失败");
                }

                // 删除文档
                documentMapper.deleteById(document.getDocumentId());

                // 删除向量（异步）
                try {
                    indexingService.deleteVectors(nodeId);
                } catch (Exception e) {
                    // 记录日志但不中断流程，因为这是异步操作
                    log.error("删除节点 {} 关联的向量时发生错误", nodeId, e);
                }

            }
        }

        // 删除节点的闭包表关系
        closureService.deleteNodeClosure(nodeId);

        // 删除节点
        nodeMapper.deleteById(nodeId);
        log.info("节点 {} 已删除", nodeId);
    }

    @Override
    public KnowledgeDocument uploadDocument(String nodeId, MultipartFile file, boolean skipIfExists) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new BusinessException(ResultCode.FILE_FORMAT_ERROR, "上传文件不能为空");
        }

        Long userId = StpUtil.getLoginIdAsLong();
        KnowledgeNode node = nodeMapper.selectById(nodeId);
        if (node == null) {
            throw new BusinessException(ResultCode.RESOURCE_NOT_FOUND, "节点不存在: " + nodeId);
        }
        if (node.getType() != NodeType.FILE) {
            throw new BusinessException(ResultCode.NODE_TYPE_MISMATCH, "只能向文件节点上传内容");
        }
        checkAccess(node.getSpaceId(), userId); // Check access

        KnowledgeDocument document = documentMapper.selectOne(
                new LambdaQueryWrapper<KnowledgeDocument>().eq(KnowledgeDocument::getNodeId, nodeId)
        );
        if (document == null) {
            // This shouldn't happen if createNode logic is correct, but handle defensively
            log.warn("节点 {} 是文件类型，但找不到关联的文档记录，将创建新记录。", nodeId);
            document = new KnowledgeDocument();
            document.setDocumentId(UUID.randomUUID().toString());
            document.setNodeId(nodeId);
            document.setVectorStatus(VectorStatus.PENDING);
            // No existing hash to compare
        } else {
            // Calculate hash of new file to check if content changed
            String newHash = fileUtils.calculateHash(file.getInputStream());
            if (newHash.equals(document.getContentHash())) {
                log.info("上传的文件内容与现有文件内容相同 (NodeId: {}), 跳过更新和索引.", nodeId);
                return document; // No changes needed
            }
            document.setContentHash(newHash); // Update hash
        }

        // 保存文件 - 使用节点路径结构确定文件位置，不再使用相对路径
        // 正常上传时覆盖现有文件（skipIfExists=false）
        Path filePath = fileUtils.saveFile(node.getSpaceId(), nodeId, file, skipIfExists);

        // 更新文档记录，不再使用filePath字段
        document.setMimeType(file.getContentType());
        document.setFileSize(file.getSize());
        document.setVectorStatus(VectorStatus.PENDING); // Set to pending for re-indexing
        document.setVectorErrorMessage(null); // Clear previous error

        if (documentMapper.selectById(document.getDocumentId()) != null) {
            documentMapper.updateById(document);
        } else {
            documentMapper.insert(document);
        }

        log.info("文件上传/更新成功: nodeId={}, 文件路径={}", nodeId, filePath);

        // 触发异步索引
        indexingService.startIndexing(document.getDocumentId());
        log.info("触发文档异步索引任务: documentId={}", document.getDocumentId());

        return document;
    }

    @Override
    public boolean checkAccess(String spaceId, Long userId) {
        KnowledgeSpace space = spaceMapper.selectById(spaceId);
        if (space == null) {
            throw new ResourceNotFoundException("知识空间不存在: " + spaceId);
        }
        // Allow access if it's a public space OR if it's private and owned by the current user
        if (!space.getIsPrivate() || (space.getIsPrivate() && userId.equals(space.getOwnerUserId()))) {
            return true;
        }
        throw new AccessDeniedException("用户无权访问知识空间: " + spaceId);
    }

    @Override
    public List<String> getAllFileNodeIdsInFolder(String folderNodeId) {
        List<String> fileNodeIds = new ArrayList<>();
        findFileNodesRecursively(folderNodeId, fileNodeIds);
        return fileNodeIds;
    }

    private void findFileNodesRecursively(String parentNodeId, List<String> fileNodeIds) {
        List<KnowledgeNode> children = nodeMapper.selectList(
                new LambdaQueryWrapper<KnowledgeNode>().eq(KnowledgeNode::getParentNodeId, parentNodeId)
        );
        for (KnowledgeNode child : children) {
            if (child.getType() == NodeType.FILE) {
                fileNodeIds.add(child.getNodeId());
            } else if (child.getType() == NodeType.FOLDER) {
                findFileNodesRecursively(child.getNodeId(), fileNodeIds);
            }
        }
    }

    @Override
    public KnowledgeNode findNodeById(String nodeId) {
        // Consider adding caching if this is frequently called
        return nodeMapper.selectById(nodeId);
    }

    @Override
    public KnowledgeDocument findDocumentByNodeId(String nodeId) {
        if (!StringUtils.hasText(nodeId)) {
            return null;
        }
        // No need to check node type here, just find the associated document
        return documentMapper.selectOne(
                new LambdaQueryWrapper<KnowledgeDocument>().eq(KnowledgeDocument::getNodeId, nodeId)
        );
    }

    /**
     * 校验并清理节点名称，根据当前操作系统动态校验文件名规则
     *
     * @param name 原始节点名称
     * @return 规范化后的节点名称
     */
    private String sanitizeNodeName(String name) {
        return name.trim();

    }

    @Override
    @Transactional
    public KnowledgeDocument reparseDocument(String nodeId) {
        Long userId = StpUtil.getLoginIdAsLong();
        KnowledgeNode node = nodeMapper.selectById(nodeId);

        if (node == null) {
            throw new ResourceNotFoundException("节点不存在: " + nodeId);
        }
        if (node.getType() != NodeType.FILE) {
            throw new BusinessException(ResultCode.NODE_TYPE_MISMATCH, "只能对文件节点执行重新解析操作");
        }
        checkAccess(node.getSpaceId(), userId); // Check access

        KnowledgeDocument document = documentMapper.selectOne(
                new LambdaQueryWrapper<KnowledgeDocument>().eq(KnowledgeDocument::getNodeId, nodeId)
        );

        if (document == null) {
            log.error("节点 {} 是文件类型，但找不到关联的文档记录。无法重新解析。", nodeId);
            throw new ResourceNotFoundException("找不到节点的关联文档信息，无法重新解析");
        }

        // 更新文档状态以准备重新索引
        document.setVectorStatus(VectorStatus.PENDING);
        document.setVectorErrorMessage(null); // 清除之前的任何错误信息
        document.setLastIndexedTime(null); // 清除上次索引时间
        documentMapper.updateById(document);

        log.info("请求重新解析文档: nodeId={}, documentId={}", nodeId, document.getDocumentId());

        // 触发异步索引
        try {
            indexingService.startIndexing(document.getDocumentId());
            log.info("已触发文档异步重新索引任务: documentId={}", document.getDocumentId());
        } catch (Exception e) {
            log.error("触发文档 {} (nodeId: {}) 的异步重新索引任务失败: {}", document.getDocumentId(), nodeId, e.getMessage(), e);
            // 即使触发失败，文档状态已为PENDING，可以后续重试或监控
            // 可以考虑是否要将状态改回 FAILED 并记录错误信息
            document.setVectorStatus(VectorStatus.FAILED);
            document.setVectorErrorMessage("触发重新索引任务失败: " + e.getMessage());
            documentMapper.updateById(document);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "触发重新索引任务失败", e);
        }

        return document;
    }

    @Override
    public VectorStatus getDocumentVectorStatus(String nodeId) {
        KnowledgeNode node = nodeMapper.selectById(nodeId);
        if (node == null) {
            throw new ResourceNotFoundException("节点不存在: " + nodeId);
        }
        if (node.getType() != NodeType.FILE) {
            throw new BusinessException(ResultCode.NODE_TYPE_MISMATCH, "指定节点不是文件类型");
        }

        KnowledgeDocument document = documentMapper.selectOne(
                new LambdaQueryWrapper<KnowledgeDocument>().eq(KnowledgeDocument::getNodeId, nodeId)
        );

        if (document == null) {
            log.warn("节点 {} 的文档记录未找到。", nodeId);
            throw new ResourceNotFoundException("找不到与节点关联的文档信息");
        }
        if (document.getVectorStatus() == null) {
            log.warn("节点 {} 的文档 (ID: {}) 向量状态为null，数据可能不一致。", nodeId, document.getDocumentId());
            // This indicates an inconsistent state. Throwing an error is safer than returning a default.
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "文档向量状态未知，数据不一致");
        }
        return document.getVectorStatus();
    }

}