package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.cortex.entity.Department;
import com.zibbava.edgemind.cortex.entity.User;
import com.zibbava.edgemind.cortex.exception.BadRequestException;
import com.zibbava.edgemind.cortex.exception.ResourceNotFoundException;
import com.zibbava.edgemind.cortex.mapper.DepartmentMapper;
import com.zibbava.edgemind.cortex.mapper.UserMapper;
import com.zibbava.edgemind.cortex.service.DepartmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 部门服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, Department> implements DepartmentService {

    private final DepartmentMapper departmentMapper;
    private final UserMapper userMapper;

    @Override
    public List<Department> getDepartmentTree() {
        // 获取所有启用的部门
        LambdaQueryWrapper<Department> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Department::getStatus, 1)
               .orderByAsc(Department::getSortOrder, Department::getId);
        
        List<Department> allDepartments = this.list(wrapper);
        
        // 构建树形结构
        return buildDepartmentTree(allDepartments, 0L);
    }

    @Override
    public List<Department> getChildDepartments(Long parentId) {
        LambdaQueryWrapper<Department> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Department::getParentId, parentId)
               .eq(Department::getStatus, 1)
               .orderByAsc(Department::getSortOrder, Department::getId);
        
        return this.list(wrapper);
    }

    @Override
    @Transactional
    public Long createDepartment(Department department) {
        // 验证部门编码唯一性
        if (StringUtils.hasText(department.getDeptCode()) && 
            isDeptCodeExists(department.getDeptCode(), null)) {
            throw new BadRequestException("部门编码已存在: " + department.getDeptCode());
        }
        
        // 验证父部门存在性
        if (department.getParentId() != null && department.getParentId() != 0) {
            Department parentDept = this.getById(department.getParentId());
            if (parentDept == null) {
                throw new BadRequestException("父部门不存在: " + department.getParentId());
            }
            // 设置部门层级
            department.setDeptLevel(parentDept.getDeptLevel() + 1);
        } else {
            department.setParentId(0L);
            department.setDeptLevel(1);
        }
        
        // 设置默认值
        if (department.getStatus() == null) {
            department.setStatus(1);
        }
        if (department.getSortOrder() == null) {
            department.setSortOrder(0);
        }
        
        this.save(department);
        
        // 更新部门路径
        updateDepartmentPath(department.getId());
        
        return department.getId();
    }

    @Override
    @Transactional
    public void updateDepartment(Department department) {
        Department existingDept = this.getById(department.getId());
        if (existingDept == null) {
            throw new ResourceNotFoundException("部门不存在: " + department.getId());
        }
        
        // 验证部门编码唯一性
        if (StringUtils.hasText(department.getDeptCode()) && 
            isDeptCodeExists(department.getDeptCode(), department.getId())) {
            throw new BadRequestException("部门编码已存在: " + department.getDeptCode());
        }
        
        // 验证父部门存在性和循环引用
        if (department.getParentId() != null && department.getParentId() != 0) {
            if (department.getParentId().equals(department.getId())) {
                throw new BadRequestException("不能将自己设置为父部门");
            }
            
            Department parentDept = this.getById(department.getParentId());
            if (parentDept == null) {
                throw new BadRequestException("父部门不存在: " + department.getParentId());
            }
            
            // 检查是否会形成循环引用
            if (isCircularReference(department.getId(), department.getParentId())) {
                throw new BadRequestException("不能将子部门设置为父部门，会形成循环引用");
            }
            
            department.setDeptLevel(parentDept.getDeptLevel() + 1);
        } else {
            department.setParentId(0L);
            department.setDeptLevel(1);
        }
        
        this.updateById(department);
        
        // 如果父部门发生变化，需要更新部门路径
        if (!existingDept.getParentId().equals(department.getParentId())) {
            updateDepartmentPath(department.getId());
            // 同时更新所有子部门的路径
            updateChildDepartmentPaths(department.getId());
        }
    }

    @Override
    @Transactional
    public void deleteDepartment(Long deptId) {
        Department department = this.getById(deptId);
        if (department == null) {
            throw new ResourceNotFoundException("部门不存在: " + deptId);
        }
        
        if (!canDeleteDepartment(deptId)) {
            throw new BadRequestException("该部门下还有子部门或用户，无法删除");
        }
        
        this.removeById(deptId);
    }

    @Override
    public boolean isDeptCodeExists(String deptCode, Long excludeDeptId) {
        if (!StringUtils.hasText(deptCode)) {
            return false;
        }
        
        LambdaQueryWrapper<Department> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Department::getDeptCode, deptCode);
        if (excludeDeptId != null) {
            wrapper.ne(Department::getId, excludeDeptId);
        }
        
        return this.count(wrapper) > 0;
    }

    @Override
    public boolean canDeleteDepartment(Long deptId) {
        // 检查是否有子部门
        LambdaQueryWrapper<Department> deptWrapper = new LambdaQueryWrapper<>();
        deptWrapper.eq(Department::getParentId, deptId);
        if (this.count(deptWrapper) > 0) {
            return false;
        }
        
        // 检查是否有用户
        LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(User::getDeptId, deptId);
        return userMapper.selectCount(userWrapper) == 0;
    }

    @Override
    public String getDepartmentPath(Long deptId) {
        if (deptId == null || deptId == 0) {
            return "";
        }
        
        Department department = this.getById(deptId);
        if (department == null) {
            return "";
        }
        
        List<String> pathParts = new ArrayList<>();
        pathParts.add(department.getDeptName());
        
        Long parentId = department.getParentId();
        while (parentId != null && parentId != 0) {
            Department parent = this.getById(parentId);
            if (parent == null) {
                break;
            }
            pathParts.add(0, parent.getDeptName());
            parentId = parent.getParentId();
        }
        
        return String.join(" > ", pathParts);
    }

    @Override
    @Transactional
    public void updateDepartmentPath(Long deptId) {
        String path = getDepartmentPath(deptId);
        
        Department department = new Department();
        department.setId(deptId);
        department.setDeptPath(path);
        
        this.updateById(department);
    }

    @Override
    public List<Long> getDescendantDeptIds(Long deptId) {
        return departmentMapper.selectDescendantIds(deptId);
    }

    /**
     * 构建部门树
     */
    private List<Department> buildDepartmentTree(List<Department> allDepartments, Long parentId) {
        List<Department> result = new ArrayList<>();
        
        // 按父ID分组
        Map<Long, List<Department>> departmentMap = allDepartments.stream()
                .collect(Collectors.groupingBy(dept -> dept.getParentId() == null ? 0L : dept.getParentId()));
        
        List<Department> children = departmentMap.get(parentId);
        if (children != null) {
            for (Department dept : children) {
                dept.setChildren(buildDepartmentTree(allDepartments, dept.getId()));
                result.add(dept);
            }
        }
        
        return result;
    }

    /**
     * 检查是否会形成循环引用
     */
    private boolean isCircularReference(Long deptId, Long parentId) {
        if (parentId == null || parentId == 0) {
            return false;
        }
        
        if (parentId.equals(deptId)) {
            return true;
        }
        
        Department parent = this.getById(parentId);
        if (parent == null) {
            return false;
        }
        
        return isCircularReference(deptId, parent.getParentId());
    }

    /**
     * 更新子部门路径
     */
    private void updateChildDepartmentPaths(Long parentId) {
        List<Department> children = getChildDepartments(parentId);
        for (Department child : children) {
            updateDepartmentPath(child.getId());
            updateChildDepartmentPaths(child.getId());
        }
    }
}
