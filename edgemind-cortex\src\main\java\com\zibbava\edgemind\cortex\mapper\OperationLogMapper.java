package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.cortex.entity.OperationLog;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface OperationLogMapper extends BaseMapper<OperationLog> {

    /**
     * 分页查询操作日志
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param operationType 操作类型
     * @param module 操作模块
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<OperationLog> selectLogPage(Page<OperationLog> page,
                                      @Param("userId") Long userId,
                                      @Param("operationType") String operationType,
                                      @Param("module") String module,
                                      @Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 根据用户ID查询最近的操作日志
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 操作日志列表
     */
    @Select("SELECT * FROM sys_operation_log WHERE user_id = #{userId} ORDER BY create_time DESC LIMIT #{limit}")
    List<OperationLog> selectRecentLogsByUserId(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 统计指定时间范围内的操作次数
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    @Select("SELECT COUNT(*) FROM sys_operation_log WHERE create_time BETWEEN #{startTime} AND #{endTime}")
    Long countOperationsByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 删除指定时间之前的日志记录
     *
     * @param beforeTime 时间点
     * @return 删除的记录数
     */
    @Delete("DELETE FROM sys_operation_log WHERE create_time < #{beforeTime}")
    int deleteLogsBefore(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 统计各模块的操作次数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    @Select("SELECT module, COUNT(*) as count FROM sys_operation_log " +
            "WHERE create_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY module ORDER BY count DESC")
    List<Map<String, Object>> countOperationsByModule(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}
