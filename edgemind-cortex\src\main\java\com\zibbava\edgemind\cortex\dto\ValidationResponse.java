package com.zibbava.edgemind.cortex.dto;

import lombok.Data;
import java.util.Map;
import java.util.HashMap;

/**
 * 表单验证响应DTO
 * 用于后端向前端返回表单验证结果
 */
@Data
public class ValidationResponse {
    
    /**
     * 验证是否通过
     */
    private boolean success;
    
    /**
     * 全局验证消息
     */
    private String message;
    
    /**
     * 字段级别的验证错误
     * key: 字段名, value: 错误消息
     */
    private Map<String, String> fieldErrors;
    
    /**
     * 验证警告信息
     * key: 字段名, value: 警告消息
     */
    private Map<String, String> warnings;
    
    /**
     * 额外的验证信息
     */
    private Map<String, Object> extraData;
    
    public ValidationResponse() {
        this.fieldErrors = new HashMap<>();
        this.warnings = new HashMap<>();
        this.extraData = new HashMap<>();
    }
    
    public static ValidationResponse success() {
        ValidationResponse response = new ValidationResponse();
        response.setSuccess(true);
        response.setMessage("验证通过");
        return response;
    }
    
    public static ValidationResponse success(String message) {
        ValidationResponse response = new ValidationResponse();
        response.setSuccess(true);
        response.setMessage(message);
        return response;
    }
    
    public static ValidationResponse error(String message) {
        ValidationResponse response = new ValidationResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }
    
    public static ValidationResponse error(String message, Map<String, String> fieldErrors) {
        ValidationResponse response = new ValidationResponse();
        response.setSuccess(false);
        response.setMessage(message);
        response.setFieldErrors(fieldErrors);
        return response;
    }
    
    /**
     * 添加字段错误
     */
    public ValidationResponse addFieldError(String field, String error) {
        this.fieldErrors.put(field, error);
        this.success = false;
        return this;
    }
    
    /**
     * 添加字段警告
     */
    public ValidationResponse addWarning(String field, String warning) {
        this.warnings.put(field, warning);
        return this;
    }
    
    /**
     * 添加额外数据
     */
    public ValidationResponse addExtraData(String key, Object value) {
        this.extraData.put(key, value);
        return this;
    }
    
    /**
     * 检查是否有字段错误
     */
    public boolean hasFieldErrors() {
        return fieldErrors != null && !fieldErrors.isEmpty();
    }
    
    /**
     * 检查是否有警告
     */
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }
}
