package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.zibbava.edgemind.cortex.common.enums.VectorStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 知识库文档实体类 (存储文档元数据及向量化状态)
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("kb_knowledge_documents")
public class KnowledgeDocument {

    /**
     * 文档记录ID (UUID字符串)
     */
    @TableId(value = "document_id", type = IdType.ASSIGN_UUID)
    private String documentId;

    /**
     * 关联的知识库节点 ID (文件类型节点)
     */
    @TableField("node_id")
    private String nodeId;

    // filePath字段已移除，使用节点路径结构代替

    /**
     * 文件的MIME类型 (例如: application/pdf, text/plain)
     */
    @TableField("mime_type")
    private String mimeType;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size_bytes")
    private Long fileSize;

    /**
     * 向量化处理状态: PENDING, PROCESSING, INDEXED, FAILED
     */
    @TableField("vector_status")
    @EnumValue // MyBatis-Plus 枚举映射
    private VectorStatus vectorStatus;

    /**
     * 记录向量化失败时的错误信息
     */
    @TableField("vector_error_message")
    private String vectorErrorMessage;

    /**
     * 最后成功索引到向量库的时间
     */
    @TableField("last_indexed_time")
    private LocalDateTime lastIndexedTime;

    /**
     * 文件内容的哈希值 (例如 SHA-256)，用于检测内容是否变更
     */
    @TableField("content_hash")
    private String contentHash;

    /**
     * 记录创建时间 (由数据库或MyBatis-Plus自动填充)
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 记录更新时间 (由数据库或MyBatis-Plus自动填充)
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

     // --- 非数据库字段 ---

    /**
     * 关联的知识库节点信息 (查询时填充)
     */
    @TableField(exist = false)
    private KnowledgeNode node;

}