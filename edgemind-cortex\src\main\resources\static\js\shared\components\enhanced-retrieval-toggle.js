/**
 * 增强检索切换组件
 * 提供知识库查询扩展功能的开关控制
 * 状态持久化到localStorage，页面刷新后保持设置
 */

class EnhancedRetrievalToggle {
    constructor(buttonElement) {
        this.button = buttonElement;
        this.storageKey = 'enhanced-retrieval-enabled';
        this.isEnabled = this.loadStateFromStorage();
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.updateButtonState();
        this.initTooltip();
    }
    
    /**
     * 从localStorage加载状态
     */
    loadStateFromStorage() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            // 默认开启增强检索，只有明确设置为false时才关闭
            return stored === null ? true : stored === 'true';
        } catch (error) {
            console.warn('[增强检索] localStorage访问失败，使用默认状态:', error);
            return true; // 默认开启
        }
    }
    
    /**
     * 保存状态到localStorage
     */
    saveStateToStorage() {
        try {
            localStorage.setItem(this.storageKey, this.isEnabled.toString());
    
        } catch (error) {
            console.warn('[增强检索] localStorage保存失败:', error);
        }
    }
    
    setupEventListeners() {
        this.button.addEventListener('click', (e) => {
            e.preventDefault();
            this.toggle();
        });
    }
    
    toggle() {
        this.isEnabled = !this.isEnabled;
        this.updateButtonState();
        this.saveStateToStorage(); // 立即保存状态
        this.onStateChange();
    }
    
    updateButtonState() {
        if (this.isEnabled) {
            this.button.classList.add('active');
            this.button.setAttribute('title', '增强检索');
        } else {
            this.button.classList.remove('active');
            this.button.setAttribute('title', '增强检索');
        }
        
        // 更新tooltip
        if (window.bootstrap && bootstrap.Tooltip) {
            const tooltip = bootstrap.Tooltip.getInstance(this.button);
            if (tooltip) {
                tooltip.dispose();
            }
            new bootstrap.Tooltip(this.button);
        }
    }
    
    initTooltip() {
        if (window.bootstrap && bootstrap.Tooltip) {
            new bootstrap.Tooltip(this.button);
        }
    }
    
    onStateChange() {
        // 触发自定义事件，通知其他组件状态变化
        const event = new CustomEvent('enhancedRetrievalToggle', {
            detail: { enabled: this.isEnabled },
            bubbles: true
        });
        this.button.dispatchEvent(event);
        

    }
    
    /**
     * 获取当前状态
     */
    getState() {
        return this.isEnabled;
    }
    
    /**
     * 获取当前状态（兼容旧版本方法名）
     */
    getEnabled() {
        return this.isEnabled;
    }
    
    /**
     * 设置状态
     */
    setState(enabled) {
        if (this.isEnabled !== enabled) {
            this.isEnabled = enabled;
            this.updateButtonState();
            this.saveStateToStorage();
            this.onStateChange();
        }
    }
    
    /**
     * 重置为默认状态
     */
    reset() {
        this.setState(true); // 重置为默认开启状态
    }
    
    setExpandingState(expanding) {
        if (expanding) {
            this.button.classList.add('expanding');
        } else {
            this.button.classList.remove('expanding');
        }
    }
    
    showButton() {
        this.button.style.display = 'inline-flex';
    }
    
    hideButton() {
        this.button.style.display = 'none';
    }
}

// 全局管理器
window.enhancedRetrievalManager = {
    toggles: new Map(),
    
    /**
     * 注册增强检索切换按钮
     */
    register(buttonId) {
        const button = document.getElementById(buttonId);
        if (button && !this.toggles.has(buttonId)) {
            const toggle = new EnhancedRetrievalToggle(button);
            this.toggles.set(buttonId, toggle);
            
            // 为按钮添加 enhancedRetrievalToggle 属性，便于外部访问
            button.enhancedRetrievalToggle = toggle;
            
    
            return toggle;
        }
        return this.toggles.get(buttonId);
    },
    
    /**
     * 获取指定按钮的状态
     */
    getState(buttonId) {
        const toggle = this.toggles.get(buttonId);
        return toggle ? toggle.getState() : true; // 默认开启
    },
    
    /**
     * 设置指定按钮的状态
     */
    setState(buttonId, enabled) {
        const toggle = this.toggles.get(buttonId);
        if (toggle) {
            toggle.setState(enabled);
        }
    },
    
    /**
     * 清理所有状态
     */
    cleanup() {
        this.toggles.clear();

    }
};

// 页面加载时自动初始化所有增强检索按钮
function initializeEnhancedRetrievalButtons() {
    const enhancedRetrievalButtons = document.querySelectorAll('.enhanced-retrieval-toggle');
    enhancedRetrievalButtons.forEach(button => {
        if (button.id) {
            window.enhancedRetrievalManager.register(button.id);
        }
    });
    
    
}

// 支持多种加载方式
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeEnhancedRetrievalButtons);
} else {
    // DOM已经加载完成，立即初始化
    initializeEnhancedRetrievalButtons();
}

// 也支持延迟初始化，用于动态添加的按钮
window.initializeEnhancedRetrievalButtons = initializeEnhancedRetrievalButtons;

// 导出组件类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedRetrievalToggle;
} else if (typeof window !== 'undefined') {
    window.EnhancedRetrievalToggle = EnhancedRetrievalToggle;
}