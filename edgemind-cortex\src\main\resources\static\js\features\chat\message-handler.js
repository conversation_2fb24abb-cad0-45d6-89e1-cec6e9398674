import { default_renderer, parser, parser_end, parser_write } from '../../shared/lib/streaming-markdown.js';
import { checkAndUpdateConversationTitle } from './chat-history.js';

// --- 状态变量 ---
let isProcessingMessage = false;  // 防止重复发送消息
let currentImageFile = null;      // 当前上传的图片文件
let isLoadingMore = false;        // 防止并发加载历史消息标志
let loadingConversationId = null; // 当前正在加载历史消息的会话ID
let autoScrollEnabled = true;     // 控制是否自动滚动到底部
let userHasScrolled = false;      // 跟踪用户是否手动滚动过
let isPaused = false;             // 控制对话是否暂停
let currentStream = null;         // 当前的SSE流实例
let currentController = null;     // 当前的AbortController实例，用于取消fetch请求
let knowledgeNodeId = null;       // 知识库节点ID，普通聊天为null
let knowledgeNodeIdFromContext = null; // 知识库节点ID，从上下文获取
let isKnowledgeChat = false; // 是否是知识库聊天

// --- DOM 元素引用 ---
let chatMessages = null;
let userInput = null;
let sendButton = null;
let modelSelect = null;
let uploadButton = null;
let imageUpload = null;
let imagePreviewContainer = null;

// --- 缓存从父页面获取的状态 ---
let parentState = {
    isGuest: true, // Default to guest
    currentConversationId: null
};
let statePromiseResolve = null;
let statePromise = new Promise(resolve => { statePromiseResolve = resolve; });

// --- 初始化函数 (由 chat-script.js 导入并调用) ---
function initialize(elements, config = {}) { // 增加config参数支持额外配置
    chatMessages = elements.chatMessages;
    userInput = elements.userInput;
    sendButton = elements.sendButton;
    modelSelect = elements.modelSelect;
    uploadButton = elements.uploadButton;
    imageUpload = elements.imageUpload;
    imagePreviewContainer = elements.imagePreviewContainer;

    // 初始化知识库节点ID
    knowledgeNodeId = config.knowledgeNodeId || null;
    knowledgeNodeIdFromContext = config.knowledgeNodeIdFromContext || null;
    isKnowledgeChat = config.isKnowledgeChat || false;

    // 验证必要的 DOM 元素
    if (!chatMessages || !userInput || !sendButton || !modelSelect || !uploadButton || !imageUpload || !imagePreviewContainer) {
        console.error("消息处理器错误: 部分必要的 DOM 元素未找到。");
        return false; // 初始化失败
    }

    // 请求父页面状态
    requestParentState();

    return true; // 初始化成功
}

// 滚动监听函数，处理滚动事件
function handleScroll() {
    if (!chatMessages) return; // 添加检查
    
    // 获取当前的滚动位置
    const currentScrollTop = chatMessages.scrollTop;
    
    if (isAtBottom()) {
        // 如果滚动到底部
        if (!autoScrollEnabled) {
             // 如果之前是手动滚动状态，现在恢复自动滚动
            userHasScrolled = false; // 重置用户滚动标记
            autoScrollEnabled = true;
        }
    } else {
        // 如果不在底部，判断是否是向上滚动
        if (autoScrollEnabled) {
        }
        userHasScrolled = true; // 标记用户手动滚动过
        autoScrollEnabled = false; // 暂停自动滚动
    }
}

// 设置消息区域的滚动监听器
function setupScrollListener(conversationId) {
    if (!chatMessages) return;
    if (chatMessages._scrollListenerAttachedFor === conversationId) return; // 避免重复绑定

    // 清除旧监听器
    if (chatMessages._scrollListener) {
        chatMessages.removeEventListener('scroll', chatMessages._scrollListener);
    }

    chatMessages._scrollListener = async function() {
        // 直接处理滚动事件的核心逻辑
        handleScroll();
            
        // 检查是否需要加载更多历史消息
        const state = await getParentState();
        if (chatMessages.scrollTop < 50 &&
            !isLoadingMore &&
            !chatMessages.dataset.noMoreMessages &&
            state.currentConversationId === conversationId)
        {
            if (!chatMessages._loadMoreTimeout) {
                 chatMessages._loadMoreTimeout = setTimeout(async () => {
                    const currentPage = chatMessages.dataset.currentPage ? parseInt(chatMessages.dataset.currentPage) : 0;
                    await loadMoreMessages(conversationId, currentPage + 1);
                    delete chatMessages._loadMoreTimeout;
                }, 100); // 例如 100ms 的延迟
            }
        }
    };
    
    chatMessages.addEventListener('scroll', chatMessages._scrollListener);
    chatMessages._scrollListenerAttachedFor = conversationId;
}

// --- PostMessage 通信 ---

// 向父页面请求状态
function requestParentState() {
    statePromise = new Promise(resolve => { statePromiseResolve = resolve; }); // Reset promise
    if (window.parent && window.parent !== window) {
        window.parent.postMessage({ type: 'GET_CONVERSATION_STATE' }, '*'); // Specify target origin in production
    } else {
        console.warn("[MessageHandler] Cannot access window.parent or it's the same window.");
        // Resolve with default state if no parent communication possible
        statePromiseResolve(parentState);
    }
}

// 监听来自父页面的消息
window.addEventListener('message', (event) => {
    // Add origin check in production!
    // if (event.origin !== "expected_parent_origin") return;

    const data = event.data;
    if (data && data.type === 'CONVERSATION_STATE') {
        parentState = data.payload || { isGuest: true, currentConversationId: null };
        if (statePromiseResolve) {
            statePromiseResolve(parentState);
            statePromiseResolve = null; // Ensure promise resolves only once
        }
    }
});

// 获取父页面状态 (异步)
async function getParentState() {
    // 每次调用都重置Promise，确保获取最新状态
    statePromise = new Promise(resolve => { statePromiseResolve = resolve; });

    // 请求最新状态
    requestParentState();

    // 等待响应
    return await statePromise;
}

// --- 消息发送与接收 ---

/**
 * 发送消息到后端，并处理响应。
 * @param {object} [context={}] - 可选的上下文对象，用于区分不同类型的聊天（例如知识库聊天）。
 * @param {string} [context.type] - 聊天类型 ('knowledge' 或 undefined/null for normal chat).
 * @param {string} [context.knowledgeNodeId] - 如果是知识库聊天，则为节点ID。
 */
async function sendMessage(context = {}) { // Explicitly accept context parameter

    // 如果当前有流在进行，则不允许发送新消息
    if (currentStream) {
        return;
    }

    // 获取父页面状态
    const state = await getParentState();
    const isGuest = state.isGuest;
    const currentCid = state.currentConversationId;

    if (isProcessingMessage) {
        return;
    }

    const text = userInput.value.trim();
    const selectedModel = modelSelect.value;

    if (!text && !currentImageFile) {
        return; // 没有内容则不发送
    }
    
    // 用户发送消息时，强制启用自动滚动
    userHasScrolled = false;
    autoScrollEnabled = true;

    // 确保之前的请求已完全清理
    if (currentController) {
        try {
            // 如果存在之前的控制器，确保它已被取消
            if (!currentController.signal.aborted) {
                currentController.abort("新请求开始，取消旧请求");
            }
        } catch (e) {
            console.warn("取消旧请求时出错:", e);
        }
        currentController = null;
    }

    // 重置所有状态
    isProcessingMessage = true;
    isPaused = false;
    sendButton.disabled = true;

    // 1. 显示用户消息
    let userMessageText = text;
    if (currentImageFile) {
        userMessageText += (text ? "\n" : "") + `[图片: ${currentImageFile.name}]`;
    }
    if (userMessageText) {
        addMessage('user', userMessageText);
    }

    // 2. 准备 FormData
    const apiUrl = '/wkg/api/unified-chat/stream';  // 改为使用统一聊天接口

    // 创建AbortController用于取消请求
    currentController = new AbortController();
    currentStream = currentController; // 保存引用，以便在terminateConversation中使用

    // 创建符合UnifiedChatRequest格式的请求参数
    const unifiedRequestData = {
        prompt: text || '',
        model: selectedModel,
        // Conditionally set conversationId or knowledgeNodeId
        conversationId: !context.type && !isGuest && currentCid ? currentCid : null,
        knowledgeNodeId: context.type === 'knowledge' ? context.knowledgeNodeId : null
    };

    // 添加深度思考状态
    if (window.deepThinkingManager) {
        // 根据聊天类型确定深度思考按钮ID
        let deepThinkingId;
        if (context.type === 'knowledge') {
            deepThinkingId = (context.chatId || 'kb-chat') + '-deep-thinking';
        } else {
            deepThinkingId = 'general-deep-thinking';
        }
        
        const deepThinkingEnabled = window.deepThinkingManager.getState(deepThinkingId);
        if (typeof deepThinkingEnabled === 'boolean') {
            unifiedRequestData.enableThinking = deepThinkingEnabled;
    
        }
    }

    // 添加增强检索状态（仅知识库聊天）
    if (context.type === 'knowledge') {
        const enhancedRetrievalButtonId = (context.chatId || 'kb-chat') + '-enhanced-retrieval';
        const enhancedRetrievalButton = document.getElementById(enhancedRetrievalButtonId);
        
        if (enhancedRetrievalButton && enhancedRetrievalButton.enhancedRetrievalToggle) {
            const enhancedRetrievalEnabled = enhancedRetrievalButton.enhancedRetrievalToggle.getEnabled();
            unifiedRequestData.enableEnhancedRetrieval = enhancedRetrievalEnabled;
    
        } else {
            // 默认开启增强检索
            unifiedRequestData.enableEnhancedRetrieval = true;
    
        }
    }

    // 准备请求选项
    let requestOptions;
    if (currentImageFile) {
        // 如果有图片，继续使用FormData
        const formData = new FormData();
        formData.append('prompt', text || '');
        formData.append('model', selectedModel);
        // Conditionally add conversationId or knowledgeNodeId to FormData
        if (!context.type && !isGuest && currentCid) {
            formData.append('conversationId', currentCid);
        }
        if (context.type === 'knowledge' && context.knowledgeNodeId) {
            formData.append('knowledgeNodeId', context.knowledgeNodeId);
        }
        // 添加深度思考状态到FormData
        if (unifiedRequestData.enableThinking !== undefined) {
            formData.append('enableThinking', unifiedRequestData.enableThinking.toString());
        }
        // 添加增强检索状态到FormData
        if (unifiedRequestData.enableEnhancedRetrieval !== undefined) {
            formData.append('enableEnhancedRetrieval', unifiedRequestData.enableEnhancedRetrieval.toString());
        }
        formData.append('image', currentImageFile);
        
        requestOptions = {
            method: 'POST',
            body: formData,
            signal: currentController.signal
        };
    } else {
        // 纯文本请求使用JSON格式
        requestOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(unifiedRequestData),
            signal: currentController.signal
        };
    }


    // 3. 清理输入框和预览
    userInput.value = '';
    userInput.style.height = 'auto';
    clearImagePreview();

    // 4. 检查并自动更新标题 (仅非游客)
    if (!isGuest && currentCid) {
        // 计算非欢迎消息的数量（排除欢迎消息）
        const messageCount = chatMessages.querySelectorAll('.message:not(.welcome-message)').length;

        
        if (messageCount === 1 && text) {

            // 直接调用chat-history.js中的函数更新标题
            await checkAndUpdateConversationTitle(currentCid, text);
        }
    }

    // 5. 创建 AI 响应占位符
    const aiMessageElement = document.createElement('div');
    aiMessageElement.classList.add('message', 'ai-message');

    // 创建加载动画容器
    const loadingContainer = document.createElement('div');
    loadingContainer.classList.add('ai-loading-container');

    const loadingAnimation = document.createElement('div');
    loadingAnimation.classList.add('ai-loading-animation');

    // 添加三个动画点
    for (let i = 0; i < 3; i++) {
        const dot = document.createElement('div');
        dot.classList.add('ai-loading-dot');
        loadingAnimation.appendChild(dot);
    }

    loadingContainer.appendChild(loadingAnimation);
    aiMessageElement.appendChild(loadingContainer);

    const thinkingDetails = document.createElement('details');
    thinkingDetails.style.display = 'none'; // 默认隐藏
    const thinkingSummary = document.createElement('summary');
    thinkingSummary.innerHTML = '思考中... <span class="thinking-indicator"></span>'; // 包含动画指示器
    const thinkingContentElement = document.createElement('div');
    thinkingContentElement.classList.add('thinking-content');
    thinkingDetails.appendChild(thinkingSummary);
    thinkingDetails.appendChild(thinkingContentElement);
    
    // 创建文档来源显示区域（仅在知识库聊天中显示）
    const documentSourcesContainer = document.createElement('div');
    documentSourcesContainer.classList.add('document-sources-container');
    documentSourcesContainer.style.display = 'none'; // 默认隐藏
    
    const aiContentElement = document.createElement('div');
    aiContentElement.classList.add('ai-main-content');
    
    aiMessageElement.appendChild(documentSourcesContainer);
    aiMessageElement.appendChild(thinkingDetails);
    aiMessageElement.appendChild(aiContentElement);
    chatMessages.appendChild(aiMessageElement);
    
    // 添加占位符后，立即尝试滚动到底部（如果自动滚动已启用）
    // 无需强制重置 userHasScrolled 和 autoScrollEnabled，让 scrollToBottom 自行判断
    scrollToBottom(); 
    
    // 显示暂停按钮，隐藏发送按钮
    togglePauseButton(true);

    // 6. 发起请求并处理 SSE 流

    // 增加参数验证
    if (!selectedModel || selectedModel === "") {
        console.error("[MessageHandler] 错误: 未选择模型!");
        if (aiContentElement) {
            aiContentElement.innerHTML = `<span class="text-danger">错误: 请选择一个AI模型后重试</span>`;
        }
        isProcessingMessage = false;
        sendButton.disabled = false;
        return;
    }

    try {
        const response = await fetch(apiUrl, requestOptions);


        if (!response.ok) {
            let errorMsg = `HTTP error! status: ${response.status}`;
            try {
                const errorData = await response.json();
                errorMsg = errorData.message || errorMsg;
                console.error(`[MessageHandler] API错误详情:`, errorData);
            } catch (e) {
                console.error(`[MessageHandler] 无法解析API错误详情:`, e);
            }
            throw new Error(errorMsg);
        }

        await processStream(response, aiMessageElement, thinkingDetails, thinkingContentElement, aiContentElement, context, documentSourcesContainer);

    } catch (error) {
        console.error('[MessageHandler] 发送消息或处理流时出错:', error);
        if (error.name === 'AbortError') {
        } else {
            if (aiContentElement) aiContentElement.innerHTML = `<span class="text-danger">错误: ${error.message || '未知错误'}</span>`;
        }
        thinkingDetails.style.display = 'none';
        aiMessageElement.classList.remove('thinking');

        // 隐藏加载动画
        const loadingContainer = aiMessageElement.querySelector('.ai-loading-container');
        if (loadingContainer) {
            loadingContainer.style.display = 'none';
        }

        scrollToBottom();
    } finally {
        isProcessingMessage = false;
        currentStream = null;
        currentController = null;
        // 修复按钮状态恢复逻辑：消息发送完成后应该恢复按钮可用状态
        sendButton.disabled = false;
        // 显示发送按钮，隐藏暂停按钮
        togglePauseButton(false);
    }
}

// 处理 SSE 流 - 支持 Ollama v0.9.0 字段分离格式
async function processStream(response, aiMessageElement, thinkingDetails, thinkingContentElement, aiContentElement, context, documentSourcesContainer) {
    const reader = response.body.getReader();
    
    // 显示暂停按钮，隐藏发送按钮
    togglePauseButton(true);

    const decoder = new TextDecoder();
    let buffer = "";
    let done = false;
    let currentAiText = "";
    let currentThinkingText = "";
    let hasThinkingContent = false;
    let firstChunkReceived = false;
    let hasAddedCodeBlocks = false;

    // 获取加载动画容器
    const loadingContainer = aiMessageElement.querySelector('.ai-loading-container');

    const triggerCodeBlockProcessing = () => {
        const newCodeBlocks = aiContentElement.querySelectorAll('pre code:not(.processed)');
        if (newCodeBlocks.length > 0) {
            hasAddedCodeBlocks = true;
            newCodeBlocks.forEach(block => {
                block.classList.add('processed');
            });
        }
    };

    const markdownRenderer = default_renderer(aiContentElement);
    const markdownParser = parser(markdownRenderer);
    
    const smartScrollToBottom = () => {
        scrollToBottom();
    };

    while (!done) {
        if (isPaused) {
            done = true;
            break;
        }
        
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        if (value) {
            const rawChunk = decoder.decode(value, { stream: true });
            buffer += rawChunk;

            let messageBoundary;
            // 处理SSE格式的消息
            while ((messageBoundary = buffer.indexOf('\n\n')) >= 0) {
                const message = buffer.substring(0, messageBoundary);
                buffer = buffer.substring(messageBoundary + 2);

                let accumulatedData = "";
                // 提取data行的内容
                message.split('\n').forEach(line => {
                    if (line.startsWith('data:')) {
                        let dataPayload = line.substring(5).replace(/__NEWLINE__/g, '\n');
                        accumulatedData += dataPayload;
                    }
                });

                // 处理累积的数据 - 支持v0.9.0格式
                if (accumulatedData.length > 0) {
                    // 如果这是第一个数据块，隐藏加载动画
                    if (!firstChunkReceived && loadingContainer) {
                        loadingContainer.style.display = 'none';
                        firstChunkReceived = true;
                    }

                    // 处理不同类型的内容
                    if (accumulatedData.startsWith('THINKING:')) {
                        // 处理思考内容
                        const thinkingText = accumulatedData.substring(9); // 移除 "THINKING:" 前缀
                        currentThinkingText += thinkingText;
                        thinkingContentElement.textContent = currentThinkingText;
                        if (thinkingText.trim()) {
                            hasThinkingContent = true;
                            thinkingDetails.style.display = '';
                        }
                        
                    } else if (accumulatedData.startsWith('SOURCES:')) {
                        // 处理文档来源信息（仅在知识库聊天中）
                        if (context.type === 'knowledge') {
                            const sourcesText = accumulatedData.substring(8); // 移除 "SOURCES:" 前缀
                            parseAndDisplayDocumentSources(sourcesText, documentSourcesContainer);
                        }
                        
                    } else if (accumulatedData.startsWith('CONTENT:')) {
                        // 处理主要内容
                        const mainText = accumulatedData.substring(8); // 移除 "CONTENT:" 前缀
                        currentAiText += mainText;
                        parser_write(markdownParser, mainText);
                        triggerCodeBlockProcessing();
                        
                    } else if (accumulatedData.startsWith('DONE:')) {
                        // 处理完成标识
        
                        
                    } else {
                        // 处理其他内容（如工具调用结果等）
        
                    }
                    
                    // 每次处理完数据后尝试滚动
                    smartScrollToBottom();
                }
            }
        }
    }

    // 流结束处理
    parser_end(markdownParser);
    
    // 立即处理代码块和复制按钮
    postProcessRenderedContent(aiContentElement);
    
    scrollToBottom();

    // 如果没有思考内容，隐藏思考区域
    if (!hasThinkingContent) {
        thinkingDetails.style.display = 'none';
    }

    // 代码高亮处理 - 增强版，确保复制按钮始终存在
    if (hasAddedCodeBlocks) {
        setTimeout(() => {
            // 再次调用postProcessRenderedContent确保所有代码块都有复制按钮
            postProcessRenderedContent(aiContentElement);
        }, 100);
    }
    
    // 代码高亮后再次滚动
    setTimeout(() => {
        if (hasAddedCodeBlocks) {
             scrollToBottom();
        }
    }, 150);
    
    // 清理状态
    isProcessingMessage = false;
    currentStream = null;
    isPaused = false;
    togglePauseButton(false);
}

// 切换暂停/发送按钮显示状态
function togglePauseButton(showPause) {
    try {
        // 直接修改DOM元素状态
        if (showPause) {
            // 显示暂停按钮，隐藏发送按钮
            if (sendButton) sendButton.style.display = 'none';
            if (document.getElementById('pause-button')) {
                document.getElementById('pause-button').style.display = 'block';
            }
        } else {
            // 隐藏暂停按钮，显示发送按钮
            if (sendButton) sendButton.style.display = 'block';
            if (document.getElementById('pause-button')) {
                document.getElementById('pause-button').style.display = 'none';
            }
        }
    } catch (error) {
        console.error("切换暂停按钮时出错:", error);
    }
}

// 终止对话
function terminateConversation() {

    if (!isProcessingMessage) {
        return false;
    }
    
    try {
        // 使用AbortController取消请求
        if (currentController && !currentController.signal.aborted) {
            try {
                currentController.abort("用户终止了对话");
            } catch (e) {
                console.warn("取消请求时出错:", e);
            }
        }
        
        // 标记为已终止
        isPaused = true; // 仍使用isPaused标识终止状态

        // 解除处理状态，允许发送新消息
        isProcessingMessage = false;
        
        // 显示发送按钮，隐藏暂停按钮
        togglePauseButton(false);
        
        // 移除加载动画
        const loadingContainer = document.querySelector('.ai-loading-container');
        if (loadingContainer) {
            loadingContainer.remove();
        }
        
        // 隐藏思考过程
        const thinkingDetails = document.querySelector('details.thinking-details');
        if (thinkingDetails) {
            thinkingDetails.style.display = 'none';
        }
        
        // 添加延迟，确保后端有足够时间处理连接关闭
        // 这有助于避免在快速终止后立即发送新请求导致的状态不一致
        setTimeout(() => {
            // 清理状态
            currentController = null;
            currentStream = null;
        }, 500);
        
        return true;
    } catch (error) {
        console.error("终止对话时出错:", error);
        return false;
    }
}

// 添加消息到对话 (sender: 'user' 或 'system')
function addMessage(sender, messageContent, isHtml = false) {
    if (!chatMessages) return;
    const messageElement = document.createElement('div');
    messageElement.classList.add('message', `${sender}-message`);

    const contentContainer = document.createElement('div'); // 使用容器包裹，方便处理
    if (sender === 'user') {
         // 用户消息直接设置文本，处理换行
        contentContainer.textContent = messageContent;
        contentContainer.innerHTML = contentContainer.innerHTML.replace(/\n/g, '<br>');
    } else if (isHtml) {
        // 系统HTML消息
        contentContainer.innerHTML = messageContent;
    } else {
        // 系统纯文本消息
        contentContainer.textContent = messageContent;
    }
    messageElement.appendChild(contentContainer);

    chatMessages.appendChild(messageElement);
    
    // 添加消息后，强制启用自动滚动并滚动
    // 仅当添加的是用户消息或AI思考结束后的消息时，才强制滚动
    // 对于历史消息渲染，不应强制改变用户当前的滚动状态
    if (sender === 'user' || sender === 'system') { // system 消息通常是错误提示
        userHasScrolled = false;
        autoScrollEnabled = true;
    }
    // 总是尝试滚动，让 scrollToBottom 根据 autoScrollEnabled 决定是否执行
    scrollToBottom(); 
    return messageElement;
}

// 显示历史 AI 消息 - 支持 v0.9.0 思考字段格式
function displayAiMessage(messageData) {
    const messageElement = document.createElement('div');
    messageElement.classList.add('message', 'ai-message');

    // 处理思考过程 (如果存在)
    let hasThinkingContent = false;
    
    // 检查不同可能的思考内容字段名 (兼容后端不同的字段命名)
    const thinkingContent = messageData.thinking_process || messageData.thinkingContent || messageData.thinking_content || messageData.thinking || '';
    
    if (thinkingContent && thinkingContent.trim()) {
        hasThinkingContent = true;
        const thinkingDetails = document.createElement('details');
        const thinkingSummary = document.createElement('summary');
        thinkingSummary.innerHTML = '思考过程 <span class="thinking-indicator-static"></span>';
        const thinkingContentEl = document.createElement('div');
        thinkingContentEl.classList.add('thinking-content');
        thinkingContentEl.textContent = thinkingContent;
        thinkingDetails.appendChild(thinkingSummary);
        thinkingDetails.appendChild(thinkingContentEl);
        messageElement.appendChild(thinkingDetails);
        
        console.debug('✅ 历史消息思考内容已加载:', thinkingContent.substring(0, 50) + '...');
    } else {
        console.debug('📝 历史消息没有思考内容:', messageData.id);
    }

    // 创建主内容容器
    const contentElement = document.createElement('div');
    contentElement.classList.add('ai-main-content');
    messageElement.appendChild(contentElement);

    // 使用streaming-markdown渲染主内容
    const markdownRenderer = default_renderer(contentElement);
    const markdownParser = parser(markdownRenderer);
    parser_write(markdownParser, messageData.content || '');
    parser_end(markdownParser);

    // 应用代码高亮和复制按钮
    postProcessRenderedContent(contentElement);

    return messageElement;
}

// 处理渲染后的内容 (修改，访问全局 hljs)
function postProcessRenderedContent(contentElement) {
    contentElement.querySelectorAll('pre code:not(.hljs)').forEach(block => {
        try {
            // 获取代码语言
            let language = '';
            if (block.className) {
                const langMatch = block.className.match(/language-([\w-]+)/);
                if (langMatch && langMatch[1]) {
                    language = langMatch[1];
                }
            }

            // 高亮代码
            if (typeof hljs !== 'undefined') {
                hljs.highlightElement(block);
            } else {
                console.warn("highlight.js (hljs) not found.");
            }

            const pre = block.parentElement;
            if (pre && pre.tagName === 'PRE') {
                // 添加语言标签
                if (language && !pre.hasAttribute('data-language')) {
                    pre.setAttribute('data-language', language);
                }

                // 添加复制按钮
                if (!pre.querySelector('.copy-btn')) {
                    const copyBtn = document.createElement('button');
                    copyBtn.classList.add('copy-btn');
                    copyBtn.innerHTML = '<i class="bi bi-clipboard"></i> 复制';
                    copyBtn.addEventListener('click', () => handleCodeCopy(block, copyBtn));
                    pre.appendChild(copyBtn);
                }
            }
        } catch (e) {
            console.error("应用代码高亮或添加复制按钮时出错:", e, block);
        }
    });
}

// 处理代码复制功能
function handleCodeCopy(codeBlock, button) {
    const code = codeBlock.textContent;
    navigator.clipboard.writeText(code)
        .then(() => showCopySuccess(button))
        .catch(err => {
            console.error('剪贴板API复制失败:', err);
            fallbackCopy(code, button); // 尝试旧方法
        });
}

// 显示复制成功的反馈
function showCopySuccess(button) {
    if (button._copyTimeout) clearTimeout(button._copyTimeout); // 清除之前的定时器
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="bi bi-check-lg"></i> 已复制';
    button.classList.add('copied', 'btn-success');
    button.classList.remove('btn-outline-secondary');
    button.disabled = true;

    button._copyTimeout = setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('copied', 'btn-success');
        button.classList.add('btn-outline-secondary');
        button.disabled = false;
        delete button._copyTimeout;
    }, 2000);
}

// 回退复制方法
function fallbackCopy(text, button) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed'; textArea.style.opacity = '0';
    document.body.appendChild(textArea);
    textArea.select();
    try {
        document.execCommand('copy');
        showCopySuccess(button);
    } catch (err) {
        console.error('回退复制方法失败:', err);
        button.innerHTML = '<i class="bi bi-x-lg"></i> 复制失败';
        button.classList.add('btn-danger');
        button.classList.remove('btn-outline-secondary');
        button.disabled = true;
        setTimeout(() => {
             button.innerHTML = '<i class="bi bi-clipboard"></i> 复制';
             button.classList.remove('btn-danger');
             button.classList.add('btn-outline-secondary');
             button.disabled = false;
        }, 2000);
    }
    document.body.removeChild(textArea);
}

// --- 辅助功能 ---
function scrollToBottom(smooth = false) {
    if (!chatMessages) return;
    
    // 如果自动滚动未启用，则不执行滚动
    if (!autoScrollEnabled) {
        return;
    }
    
    const scrollHeight = chatMessages.scrollHeight;
    const clientHeight = chatMessages.clientHeight;
    
    // 总是滚动到底部，根据 smooth 参数决定行为
    chatMessages.scrollTo({
        top: scrollHeight,
        behavior: smooth ? 'smooth' : 'auto' // 根据参数决定平滑或瞬时
    });
}

// 检查是否滚动到底部（或接近底部）
function isAtBottom() {
    if (!chatMessages) return true;
    
    const scrollHeight = chatMessages.scrollHeight;
    const clientHeight = chatMessages.clientHeight;
    const scrollTop = chatMessages.scrollTop;
    
    // 如果滚动位置在距离底部20px内，认为是滚动到底部
    return (scrollHeight - scrollTop - clientHeight) < 20;
}

// 显示欢迎消息
function showWelcomeMessage() {
    if (!chatMessages) return;
    chatMessages.innerHTML = '';
    const welcomeMessage = document.createElement('div');
    welcomeMessage.classList.add('message', 'welcome-message');
    welcomeMessage.innerHTML = '<p>你好！我是AI助手，有什么可以帮你的吗？</p>';
    chatMessages.appendChild(welcomeMessage);
    scrollToBottom();
}

// --- 加载历史消息 ---
// 为指定对话加载消息 (获取父页面状态)
async function loadMessagesForConversation(conversationId, isNew = false, pageSize = 30) {
    if (!chatMessages) {
        console.error("无法加载消息：聊天消息容器不存在");
        return;
    }
    // Only error if conversationId is invalid AND it's not a new/welcome state
    if (!conversationId && !isNew) {
        console.error("无法加载消息：会话 ID 无效 (且非新会话)");
        return;
    }

    isLoadingMore = false;
    loadingConversationId = conversationId;
    
    // 切换会话时，重置滚动状态
    userHasScrolled = false;
    autoScrollEnabled = true;
    
    delete chatMessages.dataset.currentPage;
    delete chatMessages.dataset.noMoreMessages;

    if (isNew) {
        showWelcomeMessage();
        chatMessages.dataset.noMoreMessages = "true";
        setupScrollListener(conversationId);
        return;
    }

    // For existing conversations, we *must* have a valid ID
    if (!conversationId) {
        console.error("loadMessagesForConversation internal error: trying to load existing conv with null ID");
        return;
    }

    // 定义父页面状态变量
    let parentStateData = { currentConversationId: conversationId };
    
    // 获取父页面状态，但无论state如何都继续加载消息（移除阻断）
    try {
        parentStateData = await getParentState();
    } catch (error) {
        console.warn("[MessageHandler] 获取父页面状态失败，但继续加载消息：", error);
    }

    // 显示加载指示器
    const loadingIndicator = document.createElement('div');
    loadingIndicator.classList.add('history-load-indicator');
    loadingIndicator.textContent = '加载消息中...';
    chatMessages.innerHTML = '';
    chatMessages.appendChild(loadingIndicator);

    // 加载现有会话的初始消息
    try {
        const response = await fetch(`/wkg/api/conversations/${conversationId}/messages?page=0&size=${pageSize}`);
        // 检查会话是否已经切换
        if (parentStateData.currentConversationId !== conversationId) {
            if (loadingIndicator && loadingIndicator.parentNode) {
                loadingIndicator.remove();
            }
            return; // 会话已切换，终止加载
        }

        if (!response.ok) {
            if (response.status === 404) {
                loadingIndicator.remove();
                chatMessages.dataset.noMoreMessages = "true"; // 没有更多历史消息
                return;
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const messages = data.content || [];
        const totalPages = data.totalPages || 1;

        chatMessages.innerHTML = ''; // 清空

        if (messages.length > 0) {
            renderHistoryMessages(messages, chatMessages, true);
        } else {
            showWelcomeMessage();
        }

        // 设置分页和滚动监听
        chatMessages.dataset.currentPage = "0";
        if (totalPages <= 1) {
            chatMessages.dataset.noMoreMessages = "true";
            if(messages.length > 0) showNoMoreMessagesIndicator(); // 如果有消息但只有一页
        } else {
            delete chatMessages.dataset.noMoreMessages;
        }
        setupScrollListener(conversationId);

    } catch (error) {
        console.error(`加载会话 ${conversationId} 消息失败:`, error);
        chatMessages.innerHTML = '';
        showWelcomeMessage();
        addMessage('system', `<span class="text-danger">加载会话消息失败: ${error.message}</span>`, true);
    }
}

// 加载更多历史消息 (获取父页面状态)
async function loadMoreMessages(conversationId, page, pageSize = 30) {
    // Get parent state to confirm current conversation hasn't changed
    let parentStateData = {};
    try {
        parentStateData = await getParentState();
    } catch (error) {
        console.warn("[MessageHandler] 获取父页面状态失败", error);
        return; // 无法确认当前会话，停止加载
    }

    const currentCid = parentStateData.currentConversationId;

    if (isLoadingMore || !chatMessages || chatMessages.dataset.noMoreMessages || currentCid !== conversationId) {
        return;
    }

    isLoadingMore = true;
    loadingConversationId = conversationId;

    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'message-loading-indicator text-center text-muted small py-2';
    loadingIndicator.innerHTML = '<span><i class="bi bi-arrow-clockwise me-1"></i>加载历史消息...</span>';
    chatMessages.insertBefore(loadingIndicator, chatMessages.firstChild);

    const initialScrollHeight = chatMessages.scrollHeight;
    const initialScrollTop = chatMessages.scrollTop;
    const initialAutoScroll = autoScrollEnabled;

    try {
        const response = await fetch(`/wkg/api/conversations/${conversationId}/messages?page=${page}&size=${pageSize}`);
        // 检查会话是否已经切换
        if (parentStateData.currentConversationId !== conversationId) {
            if (loadingIndicator && loadingIndicator.parentNode) {
                loadingIndicator.remove();
            }
            return; // 会话已切换
        }

        if (!response.ok) {
            if (response.status === 404) {
                loadingIndicator.remove();
                chatMessages.dataset.noMoreMessages = "true"; // 没有更多历史消息
                return;
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const messages = data.content || [];
        const totalPages = data.totalPages || 1;

        loadingIndicator.remove();

        if (messages.length > 0) {
            // 加载历史消息时，暂时禁用状态检测以避免不必要的滚动
            const prevUserScrolled = userHasScrolled;
            userHasScrolled = true;
            autoScrollEnabled = false;
            
            const tempContainer = document.createElement('div');
            renderHistoryMessages(messages, tempContainer, false);
            const fragment = document.createDocumentFragment();
            while(tempContainer.firstChild) fragment.appendChild(tempContainer.firstChild);
            chatMessages.insertBefore(fragment, chatMessages.firstChild);

            chatMessages.dataset.currentPage = page.toString();

            // 恢复滚动位置
            const newScrollHeight = chatMessages.scrollHeight;
            chatMessages.scrollTop = initialScrollTop + (newScrollHeight - initialScrollHeight);
            
            // 恢复之前的滚动状态
            userHasScrolled = prevUserScrolled;
            autoScrollEnabled = initialAutoScroll;

            if (page >= totalPages - 1) {
                chatMessages.dataset.noMoreMessages = "true";
                showNoMoreMessagesIndicator();
            }
        } else {
            chatMessages.dataset.noMoreMessages = "true";
            showNoMoreMessagesIndicator();
        }
    } catch (error) {
        console.error("加载更多历史消息失败:", error);
        loadingIndicator.remove();
        showLoadMoreErrorIndicator(conversationId, page, pageSize);
    } finally {
        isLoadingMore = false;
    }
}

// 显示没有更多历史消息的提示
function showNoMoreMessagesIndicator() {
    if (!chatMessages || chatMessages.querySelector('.message-no-more-indicator')) return;
    removeLoadMoreIndicators(); // 移除可能存在的加载或错误提示
    const noMoreIndicator = document.createElement('div');
    noMoreIndicator.className = 'message-no-more-indicator text-center text-muted small py-2';
    chatMessages.insertBefore(noMoreIndicator, chatMessages.firstChild);
}

// 显示加载更多消息失败的提示
function showLoadMoreErrorIndicator(conversationId, page, pageSize) {
    if (!chatMessages) return;
    
    // 先移除任何已存在的指示器
    removeLoadMoreIndicators();
    
    const errorIndicator = document.createElement('div');
    errorIndicator.className = 'message-error-indicator text-center text-danger small py-2';
    errorIndicator.innerHTML = '<span><i class="bi bi-exclamation-triangle me-1"></i>加载失败，点击重试</span>';
    errorIndicator.style.cursor = 'pointer';
    errorIndicator.onclick = async () => {
        // 获取最新状态再重试，确保状态一致性
        let parentStateData = {};
        try {
            parentStateData = await getParentState();
        } catch (error) {
            console.warn("[MessageHandler] 获取父页面状态失败", error);
            return; // 无法确认当前会话，停止重试
        }
        
        if (parentStateData.currentConversationId === conversationId) {
             errorIndicator.remove();
             loadMoreMessages(conversationId, page, pageSize);
        }
    };
    chatMessages.insertBefore(errorIndicator, chatMessages.firstChild);
}

// 移除顶部的加载/错误/无更多提示
function removeLoadMoreIndicators() {
    chatMessages?.querySelector('.message-loading-indicator')?.remove();
    chatMessages?.querySelector('.message-loading-error-indicator')?.remove();
    chatMessages?.querySelector('.message-no-more-indicator')?.remove();
}

// 渲染历史消息到指定容器 (修改，使用 parser object)
function renderHistoryMessages(messages, container, isInitialLoad) {
    if (!messages || messages.length === 0) return;
    try {
        messages.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));
        const fragment = document.createDocumentFragment();
        messages.forEach(msg => {
            if (!msg) return;
            if (msg.sender === 'user') {
                let content = msg.content || '';
                if (msg.imagePath) {
                    // 简化显示，不尝试加载历史图片
                    content += `\n[图片: ${msg.imagePath.split('/').pop()}]`;
                }
                const userMsgEl = document.createElement('div');
                userMsgEl.classList.add('message', 'user-message');
                const p = document.createElement('div'); // Use div for potential HTML
                p.textContent = content;
                p.innerHTML = p.innerHTML.replace(/\n/g, '<br>'); // Handle newlines
                userMsgEl.appendChild(p);
                fragment.appendChild(userMsgEl);
            } else if (msg.sender === 'ai') {
                const aiMsgEl = displayAiMessage(msg);
                if (aiMsgEl) fragment.appendChild(aiMsgEl);
            }
        });
        container.appendChild(fragment);
        if (isInitialLoad) {
            // 初始加载时，重置状态并滚动到底部
            userHasScrolled = false;
            autoScrollEnabled = true;
            // 使用 setTimeout 确保 DOM 更新完成
            setTimeout(() => scrollToBottom(), 0); 
        } else {
            // 加载更多历史消息时不应自动滚动到底部
            // 仅需恢复滚动位置，这在 loadMoreMessages 中处理
        }
    } catch (error) {
        console.error("渲染历史消息时出错:", error);
    }
}

// --- 图片处理 (修改，使用父页面的 showToast) ---
function handleImageUpload(event) {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
        if (file.size > 30 * 1024 * 1024) {
             // Need to use postMessage or have showToast available globally
             window.parent?.postMessage({ type: 'SHOW_TOAST', payload: { message: "图片大小不能超过 30MB", type: "warning" }}, '*');
             if (imageUpload) imageUpload.value = '';
             return;
        }
        currentImageFile = file;
        displayImagePreview(file);
        if (sendButton) sendButton.disabled = false;
    } else if (file) {
        window.parent?.postMessage({ type: 'SHOW_TOAST', payload: { message: "请选择图片文件", type: "warning" }}, '*');
        if (imageUpload) imageUpload.value = '';
    } else {
        // 没有选择文件或取消选择
        currentImageFile = null;
        clearImagePreview();
        if (sendButton && userInput) sendButton.disabled = userInput.value.trim() === '';
    }
    // 不再需要重置 input.value，除非有特定需求
}

function displayImagePreview(file) {
    if (!imagePreviewContainer) return;
    clearImagePreview();
    const previewElement = document.createElement('div');
    previewElement.classList.add('image-preview');
    const img = document.createElement('img');
    img.src = URL.createObjectURL(file);
    img.onload = () => URL.revokeObjectURL(img.src);
    img.alt = file.name;
    const removeButton = document.createElement('button');
    removeButton.classList.add('remove-image'); // 使用CSS定义的类
    removeButton.innerHTML = '&times;';
    removeButton.title = '移除图片';
    removeButton.type = 'button'; // 防止表单提交
    removeButton.onclick = () => {
        currentImageFile = null;
        clearImagePreview();
        if (sendButton && userInput) sendButton.disabled = userInput.value.trim() === '';
    };
    previewElement.appendChild(img);
    previewElement.appendChild(removeButton);
    imagePreviewContainer.appendChild(previewElement);
}

function clearImagePreview() {
    if (imagePreviewContainer) imagePreviewContainer.innerHTML = '';
    currentImageFile = null;
    if (imageUpload) imageUpload.value = ''; // 确保文件输入也被重置
}

// Function to check processing or loading state (needs async access to parent state)
async function isProcessingOrLoading() {
    const state = await getParentState();
    return isProcessingMessage || (isLoadingMore && loadingConversationId === state.currentConversationId);
}

// --- 暴露接口 (改为导出) ---
export {
    initialize,
    sendMessage,
    terminateConversation,
    handleImageUpload,
    loadMessagesForConversation,
    clearImagePreview
};

// 解析并显示文档来源信息
function parseAndDisplayDocumentSources(sourcesText, container) {
    if (!sourcesText || !sourcesText.trim()) {
        return;
    }
    
    // 解析来源文件信息，格式："来源文件: filename.pdf (节点ID: nodeId123)"
    const sourceLines = sourcesText.split('\n').filter(line => line.trim());
    const sources = [];
    
    sourceLines.forEach(line => {
        const match = line.match(/来源文件:\s*(.+?)\s*\(节点ID:\s*(.+?)\)/);
        if (match) {
            const fileName = match[1].trim();
            const nodeId = match[2].trim();
            sources.push({ fileName, nodeId });
        }
    });
    
    if (sources.length === 0) {
        return;
    }
    
    // 创建文档来源显示内容
    container.innerHTML = '';
    
    const sourcesHeader = document.createElement('div');
    sourcesHeader.classList.add('sources-header');
    sourcesHeader.innerHTML = '<i class="bi bi-file-text"></i> 参考文档';
    container.appendChild(sourcesHeader);
    
    const sourcesList = document.createElement('div');
    sourcesList.classList.add('sources-list');
    
    sources.forEach((source, index) => {
        const sourceItem = document.createElement('div');
        sourceItem.classList.add('source-item');
        
        const sourceLink = document.createElement('a');
        sourceLink.href = '#';
        sourceLink.classList.add('source-link');
        sourceLink.textContent = source.fileName;
        sourceLink.title = source.fileName; // 添加工具提示显示完整文档名
        sourceLink.setAttribute('data-node-id', source.nodeId);
        
        // 添加点击事件处理
        sourceLink.addEventListener('click', (e) => {
            e.preventDefault();
            handleDocumentSourceClick(source.nodeId, source.fileName);
        });
        
        sourceItem.appendChild(sourceLink);
        sourcesList.appendChild(sourceItem);
    });
    
    container.appendChild(sourcesList);
    container.style.display = 'block';
}

// 处理文档来源点击事件
function handleDocumentSourceClick(nodeId, fileName) {
    console.log('点击文档来源:', fileName, '节点ID:', nodeId);
    console.log('当前页面路径:', window.location.pathname);
    console.log('是否包含/knowledge:', window.location.pathname.includes('/knowledge'));
    
    // 检查是否在知识库页面，如果是则直接在右侧显示文档
    if (window.location.pathname.includes('/knowledge') || window.location.pathname.includes('/new-knowledge')) {
        console.log('在知识库页面，尝试在右侧显示文档');
        
        // 检查必要的函数是否存在
        console.log('updateMainView函数存在:', typeof window.updateMainView === 'function');
        console.log('updateKnowledgeChatNode函数存在:', typeof window.updateKnowledgeChatNode === 'function');
        
        try {
            // 直接调用知识库页面的相关函数来显示文档
            if (typeof window.updateMainView === 'function') {
                console.log('调用updateMainView函数');
                
                // 设置当前节点信息
                if (window.currentNodeId !== undefined) {
                    window.currentNodeId = nodeId;
                    console.log('设置currentNodeId:', nodeId);
                }
                if (window.currentNodeName !== undefined) {
                    window.currentNodeName = fileName;
                    console.log('设置currentNodeName:', fileName);
                }
                if (window.currentNodeType !== undefined) {
                    window.currentNodeType = 'FILE';
                    console.log('设置currentNodeType: FILE');
                }
                
                // 更新主视图显示文档
                window.updateMainView('FILE', fileName, nodeId);
                console.log('已调用updateMainView');
                
                // 更新知识库聊天组件
                if (typeof window.updateKnowledgeChatNode === 'function') {
                    window.updateKnowledgeChatNode(nodeId, fileName, 'FILE');
                    console.log('已调用updateKnowledgeChatNode');
                }
                
                // 显示简单的提示
                // showDocumentOpenNotification(fileName, false); // 用户不需要弹窗提示，已注释
                
                console.log('已在右侧面板中打开文档:', fileName);
                return; // 成功处理，直接返回
            } else {
                console.error('updateMainView函数不存在');
            }
        } catch (error) {
            console.error('在右侧面板打开文档时出错:', error);
        }
        
        // 如果上面的方法失败，尝试备选方案：下载文档
        console.warn('无法在右侧面板打开文档，使用下载方案');
        fallbackDownloadDocument(nodeId, fileName);
        return; // 确保不会继续执行下面的新标签页逻辑
    }
    
    // 如果不在知识库页面，则在新标签页中打开
    console.log('不在知识库页面，在新标签页中打开');
    try {
        const knowledgeUrl = `/wkg/knowledge?nodeId=${nodeId}&highlight=true`;
        const newWindow = window.open(knowledgeUrl, '_blank');
        
        if (newWindow) {
            showDocumentOpenNotification(fileName, true);
        } else {
            // 弹窗被阻止，下载文档
            fallbackDownloadDocument(nodeId, fileName);
        }
    } catch (error) {
        console.error('打开文档时出错:', error);
        fallbackDownloadDocument(nodeId, fileName);
    }
}

// 备选方案：下载文档
function fallbackDownloadDocument(nodeId, fileName) {
    console.warn('使用备选方案：下载文档');
    const downloadUrl = `/wkg/api/knowledgebase/files/${nodeId}/download`;
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 显示文档打开通知
function showDocumentOpenNotification(fileName, isNewTab = false) {
    // 创建一个简单的通知提示
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #0d6efd, #6610f2);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        max-width: 300px;
        word-wrap: break-word;
        animation: slideInRight 0.3s ease-out;
    `;
    
    const message = isNewTab ? `📄 已在新标签页中打开文档：${fileName}` : `📄 已在右侧面板打开文档：${fileName}`;
    notification.innerHTML = message;
    
    // 添加动画样式
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(notification);
    
    // 3秒后自动消失
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// ... existing code ...