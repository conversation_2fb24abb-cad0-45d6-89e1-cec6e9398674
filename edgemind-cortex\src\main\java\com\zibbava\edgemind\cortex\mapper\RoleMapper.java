package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 根据用户ID查询角色列表
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    @Select("SELECT r.* FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1")
    List<Role> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID列表查询相关角色列表
     * 
     * @param userIds 用户ID列表
     * @return 角色列表
     */
    @Select("<script>" +
            "SELECT DISTINCT r.* FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            " AND r.status = 1" +
            "</script>")
    List<Role> selectByUserIds(@Param("userIds") List<Long> userIds);

    /**
     * 根据角色编码查询角色
     * 
     * @param roleCode 角色编码
     * @return 角色
     */
    @Select("SELECT * FROM sys_role WHERE role_code = #{roleCode} AND status = 1")
    Role selectByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 统计角色下的用户数量
     * 
     * @param roleId 角色ID
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM sys_user_role WHERE role_id = #{roleId}")
    Long countUsersByRoleId(@Param("roleId") Long roleId);

    /**
     * 查询所有启用的角色
     * 
     * @return 角色列表
     */
    @Select("SELECT * FROM sys_role WHERE status = 1 ORDER BY role_name")
    List<Role> selectAllEnabled();
}
