package com.zibbava.edgemind.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.server.dto.DownloadStatisticsDTO;
import com.zibbava.edgemind.server.entity.DownloadStatistics;
import com.zibbava.edgemind.server.mapper.DownloadStatisticsMapper;
import com.zibbava.edgemind.server.service.DownloadStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 下载统计服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DownloadStatisticsServiceImpl extends ServiceImpl<DownloadStatisticsMapper, DownloadStatistics> implements DownloadStatisticsService {

    private final DownloadStatisticsMapper downloadStatisticsMapper;

    @Override
    public void recordDownload(HttpServletRequest request, String fileName, Long fileSize, Integer downloadStatus) {
        try {
            DownloadStatistics statistics = new DownloadStatistics();
            statistics.setDownloadDate(LocalDate.now());
            statistics.setDownloadCount(1);
            statistics.setIpAddress(getClientIpAddress(request));
            statistics.setUserAgent(request.getHeader("User-Agent"));
            statistics.setReferer(request.getHeader("Referer"));
            statistics.setDownloadTime(LocalDateTime.now());
            statistics.setFileName(fileName);
            statistics.setFileSize(fileSize);
            statistics.setDownloadStatus(downloadStatus);
            
            save(statistics);
            log.info("记录下载统计成功: 文件={}, IP={}, 状态={}", fileName, statistics.getIpAddress(), downloadStatus);
        } catch (Exception e) {
            log.error("记录下载统计失败: 文件={}, 错误={}", fileName, e.getMessage(), e);
        }
    }

    @Override
    public DownloadStatisticsDTO getStatisticsOverview() {
        DownloadStatisticsDTO dto = new DownloadStatisticsDTO();
        
        // 获取基础统计数据
        dto.setTotalDownloads(getTotalDownloads());
        dto.setTodayDownloads(getTodayDownloads());
        dto.setThisMonthDownloads(getThisMonthDownloads());
        dto.setUniqueIpCount(getUniqueIpCount());
        
        // 获取趋势数据
        dto.setRecentSevenDays(getRecentSevenDaysStatistics());
        dto.setRecentThirtyDays(getRecentThirtyDaysStatistics());
        
        // 计算增长率
        dto.setTodayGrowthRate(calculateTodayGrowthRate());
        dto.setMonthGrowthRate(calculateMonthGrowthRate());
        
        return dto;
    }

    @Override
    public Long getTotalDownloads() {
        Long total = downloadStatisticsMapper.getTotalDownloads();
        return total != null ? total : 0L;
    }

    @Override
    public Long getTodayDownloads() {
        Long today = downloadStatisticsMapper.getTodayDownloads();
        return today != null ? today : 0L;
    }

    @Override
    public Long getThisMonthDownloads() {
        Long thisMonth = downloadStatisticsMapper.getThisMonthDownloads();
        return thisMonth != null ? thisMonth : 0L;
    }

    @Override
    public Long getUniqueIpCount() {
        Long uniqueIp = downloadStatisticsMapper.getUniqueIpCount();
        return uniqueIp != null ? uniqueIp : 0L;
    }

    @Override
    public List<Map<String, Object>> getRecentSevenDaysStatistics() {
        return downloadStatisticsMapper.getRecentSevenDaysStatistics();
    }

    @Override
    public List<Map<String, Object>> getRecentThirtyDaysStatistics() {
        return downloadStatisticsMapper.getRecentThirtyDaysStatistics();
    }

    @Override
    public List<Map<String, Object>> getStatisticsByDateRange(LocalDate startDate, LocalDate endDate) {
        return downloadStatisticsMapper.getStatisticsByDateRange(startDate, endDate);
    }

    @Override
    public String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (StringUtils.hasText(proxyClientIp) && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }
        
        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (StringUtils.hasText(wlProxyClientIp) && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 计算今日增长率（相比昨日）
     */
    private Double calculateTodayGrowthRate() {
        try {
            Long todayCount = getTodayDownloads();
            // 这里可以添加获取昨日下载量的逻辑
            // 暂时返回0.0
            return 0.0;
        } catch (Exception e) {
            log.error("计算今日增长率失败", e);
            return 0.0;
        }
    }

    /**
     * 计算本月增长率（相比上月）
     */
    private Double calculateMonthGrowthRate() {
        try {
            Long thisMonthCount = getThisMonthDownloads();
            // 这里可以添加获取上月下载量的逻辑
            // 暂时返回0.0
            return 0.0;
        } catch (Exception e) {
            log.error("计算本月增长率失败", e);
            return 0.0;
        }
    }
}