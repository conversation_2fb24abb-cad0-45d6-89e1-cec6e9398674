package com.zibbava.edgemind.cortex.common.exception;

import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import lombok.Getter;

/**
 * 统一业务异常类
 * 用于封装业务逻辑异常，关联错误码和错误消息
 */
@Getter
public class BusinessException extends RuntimeException {

    /**
     * 业务错误码
     */
    private final ResultCode resultCode;

    /**
     * 错误详情
     */
    private final String detailMessage;

    /**
     * 使用预定义的业务错误码创建异常
     * 
     * @param resultCode 业务错误码枚举
     */
    public BusinessException(ResultCode resultCode) {
        // 调用父类构造器，设置message为错误码的默认消息
        super(resultCode.getMessage());
        this.resultCode = resultCode;
        this.detailMessage = resultCode.getMessage();
    }

    /**
     * 使用预定义的业务错误码和自定义错误消息创建异常
     * 
     * @param resultCode 业务错误码枚举
     * @param detailMessage 自定义错误详情消息，将覆盖错误码的默认消息
     */
    public BusinessException(ResultCode resultCode, String detailMessage) {
        // 调用父类构造器，设置message为自定义详情消息
        super(detailMessage);
        this.resultCode = resultCode;
        this.detailMessage = detailMessage;
    }

    /**
     * 使用预定义的业务错误码、自定义错误消息和原始异常创建异常
     * 
     * @param resultCode 业务错误码枚举
     * @param detailMessage 自定义错误详情消息
     * @param cause 原始异常
     */
    public BusinessException(ResultCode resultCode, String detailMessage, Throwable cause) {
        super(detailMessage, cause);
        this.resultCode = resultCode;
        this.detailMessage = detailMessage;
    }

    /**
     * 获取错误码
     * 
     * @return 错误码数值
     */
    public int getCode() {
        return this.resultCode.getCode();
    }

    /**
     * 获取默认消息
     * 
     * @return 错误码默认消息
     */
    public String getResultMsg() {
        return this.resultCode.getMessage();
    }
    
    @Override
    public String toString() {
        return "BusinessException{" +
                "code=" + this.resultCode.getCode() +
                ", message='" + this.getMessage() + '\'' +
                '}';
    }
} 