package com.zibbava.edgemind.cortex.store;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zibbava.edgemind.cortex.demo.util.StopWordsLoader;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.filter.Filter;
import dev.langchain4j.store.embedding.filter.comparison.*;
import dev.langchain4j.store.embedding.filter.logical.*;
import io.weaviate.client.Config;
import io.weaviate.client.WeaviateClient;
import io.weaviate.client.base.Result;
import io.weaviate.client.v1.data.model.WeaviateObject;
import io.weaviate.client.v1.filters.WhereFilter;
import io.weaviate.client.v1.graphql.model.GraphQLResponse;
import io.weaviate.client.v1.graphql.query.argument.HybridArgument;
import io.weaviate.client.v1.graphql.query.argument.NearVectorArgument;
import io.weaviate.client.v1.graphql.query.argument.WhereArgument;
import io.weaviate.client.v1.graphql.query.fields.Field;
import io.weaviate.client.v1.misc.model.InvertedIndexConfig;
import io.weaviate.client.v1.misc.model.StopwordConfig;
import io.weaviate.client.v1.schema.model.DataType;
import io.weaviate.client.v1.schema.model.Property;
import io.weaviate.client.v1.schema.model.WeaviateClass;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 企业级 Weaviate 向量存储实现
 * <p>
 * 🚀 核心特性：
 * - 使用 Weaviate Java SDK，提供现代化向量搜索体验
 * - 优化的批量操作和连接管理，适合高并发场景
 * - 完善的错误处理和重试机制，保证系统稳定性
 * - 符合 RAG 最佳实践的元数据索引策略
 * - 自动数据一致性保证和性能优化
 * - Spring Boot 生命周期管理，确保资源正确释放
 * <p>
 * 🎯 RAG 优化：
 * - 针对文档片段的快速检索和精确过滤
 * - 支持复杂元数据查询和混合搜索场景
 * - 使用中文停用词优化搜索质量
 * - 内存友好的大规模数据处理
 * <p>
 * 🔧 生命周期管理：
 * - 实现 DisposableBean 接口，确保 Spring 容器关闭时正确释放资源
 * - 使用 @PreDestroy 注解，提供双重保障的资源清理机制
 * - 线程安全的关闭状态管理，避免重复关闭操作
 */
@Slf4j
public class WeaviateEmbeddingStore implements EmbeddingStore<TextSegment>{

    // ================== 混合搜索枚举定义 ==================

    /**
     * 混合搜索重排策略
     */
    public enum HybridRankStrategy {
        /**
         * 加权评分融合 (Weighted Score Fusion)
         * 算法: score = alpha * vector_score + (1-alpha) * bm25_score
         * 优点: 精确控制两种搜索的权重比例，精准度最高
         */
        WEIGHTED
    }

    // Weaviate 客户端实例
    private final WeaviateClient client;

    // 配置参数
    private final String className;
    private final int dimension;
    private final int batchSize;
    private final long connectionTimeout;
    private final int retryAttempts;

    // 混合搜索配置
    private final boolean enableHybridSearch;
    private final float alpha; // 向量搜索权重 (0.0-1.0)
    private final HybridRankStrategy rankStrategy;

    // 连接状态管理
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private final AtomicBoolean isSchemaReady = new AtomicBoolean(false);
    private final AtomicBoolean isClosed = new AtomicBoolean(false);

    // 字段定义 - 基础字段
    private static final String TEXT_FIELD = "text";

    // 关键索引字段 - 针对 RAG 优化
    private static final String NODE_ID_FIELD = "node_id";
    private static final String SPACE_ID_FIELD = "space_id";
    private static final String FILE_NAME_FIELD = "file_name";
    private static final String USER_ID_FIELD = "user_id";
    private static final String MIME_TYPE_FIELD = "mime_type";
    private static final String CREATED_TIME_FIELD = "created_time";
    private static final String UPDATED_TIME_FIELD = "updated_time";
    private static final String CHUNK_INDEX_FIELD = "chunk_index";
    private static final String CHUNK_SIZE_FIELD = "chunk_size";
    private static final String DOCUMENT_ID_FIELD = "document_id";
    private static final String TITLE_FIELD = "title";
    private static final String NODE_PATH_FIELD = "node_path";

    // 性能优化常量
    private static final int DEFAULT_BATCH_SIZE = 100;
    private static final int MAX_BATCH_SIZE = 1000;
    private static final long DEFAULT_CONNECTION_TIMEOUT_MS = 30000L;
    private static final int DEFAULT_RETRY_ATTEMPTS = 3;

    // JSON序列化工具
    private final ObjectMapper objectMapper = new ObjectMapper();

    public WeaviateEmbeddingStore(
            String host,
            int port,
            String className,
            Integer dimension,
            Integer batchSize,
            Long connectionTimeout,
            Integer retryAttempts,
            boolean enableHybridSearch,
            float alpha,
            HybridRankStrategy rankStrategy) {

        this.className = className != null ? className : "Document";
        this.dimension = dimension != null ? dimension : 1024;
        this.batchSize = batchSize != null ? Math.min(batchSize, MAX_BATCH_SIZE) : DEFAULT_BATCH_SIZE;
        this.connectionTimeout = connectionTimeout != null ? connectionTimeout : DEFAULT_CONNECTION_TIMEOUT_MS;
        this.retryAttempts = retryAttempts != null ? Math.max(1, retryAttempts) : DEFAULT_RETRY_ATTEMPTS;
        this.enableHybridSearch = enableHybridSearch;
        this.alpha = Math.max(0.0f, Math.min(1.0f, alpha)); // 确保在0-1范围内
        this.rankStrategy = rankStrategy != null ? rankStrategy : HybridRankStrategy.WEIGHTED;

        // 初始化 Weaviate 客户端
        this.client = createWeaviateClient(host, port);
        this.isConnected.set(true);
//        log.info("✅ Weaviate 客户端连接成功: {}:{}", host, port);

        // 初始化Schema
        initializeSchemaWithRetry();
    }

    /**
     * 创建 Weaviate 客户端 - 企业级连接配置
     */
    private WeaviateClient createWeaviateClient(String host, int port) {
        try {
            String url = String.format("http://%s:%d",
                    host != null ? host : "localhost",
                    port > 0 ? port : 8080);

            Config config = new Config("http", String.format("%s:%d", host, port));
            return new WeaviateClient(config);
        } catch (Exception e) {
            log.error("❌ 创建 Weaviate 客户端失败", e);
            throw new RuntimeException("无法创建 Weaviate 客户端", e);
        }
    }

    /**
     * 带重试的Schema初始化
     */
    private void initializeSchemaWithRetry() {
        for (int attempt = 1; attempt <= retryAttempts; attempt++) {
            try {
                initializeSchema();
                isSchemaReady.set(true);
//                log.info("✅ Weaviate Schema 初始化成功 (尝试 {}/{})", attempt, retryAttempts);
                return;
            } catch (Exception e) {
                log.warn("⚠️ Schema 初始化失败 (尝试 {}/{}): {}", attempt, retryAttempts, e.getMessage());
                if (attempt == retryAttempts) {
                    log.error("❌ Schema 初始化最终失败", e);
                    throw new RuntimeException("Schema 初始化失败", e);
                }
                try {
                    Thread.sleep(1000 * attempt); // 递增延迟
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("初始化被中断", ie);
                }
            }
        }
    }

    /**
     * 初始化Schema
     */
    private void initializeSchema() {
        try {
            // 检查Schema是否已存在
            Result<Boolean> existsResult = client.schema().exists().withClassName(className).run();
            if (existsResult.hasErrors()) {
                log.warn("检查Schema失败: {}", existsResult.getError());
            } else if (existsResult.getResult()) {
                log.info("Schema已存在，跳过创建: {}", className);
                return;
            }

            // 创建优化的Schema
            createOptimizedSchema();
            log.info("✅ Schema创建成功: {}", className);
        } catch (Exception e) {
            log.error("❌ 初始化Schema异常", e);
            throw e;
        }
    }

    /**
     * 创建优化的Schema - 适合消费级电脑使用，从文件加载中文停用词
     */
    private void createOptimizedSchema() {
        try {
            // 从文件加载停用词配置
            log.info("正在从文件加载停用词配置...");
            StopwordConfig stopwordConfig = StopWordsLoader.createStopwordConfig();

            // 显示停用词统计信息
            StopWordsLoader.StopWordsInfo stopWordsInfo = StopWordsLoader.getStopWordsInfo();
            log.info("停用词统计: {}", stopWordsInfo);

            // 配置倒排索引，包含停用词设置
            InvertedIndexConfig invertedIndexConfig = InvertedIndexConfig.builder()
                    .stopwords(stopwordConfig)
                    .indexTimestamps(false) // 不索引时间戳以节省资源
                    .indexNullState(false)  // 不索引空值状态以节省资源
                    .indexPropertyLength(false) // 不索引属性长度以节省资源
                    .build();

            // 创建文档相关属性，优化索引配置
            List<Property> properties = Arrays.asList(
                    Property.builder()
                            .name(NODE_ID_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("节点ID")
                            .indexFilterable(true)  // 支持过滤查询
                            .indexSearchable(false) // 不需要全文搜索，节省资源
                            .build(),
                    Property.builder()
                            .name(SPACE_ID_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("空间ID")
                            .indexFilterable(true)
                            .indexSearchable(false)
                            .build(),
                    Property.builder()
                            .name(FILE_NAME_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("文件名")
                            .tokenization("gse") // 使用GSE中文分词
                            .indexFilterable(true)
                            .indexSearchable(true)  // 文件名需要搜索
                            .build(),
                    Property.builder()
                            .name(USER_ID_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("用户ID")
                            .indexFilterable(true)
                            .indexSearchable(false)
                            .build(),
                    Property.builder()
                            .name(MIME_TYPE_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("文件类型")
                            .indexFilterable(true)
                            .indexSearchable(false)
                            .build(),
                    Property.builder()
                            .name(CREATED_TIME_FIELD)
                            .dataType(Arrays.asList(DataType.DATE))
                            .description("创建时间")
                            .indexFilterable(true)
                            .build(),
                    Property.builder()
                            .name(UPDATED_TIME_FIELD)
                            .dataType(Arrays.asList(DataType.DATE))
                            .description("更新时间")
                            .indexFilterable(true)
                            .build(),
                    Property.builder()
                            .name(CHUNK_INDEX_FIELD)
                            .dataType(Arrays.asList(DataType.INT))
                            .description("分片索引")
                            .indexFilterable(false)
                            .indexSearchable(false)
                            .build(),
                    Property.builder()
                            .name(CHUNK_SIZE_FIELD)
                            .dataType(Arrays.asList(DataType.INT))
                            .description("分片大小")
                            .indexFilterable(false)
                            .indexSearchable(false)
                            .build(),
                    Property.builder()
                            .name(DOCUMENT_ID_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("文档ID")
                            .indexFilterable(true)
                            .indexSearchable(false)
                            .build(),
                    Property.builder()
                            .name(TITLE_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("文档标题")
                            .tokenization("gse") // 使用GSE中文分词
                            .indexSearchable(true)  // 标题需要全文搜索
                            .indexFilterable(true)
                             .build(),
                    Property.builder()
                            .name(NODE_PATH_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("节点路径")
                            .indexFilterable(true)  // 支持路径过滤查询
                            .indexSearchable(false)  // 支持路径搜索
                            .build(),
                    Property.builder()
                            .name(TEXT_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("文本内容")
                            .tokenization("gse") // 使用GSE中文分词
                            .indexSearchable(true)  // 内容需要全文搜索
                            .indexFilterable(false) // 内容不需要精确过滤，节省资源
                            .build()
            );

            // 创建类，配置向量索引优化和停用词
            WeaviateClass weaviateClass = WeaviateClass.builder()
                    .className(className)
                    .description("文档存储类")
                    .properties(properties)
                    .vectorizer("none") // 使用自定义向量
                    .vectorIndexType("hnsw") // 向量索引配置 - 优化内存使用
                    .invertedIndexConfig(invertedIndexConfig) // 配置停用词
                    .build();

            Result<Boolean> result = client.schema().classCreator()
                    .withClass(weaviateClass)
                    .run();

            if (result.hasErrors()) {
                throw new RuntimeException("创建Schema失败: " + result.getError());
            }

            log.info("✅ Schema创建成功，已从文件加载停用词配置和优化设置");
        } catch (Exception e) {
            log.error("❌ 创建Schema异常", e);
            throw new RuntimeException("创建Schema失败", e);
        }
    }

    @Override
    public String add(Embedding embedding) {
        return add(embedding, null);
    }

    @Override
    public void add(String id, Embedding embedding) {
        add(id, embedding, null);
    }

    @Override
    public String add(Embedding embedding, TextSegment textSegment) {
        String id = UUID.randomUUID().toString();
        add(id, embedding, textSegment);
        return id;
    }

    public void add(String id, Embedding embedding, TextSegment textSegment) {
        addInternal(id, embedding, textSegment);
    }

    private void addInternal(String id, Embedding embedding, TextSegment textSegment) {
        ensureSchemaReady();

        try {
            // 生成向量 - 修复类型转换
            Float[] vector = embedding.vectorAsList().stream()
                    .map(d -> d.floatValue())
                    .toArray(Float[]::new);

            // 创建文档对象
            Map<String, Object> properties = new HashMap<>();

            if (textSegment != null) {
                properties.put(TEXT_FIELD, textSegment.text());

                // 处理元数据
                if (textSegment.metadata() != null) {
                    Map<String, Object> metadata = textSegment.metadata().toMap();

                    // 提取关键字段
                    properties.put(NODE_ID_FIELD, getMetadataValue(metadata, NODE_ID_FIELD, ""));
                    properties.put(SPACE_ID_FIELD, getMetadataValue(metadata, SPACE_ID_FIELD, ""));
                    properties.put(FILE_NAME_FIELD, getMetadataValue(metadata, FILE_NAME_FIELD, ""));
                    properties.put(USER_ID_FIELD, getMetadataValue(metadata, USER_ID_FIELD, ""));
                    properties.put(MIME_TYPE_FIELD, getMetadataValue(metadata, MIME_TYPE_FIELD, ""));
                    properties.put(TITLE_FIELD, getMetadataValue(metadata, TITLE_FIELD, ""));
                    properties.put(NODE_PATH_FIELD, getMetadataValue(metadata, NODE_PATH_FIELD, ""));

                    // 处理创建时间 - 确保RFC3339格式
                    Object createdTime = metadata.get(CREATED_TIME_FIELD);
                    if (createdTime != null) {
                        properties.put(CREATED_TIME_FIELD, createdTime.toString());
                    } else {
                        properties.put(CREATED_TIME_FIELD, Instant.now().toString());
                    }
                    
                    // 处理更新时间 - 确保RFC3339格式
                    Object updatedTime = metadata.get(UPDATED_TIME_FIELD);
                    if (updatedTime != null) {
                        properties.put(UPDATED_TIME_FIELD, updatedTime.toString());
                    }
                    
                    // 处理数值字段
                    Object chunkIndex = metadata.get(CHUNK_INDEX_FIELD);
                    if (chunkIndex != null) {
                        properties.put(CHUNK_INDEX_FIELD, Integer.parseInt(chunkIndex.toString()));
                    }
                    
                    Object chunkSize = metadata.get(CHUNK_SIZE_FIELD);
                    if (chunkSize != null) {
                        properties.put(CHUNK_SIZE_FIELD, Long.parseLong(chunkSize.toString()));
                    }
                    
                    // 处理文档ID
                    Object documentId = metadata.get(DOCUMENT_ID_FIELD);
                    if (documentId != null) {
                        properties.put(DOCUMENT_ID_FIELD, documentId.toString());
                    }

                }
            } else {
                // 设置默认值
                setDefaultFields(properties);
            }

            // 创建对象
            Result<WeaviateObject> result = client.data().creator()
                    .withClassName(className)
                    .withID(id)
                    .withProperties(properties)
                    .withVector(vector)
                    .run();

            if (result.hasErrors()) {
                throw new RuntimeException("添加文档失败: " + result.getError());
            }

            log.debug("✅ 文档添加成功，ID: {}", id);
        } catch (Exception e) {
            log.error("❌ 添加文档异常: {}", e.getMessage());
            throw new RuntimeException("添加文档失败", e);
        }
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings) {
        return addAll(embeddings, null);
    }

    @Override
    public List<String> addAll(List<Embedding> embeddings, List<TextSegment> textSegments) {
        List<String> ids = generateRandomIds(embeddings.size());
        addAll(ids, embeddings, textSegments);
        return ids;
    }

    public void addAll(List<String> ids, List<Embedding> embeddings, List<TextSegment> textSegments) {
        if (ids.size() != embeddings.size()) {
            throw new IllegalArgumentException("IDs和embeddings数量不匹配");
        }
        if (textSegments != null && textSegments.size() != embeddings.size()) {
            throw new IllegalArgumentException("TextSegments和embeddings数量不匹配");
        }

        ensureSchemaReady();

        // 分批处理
        List<List<String>> idBatches = partitionList(ids, batchSize);
        List<List<Embedding>> embeddingBatches = partitionList(embeddings, batchSize);
        List<List<TextSegment>> segmentBatches = textSegments != null ?
                partitionList(textSegments, batchSize) : null;

        for (int i = 0; i < idBatches.size(); i++) {
            List<TextSegment> currentSegments = segmentBatches != null ? segmentBatches.get(i) : null;
            insertBatchWithRetry(idBatches.get(i), embeddingBatches.get(i), currentSegments, i + 1, idBatches.size());
        }

        log.info("✅ 批量添加完成: {} 个文档", ids.size());
    }

    private void insertBatchWithRetry(List<String> ids, List<Embedding> embeddings,
                                      List<TextSegment> textSegments, int batchNum, int totalBatches) {
        for (int attempt = 1; attempt <= retryAttempts; attempt++) {
            try {
                insertBatch(ids, embeddings, textSegments);
                log.debug("✅ 批次 {}/{} 插入成功 (尝试 {}/{})", batchNum, totalBatches, attempt, retryAttempts);
                return;
            } catch (Exception e) {
                log.warn("⚠️ 批次 {}/{} 插入失败 (尝试 {}/{}): {}",
                        batchNum, totalBatches, attempt, retryAttempts, e.getMessage());
                if (attempt == retryAttempts) {
                    log.error("❌ 批次 {}/{} 最终插入失败", batchNum, totalBatches, e);
                    throw new RuntimeException("批量插入失败", e);
                }
                try {
                    Thread.sleep(1000 * attempt);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("插入被中断", ie);
                }
            }
        }
    }

    private void insertBatch(List<String> ids, List<Embedding> embeddings, List<TextSegment> textSegments) {
        // 由于Weaviate Java SDK的限制，这里使用单个插入的方式
        // 在实际生产环境中，可以考虑使用批量API或异步处理
        for (int i = 0; i < ids.size(); i++) {
            TextSegment segment = textSegments != null && i < textSegments.size() ? textSegments.get(i) : null;
            addInternal(ids.get(i), embeddings.get(i), segment);
        }
    }

    @Override
    public EmbeddingSearchResult<TextSegment> search(EmbeddingSearchRequest request) {
        ensureSchemaReady();

        if (enableHybridSearch) {
            return performHybridSearch(request);
        } else {
            return performVectorSearch(request);
        }
    }

    /**
     * 混合检索方法 - 结合向量搜索和BM25关键词搜索
     */
    public EmbeddingSearchResult<TextSegment> hybridSearch(String queryText, Embedding queryEmbedding,
                                                           int maxResults, double minScore, Filter filter) {
        ensureSchemaReady();

        try {
            log.debug("执行混合检索: queryText={}, maxResults={}, minScore={}", queryText, maxResults, minScore);

            // 生成查询向量
            Float[] queryVector = queryEmbedding.vectorAsList().stream()
                    .map(d -> d.floatValue())
                    .toArray(Float[]::new);

            // 构建混合检索参数
            HybridArgument hybrid = HybridArgument.builder()
                    .query(queryText)
                    .vector(queryVector) // 向量搜索
                    .alpha(alpha) // 权重控制
                    .build();

            // 构建过滤条件
            WhereArgument whereArg = null;
            if (filter != null) {
                whereArg = convertFilterToWhereArgument(filter);
                if (whereArg != null) {
                    log.debug("应用过滤条件: {}", filter);
                }
            }

            // 构建返回字段
            Field[] fields = {
                    Field.builder().name(NODE_ID_FIELD).build(),
                    Field.builder().name(SPACE_ID_FIELD).build(),
                    Field.builder().name(FILE_NAME_FIELD).build(),
                    Field.builder().name(USER_ID_FIELD).build(),
                    Field.builder().name(MIME_TYPE_FIELD).build(),
                    Field.builder().name(CREATED_TIME_FIELD).build(),
                    Field.builder().name(UPDATED_TIME_FIELD).build(),
                    Field.builder().name(CHUNK_INDEX_FIELD).build(),
                    Field.builder().name(CHUNK_SIZE_FIELD).build(),
                    Field.builder().name(DOCUMENT_ID_FIELD).build(),
                    Field.builder().name(TITLE_FIELD).build(),
                    Field.builder().name(TEXT_FIELD).build(),
                    Field.builder()
                            .name("_additional")
                            .fields(new Field[]{
                                    Field.builder().name("id").build(),
                                    Field.builder().name("score").build()
                            })
                            .build()
            };

            // 执行混合检索 - 修复API调用
            var queryBuilder = client.graphQL().get()
                    .withClassName(className)
                    .withHybrid(hybrid)
                    .withLimit(maxResults)
                    .withFields(fields);

            if (whereArg != null) {
                queryBuilder = queryBuilder.withWhere(whereArg);
            }

            Result<GraphQLResponse> searchResult = queryBuilder.run();

            if (searchResult.hasErrors()) {
                log.error("混合检索失败: {}", searchResult.getError());
                return new EmbeddingSearchResult<>(Collections.emptyList());
            }

            List<EmbeddingMatch<TextSegment>> matches = parseGraphQLResponse(searchResult.getResult(), minScore);
            log.debug("混合检索完成，找到 {} 个结果", matches.size());

            return new EmbeddingSearchResult<>(matches);
        } catch (Exception e) {
            log.error("❌ 混合检索异常", e);
            return new EmbeddingSearchResult<>(Collections.emptyList());
        }
    }

    private EmbeddingSearchResult<TextSegment> performHybridSearch(EmbeddingSearchRequest request) {
        // 如果没有查询文本，回退到向量搜索
        return performVectorSearch(request);
    }

    private EmbeddingSearchResult<TextSegment> performVectorSearch(EmbeddingSearchRequest request) {
        try {
            // 生成查询向量
            Float[] queryVector = request.queryEmbedding().vectorAsList().stream()
                    .map(d -> d.floatValue())
                    .toArray(Float[]::new);

            // 构建向量搜索参数
            NearVectorArgument nearVector = NearVectorArgument.builder()
                    .vector(queryVector)
                    .build();

            // 构建过滤条件
            WhereArgument whereArg = null;
            if (request.filter() != null) {
                whereArg = convertFilterToWhereArgument(request.filter());
                if (whereArg != null) {
                    log.debug("向量搜索应用过滤条件: {}", request.filter());
                }
            }

            // 构建返回字段
            Field[] fields = {
                    Field.builder().name(NODE_ID_FIELD).build(),
                    Field.builder().name(SPACE_ID_FIELD).build(),
                    Field.builder().name(FILE_NAME_FIELD).build(),
                    Field.builder().name(USER_ID_FIELD).build(),
                    Field.builder().name(MIME_TYPE_FIELD).build(),
                    Field.builder().name(CREATED_TIME_FIELD).build(),
                    Field.builder().name(UPDATED_TIME_FIELD).build(),
                    Field.builder().name(CHUNK_INDEX_FIELD).build(),
                    Field.builder().name(CHUNK_SIZE_FIELD).build(),
                    Field.builder().name(DOCUMENT_ID_FIELD).build(),
                    Field.builder().name(TITLE_FIELD).build(),
                    Field.builder().name(TEXT_FIELD).build(),
                    Field.builder()
                            .name("_additional")
                            .fields(new Field[]{
                                    Field.builder().name("id").build(),
                                    Field.builder().name("score").build()
                            })
                            .build()
            };

            // 执行向量搜索
            var queryBuilder = client.graphQL().get()
                    .withClassName(className)
                    .withNearVector(nearVector)
                    .withLimit(request.maxResults())
                    .withFields(fields);

            if (whereArg != null) {
                queryBuilder = queryBuilder.withWhere(whereArg);
            }

            Result<GraphQLResponse> result = queryBuilder.run();

            if (result.hasErrors()) {
                log.error("向量搜索失败: {}", result.getError());
                return new EmbeddingSearchResult<>(Collections.emptyList());
            }

            List<EmbeddingMatch<TextSegment>> matches = parseGraphQLResponse(result.getResult(), request.minScore());
            log.debug("向量搜索完成，找到 {} 个结果", matches.size());

            return new EmbeddingSearchResult<>(matches);
        } catch (Exception e) {
            log.error("❌ 向量搜索异常", e);
            return new EmbeddingSearchResult<>(Collections.emptyList());
        }
    }

    private List<EmbeddingMatch<TextSegment>> parseGraphQLResponse(GraphQLResponse response, double minScore) {
        List<EmbeddingMatch<TextSegment>> results = new ArrayList<>();

        if (response != null && response.getData() != null) {
            Map<String, Object> data = (Map<String, Object>) response.getData();
            Map<String, Object> getClass = (Map<String, Object>) data.get("Get");

            if (getClass != null) {
                List<Map<String, Object>> documents = (List<Map<String, Object>>) getClass.get(className);

                if (documents != null) {
                    for (Map<String, Object> doc : documents) {
                        try {
                            EmbeddingMatch<TextSegment> match = createEmbeddingMatch(doc, minScore);
                            if (match != null) {
                                results.add(match);
                            }
                        } catch (Exception e) {
                            log.warn("解析搜索结果失败: {}", e.getMessage());
                        }
                    }
                }
            }
        }

        return results;
    }

    private EmbeddingMatch<TextSegment> createEmbeddingMatch(Map<String, Object> doc, double minScore) {
        try {
            Map<String, Object> additional = (Map<String, Object>) doc.get("_additional");
            if (additional == null) {
                return null;
            }

            String id = (String) additional.get("id");
            Object scoreObj = additional.get("score");
            double score = Double.parseDouble(scoreObj.toString());

            // 应用最小分数过滤
            if (score < minScore) {
                return null;
            }

            // 构建TextSegment
            String text = (String) doc.get(TEXT_FIELD);
            if (text == null) {
                text = "";
            }

            // 构建元数据
            Map<String, Object> metadata = new HashMap<>();

            // 添加所有字段到元数据
            addFieldToMetadata(metadata, doc, NODE_ID_FIELD);
            addFieldToMetadata(metadata, doc, SPACE_ID_FIELD);
            addFieldToMetadata(metadata, doc, FILE_NAME_FIELD);
            addFieldToMetadata(metadata, doc, USER_ID_FIELD);
            addFieldToMetadata(metadata, doc, MIME_TYPE_FIELD);
            addFieldToMetadata(metadata, doc, CREATED_TIME_FIELD);
            addFieldToMetadata(metadata, doc, UPDATED_TIME_FIELD);
            addFieldToMetadata(metadata, doc, CHUNK_INDEX_FIELD);
            addFieldToMetadata(metadata, doc, CHUNK_SIZE_FIELD);
            addFieldToMetadata(metadata, doc, DOCUMENT_ID_FIELD);
            addFieldToMetadata(metadata, doc, TITLE_FIELD);
            addFieldToMetadata(metadata, doc, NODE_PATH_FIELD);

            TextSegment textSegment = TextSegment.from(text, dev.langchain4j.data.document.Metadata.from(metadata));
            return new EmbeddingMatch<>(score, id, null, textSegment);
        } catch (Exception e) {
            log.warn("创建EmbeddingMatch失败: {}", e.getMessage());
            return null;
        }
    }

    private void addFieldToMetadata(Map<String, Object> metadata, Map<String, Object> doc, String fieldName) {
        Object value = doc.get(fieldName);
        if (value != null) {
            metadata.put(fieldName, value);
        }
    }

    @Override
    public void remove(String id) {
        removeAll(Collections.singletonList(id));
    }

    @Override
    public void removeAll(Collection<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        ensureSchemaReady();

        try {
            for (String id : ids) {
                Result<Boolean> result = client.data().deleter()
                        .withClassName(className)
                        .withID(id)
                        .run();

                if (result.hasErrors()) {
                    log.warn("删除文档失败 ID={}: {}", id, result.getError());
                } else {
                    log.debug("✅ 文档删除成功 ID={}", id);
                }
            }
        } catch (Exception e) {
            log.error("❌ 批量删除异常", e);
            throw new RuntimeException("批量删除失败", e);
        }
    }

    @Override
    public void removeAll() {
        ensureSchemaReady();

        try {
            // 删除整个类（包含所有数据）
            Result<Boolean> result = client.schema().classDeleter()
                    .withClassName(className)
                    .run();

            if (result.hasErrors()) {
                throw new RuntimeException("清空数据失败: " + result.getError());
            }

            // 重新创建Schema
            initializeSchema();
            log.info("✅ 所有数据已清空并重新创建Schema");
        } catch (Exception e) {
            log.error("❌ 清空数据异常", e);
            throw new RuntimeException("清空数据失败", e);
        }
    }

    @Override
    public void removeAll(Filter filter) {
        // Weaviate的删除操作相对复杂，这里提供简化实现
        // 实际使用时可能需要先查询再删除
        log.warn("基于过滤条件的删除操作暂未完全实现");
        throw new UnsupportedOperationException("基于过滤条件的删除操作暂未实现");
    }

    private void ensureSchemaReady() {
        if (isClosed.get()) {
            throw new IllegalStateException("WeaviateEmbeddingStore已关闭");
        }
        if (!isSchemaReady.get()) {
            throw new IllegalStateException("Schema未就绪");
        }
    }

    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

    private List<String> generateRandomIds(int size) {
        return Collections.nCopies(size, null).stream()
                .map(ignored -> UUID.randomUUID().toString())
                .collect(Collectors.toList());
    }

    private String getMetadataValue(Map<String, Object> metadata, String key, String defaultValue) {
        Object value = metadata.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    private void setDefaultFields(Map<String, Object> properties) {
        properties.put(NODE_ID_FIELD, "");
        properties.put(SPACE_ID_FIELD, "");
        properties.put(FILE_NAME_FIELD, "");
        properties.put(USER_ID_FIELD, "");
        properties.put(MIME_TYPE_FIELD, "");
        properties.put(CREATED_TIME_FIELD, Instant.now().toString());
        properties.put(UPDATED_TIME_FIELD, Instant.now().toString());
        properties.put(CHUNK_INDEX_FIELD, 0);
        properties.put(CHUNK_SIZE_FIELD, 0);
        properties.put(DOCUMENT_ID_FIELD, "");
        properties.put(TITLE_FIELD, "");
        // METADATA_FIELD 已移除，所有字段现在都作为独立属性
    }

    /**
     * 将langchain4j的Filter转换为Weaviate的WhereArgument
     */
    private WhereArgument convertFilterToWhereArgument(Filter filter) {
        if (filter == null) {
            return null;
        }

        try {
            WhereFilter whereFilter = convertFilterToWhereFilter(filter);
            return WhereArgument.builder()
                    .filter(whereFilter)
                    .build();
        } catch (Exception e) {
            log.warn("转换过滤条件失败: {}", e.getMessage());
        }
        
        return null;
    }

    private WhereFilter convertFilterToWhereFilter(Filter filter) {
        if (filter instanceof IsEqualTo equalTo) {
            return buildWhereFilter(equalTo.key(), "Equal", equalTo.comparisonValue());
        } else if (filter instanceof IsNotEqualTo notEqualTo) {
            return buildWhereFilter(notEqualTo.key(), "NotEqual", notEqualTo.comparisonValue());
        } else if (filter instanceof IsGreaterThan greaterThan) {
            return buildWhereFilter(greaterThan.key(), "GreaterThan", greaterThan.comparisonValue());
        } else if (filter instanceof IsGreaterThanOrEqualTo greaterThanOrEqual) {
            return buildWhereFilter(greaterThanOrEqual.key(), "GreaterThanEqual", greaterThanOrEqual.comparisonValue());
        } else if (filter instanceof IsLessThan lessThan) {
            return buildWhereFilter(lessThan.key(), "LessThan", lessThan.comparisonValue());
        } else if (filter instanceof IsLessThanOrEqualTo lessThanOrEqual) {
            return buildWhereFilter(lessThanOrEqual.key(), "LessThanEqual", lessThanOrEqual.comparisonValue());
        } else if (filter instanceof IsIn isIn) {
            // Weaviate不直接支持IN操作，转换为OR条件
            List<WhereFilter> orFilters = new ArrayList<>();
            for (Object value : isIn.comparisonValues()) {
                orFilters.add(buildWhereFilter(isIn.key(), "Equal", value));
            }
            
            if (orFilters.size() == 1) {
                return orFilters.get(0);
            } else {
                return WhereFilter.builder()
                        .operands(orFilters.toArray(new WhereFilter[0]))
                        .operator("Or")
                        .build();
            }
        } else if (filter instanceof IsNotIn isNotIn) {
            // Weaviate不直接支持NOT_IN操作，转换为AND条件
            List<WhereFilter> andFilters = new ArrayList<>();
            for (Object value : isNotIn.comparisonValues()) {
                andFilters.add(buildWhereFilter(isNotIn.key(), "NotEqual", value));
            }
            
            if (andFilters.size() == 1) {
                return andFilters.get(0);
            } else {
                return WhereFilter.builder()
                        .operands(andFilters.toArray(new WhereFilter[0]))
                        .operator("And")
                        .build();
            }
        } else if (filter instanceof ContainsString containsString) {
            // 使用Weaviate的Like操作符实现字符串包含匹配
            String likePattern = "*" + containsString.comparisonValue() + "*";
            return buildWhereFilter(containsString.key(), "Like", likePattern);
        } else if (filter instanceof And andFilter) {
            List<WhereFilter> whereFilters = new ArrayList<>();
            Filter left = andFilter.left();
            Filter right = andFilter.right();
            whereFilters.add(convertFilterToWhereFilter(left));
            whereFilters.add(convertFilterToWhereFilter(right));
            return WhereFilter.builder()
                    .operands(whereFilters.toArray(new WhereFilter[0]))
                    .operator("And")
                    .build();
        } else if (filter instanceof Or orFilter) {
            List<WhereFilter> whereFilters = new ArrayList<>();
            Filter left = orFilter.left();
            Filter right = orFilter.right();
            whereFilters.add(convertFilterToWhereFilter(left));
            whereFilters.add(convertFilterToWhereFilter(right));
            return WhereFilter.builder()
                    .operands(whereFilters.toArray(new WhereFilter[0]))
                    .operator("Or")
                    .build();
        } else {
            throw new IllegalArgumentException("Unsupported filter type: " + filter.getClass());
        }
    }

    /**
     * 构建WhereFilter，根据值类型选择合适的value方法
     */
    private WhereFilter buildWhereFilter(String path, String operator, Object value) {
        WhereFilter.WhereFilterBuilder builder = WhereFilter.builder()
                .path(new String[]{path})
                .operator(operator);

        // 根据值类型设置正确的value方法
        if (value == null) {
            builder.valueText("null");
        } else if (value instanceof String) {
            builder.valueText((String) value);
        } else if (value instanceof Integer) {
            builder.valueInt((Integer) value);
        } else if (value instanceof Long) {
            builder.valueInt(((Long) value).intValue());
        } else if (value instanceof Double) {
            builder.valueNumber((Double) value);
        } else if (value instanceof Float) {
            builder.valueNumber(((Float) value).doubleValue());
        } else if (value instanceof Boolean) {
            builder.valueBoolean((Boolean) value);
        } else if (value instanceof Date) {
            builder.valueDate((Date) value);
        } else {
            // 对于其他类型，转换为字符串
            builder.valueText(value.toString());
        }

        return builder.build();
    }

    /**
     * 检查连接健康状态
     */
    public boolean isHealthy() {
        try {
            if (isClosed.get()) {
                return false;
            }

            Result<Boolean> result = client.misc().readyChecker().run();
            return !result.hasErrors() && Boolean.TRUE.equals(result.getResult());
        } catch (Exception e) {
            log.debug("健康检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取统计信息
     */
    public Map<String, Object> getStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("className", className);
        stats.put("dimension", dimension);
        stats.put("batchSize", batchSize);
        stats.put("isConnected", isConnected.get());
        stats.put("isSchemaReady", isSchemaReady.get());
        stats.put("isClosed", isClosed.get());
        stats.put("enableHybridSearch", enableHybridSearch);
        stats.put("alpha", alpha);
        stats.put("rankStrategy", rankStrategy.name());
        return stats;
    }

    @PreDestroy
    public void preDestroy() {
        close();
    }

    public void close() {
        if (isClosed.compareAndSet(false, true)) {
            try {
                isConnected.set(false);
                isSchemaReady.set(false);
                log.info("✅ WeaviateEmbeddingStore 已关闭");
            } catch (Exception e) {
                log.error("❌ 关闭 WeaviateEmbeddingStore 时发生异常", e);
            }
        }
    }

    public boolean isClosed() {
        return isClosed.get();
    }

    // Builder模式
    public static WeaviateEmbeddingStoreBuilder builder() {
        return new WeaviateEmbeddingStoreBuilder();
    }

    public static class WeaviateEmbeddingStoreBuilder {
        private String host = "localhost";
        private int port = 8080;
        private String className = "Document";
        private Integer dimension = 1024;
        private Integer batchSize = DEFAULT_BATCH_SIZE;
        private Long connectionTimeout = DEFAULT_CONNECTION_TIMEOUT_MS;
        private Integer retryAttempts = DEFAULT_RETRY_ATTEMPTS;
        private boolean enableHybridSearch = false;
        private float alpha = 0.7f;
        private HybridRankStrategy rankStrategy = HybridRankStrategy.WEIGHTED;

        public WeaviateEmbeddingStoreBuilder host(String host) {
            this.host = host;
            return this;
        }

        public WeaviateEmbeddingStoreBuilder port(int port) {
            this.port = port;
            return this;
        }

        public WeaviateEmbeddingStoreBuilder className(String className) {
            this.className = className;
            return this;
        }

        public WeaviateEmbeddingStoreBuilder dimension(int dimension) {
            this.dimension = dimension;
            return this;
        }

        public WeaviateEmbeddingStoreBuilder batchSize(int batchSize) {
            this.batchSize = batchSize;
            return this;
        }

        public WeaviateEmbeddingStoreBuilder connectionTimeout(long connectionTimeout) {
            this.connectionTimeout = connectionTimeout;
            return this;
        }

        public WeaviateEmbeddingStoreBuilder retryAttempts(int retryAttempts) {
            this.retryAttempts = retryAttempts;
            return this;
        }

        public WeaviateEmbeddingStoreBuilder enableHybridSearch(boolean enableHybridSearch) {
            this.enableHybridSearch = enableHybridSearch;
            return this;
        }

        public WeaviateEmbeddingStoreBuilder alpha(float alpha) {
            this.alpha = alpha;
            return this;
        }

        public WeaviateEmbeddingStoreBuilder rankStrategy(HybridRankStrategy rankStrategy) {
            this.rankStrategy = rankStrategy;
            return this;
        }

        public WeaviateEmbeddingStore build() {
            return new WeaviateEmbeddingStore(
                    host, port, className, dimension, batchSize, connectionTimeout,
                    retryAttempts, enableHybridSearch, alpha, rankStrategy
            );
        }
    }
}