package com.zibbava.edgemind.cortex.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.time.LocalDateTime;

@Slf4j
@Configuration
@EnableTransactionManagement // 建议显式开启事务管理
public class MybatisPlusConfig {

    /**
     * 配置 Mybatis Plus 的自动填充处理器
     */
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MetaObjectHandler() {
            @Override
            public void insertFill(MetaObject metaObject) {
                log.info("start insert fill ....");
                // 插入时自动填充创建时间和更新时间
                LocalDateTime now = LocalDateTime.now();
                this.strictInsertFill(metaObject, "createTime", () -> now, LocalDateTime.class);
                this.strictInsertFill(metaObject, "updateTime", () -> now, LocalDateTime.class);
                // 如果还有其他字段如 createBy, updateBy 也可在此设置
                // this.strictInsertFill(metaObject, "createBy", () -> getCurrentUserId(), Long.class);
                // this.strictInsertFill(metaObject, "updateBy", () -> getCurrentUserId(), Long.class);
            }

            @Override
            public void updateFill(MetaObject metaObject) {
                log.info("start update fill ....");
                // 更新时自动填充更新时间
                this.strictUpdateFill(metaObject, "updateTime", LocalDateTime::now, LocalDateTime.class);
                // 如果还有其他字段如 updateBy 也可在此设置
                // this.strictUpdateFill(metaObject, "updateBy", () -> getCurrentUserId(), Long.class);
            }
        };
    }

    // 如果需要分页插件，可以取消注释下面的 Bean
    /*
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL)); // 添加MySQL分页插件
        return interceptor;
    }
    */
}