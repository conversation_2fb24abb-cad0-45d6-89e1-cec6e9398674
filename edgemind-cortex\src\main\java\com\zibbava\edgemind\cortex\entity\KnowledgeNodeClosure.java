package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 知识库节点闭包表实体类
 * 用于高效查询树形结构
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("kb_knowledge_node_closure")
public class KnowledgeNodeClosure {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 祖先节点ID
     */
    @TableField("ancestor_id")
    private String ancestorId;

    /**
     * 后代节点ID
     */
    @TableField("descendant_id")
    private String descendantId;

    /**
     * 层级深度 (0表示自身)
     */
    @TableField("depth")
    private Integer depth;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
