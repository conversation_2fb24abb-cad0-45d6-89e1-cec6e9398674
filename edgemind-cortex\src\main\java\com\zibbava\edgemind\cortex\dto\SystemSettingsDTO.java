package com.zibbava.edgemind.cortex.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 系统设置数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SystemSettingsDTO {

    /**
     * 知识库存储路径
     */
    @NotBlank(message = "知识库存储路径不能为空")
    private String storagePath;

    /**
     * 个人库存储路径
     */
    private String personalStoragePath;
}
