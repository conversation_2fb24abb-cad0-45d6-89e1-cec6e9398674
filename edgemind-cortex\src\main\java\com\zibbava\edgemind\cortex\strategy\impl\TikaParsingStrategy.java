package com.zibbava.edgemind.cortex.strategy.impl;

import com.zibbava.edgemind.cortex.strategy.DocumentParsingStrategy;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.DocumentParser;
import dev.langchain4j.data.document.parser.apache.tika.ApacheTikaDocumentParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;

/**
 * Tika文档解析策略实现
 * 用于处理除PDF外的其他文档类型
 */
@Component
@Slf4j
public class TikaParsingStrategy implements DocumentParsingStrategy {
    
    private volatile DocumentParser tikaParser = null;
    
    @Override
    public Document parseDocument(byte[] fileContent, String fileName) throws Exception {
        log.info("使用Tika解析策略处理文档: {}", fileName);
        DocumentParser parser = getTikaParser();
        return parser.parse(new ByteArrayInputStream(fileContent));
    }
    
    @Override
    public boolean supports(String fileName) {
        if (fileName == null) {
            return false;
        }
        String lowerFileName = fileName.toLowerCase();
        // 支持除PDF外的其他文档类型
        return !lowerFileName.endsWith(".pdf");
    }
    
    /**
     * 获取Tika解析器（懒加载）
     */
    private DocumentParser getTikaParser() {
        if (tikaParser == null) {
            synchronized (this) {
                if (tikaParser == null) {
                    log.info("Initializing ApacheTikaDocumentParser instance...");
                    tikaParser = new ApacheTikaDocumentParser();
                }
            }
        }
        return tikaParser;
    }
}