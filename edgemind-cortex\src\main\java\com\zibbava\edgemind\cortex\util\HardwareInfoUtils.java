package com.zibbava.edgemind.cortex.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.ComputerSystem;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.hardware.NetworkIF;
import oshi.software.os.OperatingSystem;

import java.util.ArrayList;
import java.util.List;

/**
 * 硬件信息工具类
 * 用于获取系统硬件信息并生成唯一的硬件指纹
 * 
 * 版本：v2.0 - 增强稳定性和容错能力
 */
@Slf4j
public class HardwareInfoUtils {

    // 缓存SystemInfo实例，避免重复初始化
    private static volatile SystemInfo cachedSystemInfo;
    private static final Object SYSTEM_INFO_LOCK = new Object();

    /**
     * 获取SystemInfo实例（带缓存）
     */
    private static SystemInfo getSystemInfo() {
        if (cachedSystemInfo == null) {
            synchronized (SYSTEM_INFO_LOCK) {
                if (cachedSystemInfo == null) {
                    try {
                        log.debug("初始化SystemInfo实例");
                        cachedSystemInfo = new SystemInfo();
                    } catch (Exception e) {
                        log.error("初始化SystemInfo失败", e);
                        throw new RuntimeException("无法初始化系统信息", e);
                    }
                }
            }
        }
        return cachedSystemInfo;
    }

    /**
     * 获取硬件指纹
     * 基于最稳定的硬件信息生成唯一标识
     * 
     * 稳定性改进：
     * 1. 使用容错机制，单个组件失败不影响整体
     * 2. 只使用最稳定的硬件特征
     * 3. 规范化处理，去除可能变化的空白字符
     * 4. 添加详细日志用于故障诊断
     *
     * @return 硬件指纹字符串
     */
    public static String getHardwareFingerprint() {
        try {
            SystemInfo systemInfo = getSystemInfo();
            HardwareAbstractionLayer hardware = systemInfo.getHardware();
            
            List<String> stableHardwareInfo = new ArrayList<>();
            
            // 1. CPU信息（最稳定的标识）
            try {
                CentralProcessor processor = hardware.getProcessor();
                String processorId = normalizeString(processor.getProcessorIdentifier().getProcessorID());
                String processorName = normalizeString(processor.getProcessorIdentifier().getName());
                
                if (!processorId.isEmpty()) {
                    stableHardwareInfo.add("CPU_ID:" + processorId);
                    log.debug("✓ CPU ID: {}", processorId);
                } else {
                    log.warn("⚠ CPU ID为空，跳过");
                }
                
                if (!processorName.isEmpty()) {
                    stableHardwareInfo.add("CPU_NAME:" + processorName);
                    log.debug("✓ CPU名称: {}", processorName);
                } else {
                    log.warn("⚠ CPU名称为空，跳过");
                }
                
                // 添加核心数作为辅助标识
                int physicalCores = processor.getPhysicalProcessorCount();
                int logicalCores = processor.getLogicalProcessorCount();
                stableHardwareInfo.add("CPU_CORES:" + physicalCores + "P" + logicalCores + "L");
                log.debug("✓ CPU核心数: {}物理/{}逻辑", physicalCores, logicalCores);
                
            } catch (Exception e) {
                log.error("❌ 获取CPU信息失败", e);
                stableHardwareInfo.add("CPU_ERROR:" + e.getClass().getSimpleName());
            }
            
            // 2. 主板信息（非常稳定）
            try {
                ComputerSystem computerSystem = hardware.getComputerSystem();
                String manufacturer = normalizeString(computerSystem.getManufacturer());
                String model = normalizeString(computerSystem.getModel());
                String serialNumber = normalizeString(computerSystem.getSerialNumber());
                
                if (!manufacturer.isEmpty() && !manufacturer.equals("Unknown")) {
                    stableHardwareInfo.add("MB_MANUFACTURER:" + manufacturer);
                    log.debug("✓ 主板制造商: {}", manufacturer);
                }
                
                if (!model.isEmpty() && !model.equals("Unknown")) {
                    stableHardwareInfo.add("MB_MODEL:" + model);
                    log.debug("✓ 主板型号: {}", model);
                }
                
                if (!serialNumber.isEmpty() && !serialNumber.equals("Unknown") && !serialNumber.equals("To be filled by O.E.M.")) {
                    stableHardwareInfo.add("MB_SERIAL:" + serialNumber);
                    log.debug("✓ 主板序列号: {}", serialNumber);
                } else {
                    log.debug("⚠ 主板序列号无效或为默认值，跳过");
                }
                
            } catch (Exception e) {
                log.error("❌ 获取主板信息失败", e);
                stableHardwareInfo.add("MB_ERROR:" + e.getClass().getSimpleName());
            }
            
            // 3. BIOS信息（相对稳定）
            try {
                ComputerSystem computerSystem = hardware.getComputerSystem();
                String biosVendor = normalizeString(computerSystem.getFirmware().getManufacturer());
                String biosVersion = normalizeString(computerSystem.getFirmware().getVersion());
                
                if (!biosVendor.isEmpty() && !biosVendor.equals("Unknown")) {
                    stableHardwareInfo.add("BIOS_VENDOR:" + biosVendor);
                    log.debug("✓ BIOS制造商: {}", biosVendor);
                }
                
                if (!biosVersion.isEmpty() && !biosVersion.equals("Unknown")) {
                    stableHardwareInfo.add("BIOS_VERSION:" + biosVersion);
                    log.debug("✓ BIOS版本: {}", biosVersion);
                }
                
            } catch (Exception e) {
                log.error("❌ 获取BIOS信息失败", e);
                stableHardwareInfo.add("BIOS_ERROR:" + e.getClass().getSimpleName());
            }
            
            // 4. 如果硬件信息太少，添加操作系统信息作为补充
            if (stableHardwareInfo.size() < 3) {
                log.warn("⚠ 硬件信息较少({})，添加操作系统信息作为补充", stableHardwareInfo.size());
                try {
                    String osName = normalizeString(System.getProperty("os.name"));
                    String osArch = normalizeString(System.getProperty("os.arch"));
                    if (!osName.isEmpty()) {
                        stableHardwareInfo.add("OS_NAME:" + osName);
                    }
                    if (!osArch.isEmpty()) {
                        stableHardwareInfo.add("OS_ARCH:" + osArch);
                    }
                } catch (Exception e) {
                    log.error("❌ 获取操作系统信息失败", e);
                }
            }
            
            // 5. 验证是否有足够的硬件信息
            if (stableHardwareInfo.isEmpty()) {
                log.error("❌ 无法获取任何硬件信息");
                return "NO-HARDWARE-INFO-AVAILABLE";
            }
            
            // 6. 将所有信息排序后拼接（确保一致性）
            stableHardwareInfo.sort(String::compareTo);
            String combinedInfo = String.join("|", stableHardwareInfo);
            
            log.debug("🔧 硬件指纹组件数量: {}", stableHardwareInfo.size());
            log.debug("🔧 稳定硬件信息: {}", combinedInfo);
            
            // 7. 生成SHA-256指纹
            String fingerprint = DigestUtils.sha256Hex(combinedInfo);
            String formattedFingerprint = formatFingerprint(fingerprint);
            
            log.debug("✅ 硬件指纹生成成功: {}", formattedFingerprint);
            return formattedFingerprint;
            
        } catch (Exception e) {
            log.error("❌ 获取硬件指纹失败", e);
            return "ERROR-GENERATING-FINGERPRINT-" + e.getClass().getSimpleName();
        }
    }
    
    /**
     * 规范化字符串，去除空白字符和特殊字符
     * 
     * @param input 输入字符串
     * @return 规范化后的字符串
     */
    private static String normalizeString(String input) {
        if (input == null) {
            return "";
        }
        // 去除前后空白，转换为大写，去除所有空白字符
        return input.trim().toUpperCase().replaceAll("\\s+", "_").replaceAll("[^A-Z0-9_.-]", "");
    }
    
    /**
     * 格式化指纹为更友好的形式
     * 
     * @param fingerprint 原始指纹字符串
     * @return 格式化后的指纹
     */
    private static String formatFingerprint(String fingerprint) {
        // 取前32位，每4位插入一个连字符
        StringBuilder formatted = new StringBuilder();
        String shortened = fingerprint.substring(0, Math.min(fingerprint.length(), 32));
        
        for (int i = 0; i < shortened.length(); i++) {
            if (i > 0 && i % 4 == 0) {
                formatted.append('-');
            }
            formatted.append(shortened.charAt(i));
        }
        
        return formatted.toString().toUpperCase();
    }
    
    /**
     * 获取详细的硬件信息（用于调试）
     * 
     * @return 详细的硬件信息列表
     */
    public static List<String> getDetailedHardwareInfo() {
        List<String> details = new ArrayList<>();
        try {
            SystemInfo systemInfo = getSystemInfo();
            HardwareAbstractionLayer hardware = systemInfo.getHardware();
            OperatingSystem os = systemInfo.getOperatingSystem();
            
            details.add("=== 硬件指纹调试信息 ===");
            details.add("时间: " + java.time.LocalDateTime.now());
            details.add("");
            
            // CPU信息
            details.add("【CPU信息】");
            try {
                CentralProcessor processor = hardware.getProcessor();
                details.add("  处理器ID: " + processor.getProcessorIdentifier().getProcessorID());
                details.add("  处理器名称: " + processor.getProcessorIdentifier().getName());
                details.add("  规范化ID: " + normalizeString(processor.getProcessorIdentifier().getProcessorID()));
                details.add("  规范化名称: " + normalizeString(processor.getProcessorIdentifier().getName()));
                details.add("  物理核心数: " + processor.getPhysicalProcessorCount());
                details.add("  逻辑核心数: " + processor.getLogicalProcessorCount());
                details.add("  制造商: " + processor.getProcessorIdentifier().getVendor());
                details.add("  系列: " + processor.getProcessorIdentifier().getFamily());
                details.add("  型号: " + processor.getProcessorIdentifier().getModel());
            } catch (Exception e) {
                details.add("  ❌ CPU信息获取失败: " + e.getMessage());
            }
            details.add("");
            
            // 主板信息
            details.add("【主板信息】");
            try {
                ComputerSystem computerSystem = hardware.getComputerSystem();
                details.add("  制造商: " + computerSystem.getManufacturer());
                details.add("  型号: " + computerSystem.getModel());
                details.add("  序列号: " + computerSystem.getSerialNumber());
                details.add("  规范化制造商: " + normalizeString(computerSystem.getManufacturer()));
                details.add("  规范化型号: " + normalizeString(computerSystem.getModel()));
                details.add("  规范化序列号: " + normalizeString(computerSystem.getSerialNumber()));
                
                // 基板信息
                if (computerSystem.getBaseboard() != null) {
                    details.add("  基板制造商: " + computerSystem.getBaseboard().getManufacturer());
                    details.add("  基板型号: " + computerSystem.getBaseboard().getModel());
                    details.add("  基板序列号: " + computerSystem.getBaseboard().getSerialNumber());
                }
            } catch (Exception e) {
                details.add("  ❌ 主板信息获取失败: " + e.getMessage());
            }
            details.add("");
            
            // BIOS信息
            details.add("【BIOS信息】");
            try {
                ComputerSystem computerSystem = hardware.getComputerSystem();
                details.add("  制造商: " + computerSystem.getFirmware().getManufacturer());
                details.add("  版本: " + computerSystem.getFirmware().getVersion());
                details.add("  发布日期: " + computerSystem.getFirmware().getReleaseDate());
                details.add("  规范化制造商: " + normalizeString(computerSystem.getFirmware().getManufacturer()));
                details.add("  规范化版本: " + normalizeString(computerSystem.getFirmware().getVersion()));
            } catch (Exception e) {
                details.add("  ❌ BIOS信息获取失败: " + e.getMessage());
            }
            details.add("");
            
            // 内存信息
            details.add("【内存信息】");
            try {
                long totalMemory = hardware.getMemory().getTotal();
                details.add("  总内存: " + (totalMemory / 1024 / 1024 / 1024) + " GB");
                details.add("  内存模块数量: " + hardware.getMemory().getPhysicalMemory().size());
            } catch (Exception e) {
                details.add("  ❌ 内存信息获取失败: " + e.getMessage());
            }
            details.add("");
            
            // 网络接口信息（可能不稳定，仅用于调试）
            details.add("【网络接口信息】（不用于指纹生成）");
            try {
                List<NetworkIF> networkIFs = hardware.getNetworkIFs();
                for (int i = 0; i < networkIFs.size(); i++) {
                    NetworkIF net = networkIFs.get(i);
                    details.add("  接口 " + (i + 1) + ":");
                    details.add("    名称: " + net.getName());
                    details.add("    显示名称: " + net.getDisplayName());
                    details.add("    MAC地址: " + net.getMacaddr());
                    details.add("    是否启用: " + (net.getSpeed() > 0));
                }
            } catch (Exception e) {
                details.add("  ❌ 网络接口信息获取失败: " + e.getMessage());
            }
            details.add("");
            
            // 操作系统信息
            details.add("【操作系统信息】");
            try {
                details.add("  系统家族: " + os.getFamily());
                details.add("  制造商: " + os.getManufacturer());
                details.add("  版本: " + os.getVersionInfo().getVersion());
                details.add("  构建号: " + os.getVersionInfo().getBuildNumber());
                details.add("  系统名称: " + System.getProperty("os.name"));
                details.add("  系统版本: " + System.getProperty("os.version"));
                details.add("  系统架构: " + System.getProperty("os.arch"));
            } catch (Exception e) {
                details.add("  ❌ 操作系统信息获取失败: " + e.getMessage());
            }
            details.add("");
            
            // 当前指纹
            details.add("【当前硬件指纹】");
            String currentFingerprint = getHardwareFingerprint();
            details.add("  指纹: " + currentFingerprint);
            details.add("");
            
        } catch (Exception e) {
            log.error("获取详细硬件信息失败", e);
            details.add("❌ 获取硬件信息时发生错误: " + e.getMessage());
            details.add("错误类型: " + e.getClass().getName());
            if (e.getStackTrace().length > 0) {
                details.add("错误位置: " + e.getStackTrace()[0]);
            }
        }
        
        return details;
    }
}
