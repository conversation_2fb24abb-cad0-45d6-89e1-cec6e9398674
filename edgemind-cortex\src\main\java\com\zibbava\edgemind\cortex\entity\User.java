package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@TableName("sys_user") // MyBatis Plus: 指定数据库表名
public class User {

    @TableId(type = IdType.AUTO) // MyBatis Plus: 指定主键及自增类型
    private Long id;

    @TableField("username") // MyBatis Plus: 明确指定列名
    private String username;

    @TableField("password") // MyBatis Plus: 明确指定列名
    private String password;

    @TableField("nickname") // 补充: 用户昵称/姓名
    private String nickname;

    @TableField("email") // 补充: 邮箱
    private String email;

    @TableField("phone") // 补充: 手机号
    private String phone;

    @TableField("status") // 补充: 用户状态 (0: 禁用, 1: 启用)
    private Integer status;

    @TableField("dept_id") // 补充: 所属部门 ID
    private Long deptId;

    @TableField("remark") // 补充: 备注
    private String remark;

    @TableField("avatar") // 用户头像路径
    private String avatar;

    @TableField("last_login_time") // 最后登录时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastLoginTime;

    @TableField("last_login_ip") // 最后登录IP
    private String lastLoginIp;

    @TableField("password_update_time") // 密码最后更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime passwordUpdateTime;

    @TableField("account_locked") // 账户是否锁定
    private Boolean accountLocked;

    @TableField("lock_time") // 锁定时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lockTime;

    @TableField("failed_login_attempts") // 连续登录失败次数
    private Integer failedLoginAttempts;

    // MyBatis Plus: 配置创建时间自动填充 (需要配合 MetaObjectHandler)
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    // MyBatis Plus: 配置更新时间自动填充 (需要配合 MetaObjectHandler)
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    // MyBatis Plus: 逻辑删除字段 (0: 未删除, 1: 已删除)
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 删除时间 (软删除时记录删除时间)
    @TableField("delete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deleteTime;

    // 删除操作人ID
    @TableField("delete_user_id")
    private Long deleteUserId;

    // ----- 关联关系 (非数据库字段) -----

    // 用户拥有的角色集合 (查询时填充)
    @TableField(exist = false)
    private Set<Role> roles;

    // 用户拥有的角色ID集合 (方便更新)
    @TableField(exist = false)
    private List<Long> roleIds;

    // 用户所属部门信息 (查询时填充)
    @TableField(exist = false)
    private Department department; // 对应 deptId

} 