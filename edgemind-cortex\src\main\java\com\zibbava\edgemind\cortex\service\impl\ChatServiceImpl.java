package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zibbava.edgemind.cortex.entity.ChatConversation;
import com.zibbava.edgemind.cortex.entity.ChatMessage;
import com.zibbava.edgemind.cortex.mapper.ChatConversationMapper;
import com.zibbava.edgemind.cortex.mapper.ChatMessageMapper;
import com.zibbava.edgemind.cortex.service.ChatService;
import com.zibbava.edgemind.cortex.service.ModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.SynchronousSink;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 聊天服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ChatServiceImpl implements ChatService {

    @Autowired
    private final ChatConversationMapper conversationMapper;
    @Autowired
    private final ChatMessageMapper messageMapper;
    @Autowired
    private ModelService modelService;
    @Autowired
    private List<Object> tools;

    // Inject properties
    @Value("${ai.model.ollama.baseUrl:http://localhost:11434}")
    private String ollamaBaseUrl;
    @Value("${ai.model.ollama.timeout:120}")
    private long ollamaTimeoutSeconds;

    @Value("${ai.model.deepseek.baseUrl:https://api.deepseek.com/v1}")
    private String deepseekBaseUrl;
    @Value("${ai.model.deepseek.apiKey:***********************************}")
    private String deepseekApiKey;
    @Value("${ai.model.deepseek.modelName:deepseek-reasoner}")
    private String deepseekModelName;
    @Value("${ai.model.deepseek.timeout:120}")
    private long deepseekTimeoutSeconds;

    @Value("${ai.model.openai.baseUrl:https://api.openai.com/v1}")
    private String openaiBaseUrl;
    @Value("${ai.model.openai.apiKey:YOUR_OPENAI_API_KEY}")
    private String openaiApiKey;
    @Value("${ai.model.openai.modelName:gpt-3.5-turbo}")
    private String openaiModelName;
    @Value("${ai.model.openai.timeout:120}")
    private long openaiTimeoutSeconds;

    @Override
    @Transactional
    public ChatConversation createConversation(Long userId, String title) {
        ChatConversation conversation = new ChatConversation();
        conversation.setUserId(userId);
        conversation.setTitle(title);

        LocalDateTime now = LocalDateTime.now();
        conversation.setCreateTime(now);
        conversation.setUpdateTime(now);
        conversation.setStatus(1); // 正常状态

        conversationMapper.insert(conversation);
        log.info("创建新会话: userId={}, title={}, id={}", userId, title, conversation.getId());
        return conversation;
    }

    @Override
    public List<ChatConversation> getUserConversations(Long userId) {
        return conversationMapper.selectActiveByUserId(userId);
    }

    @Override
    public ChatConversation getConversation(Long conversationId) {
        return conversationMapper.selectById(conversationId);
    }

    @Override
    public List<ChatMessage> getConversationMessages(Long conversationId) {
        return messageMapper.selectByConversationId(conversationId);
    }

    @Override
    @Transactional
    public ChatMessage saveUserMessage(Long conversationId, String content, String imagePath) {
        ChatMessage message = new ChatMessage();
        message.setConversationId(conversationId);
        message.setSender("user");
        message.setContent(content);
        message.setImagePath(imagePath);
        message.setCreateTime(LocalDateTime.now());

        messageMapper.insert(message);

        // 更新会话的最后更新时间
        updateConversationUpdateTime(conversationId);

        return message;
    }

    @Override
    @Transactional
    public ChatMessage saveAiMessage(Long conversationId, String content, String thinkingContent, Integer tokenCount, String modelName) {
        ChatMessage message = new ChatMessage();
        message.setConversationId(conversationId);
        message.setSender("ai");
        message.setContent(content);
        message.setThinkingContent(thinkingContent);
        message.setTokenCount(tokenCount);
        message.setModelName(modelName);
        message.setCreateTime(LocalDateTime.now());

        messageMapper.insert(message);

        // 更新会话的最后更新时间
        updateConversationUpdateTime(conversationId);

        return message;
    }

    @Override
    public boolean updateConversationTitle(Long conversationId, String title) {
        ChatConversation conversation = new ChatConversation();
        conversation.setId(conversationId);
        conversation.setTitle(title);
        conversation.setUpdateTime(LocalDateTime.now());

        return conversationMapper.updateById(conversation) > 0;
    }

    @Override
    public boolean deleteConversation(Long conversationId) {
        LambdaUpdateWrapper<ChatConversation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatConversation::getId, conversationId)
                .set(ChatConversation::getStatus, 0)
                .set(ChatConversation::getUpdateTime, LocalDateTime.now());

        return conversationMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 更新会话的最后更新时间
     *
     * @param conversationId 会话ID
     */
    private void updateConversationUpdateTime(Long conversationId) {
        LambdaUpdateWrapper<ChatConversation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChatConversation::getId, conversationId)
                .set(ChatConversation::getUpdateTime, LocalDateTime.now());

        conversationMapper.update(null, updateWrapper);
    }

    @Override
    public List<ChatConversation> getRecentUserConversations(Long userId, Integer limit) {
        return conversationMapper.selectRecentActiveByUserId(userId, limit);
    }

    @Override
    public List<ChatMessage> getRecentConversationMessages(Long conversationId, Integer limit) {
        return messageMapper.selectRecentByConversationId(conversationId, limit);
    }

    @Override
    public List<ChatMessage> getConversationMessagesPaged(Long conversationId, Integer page, Integer size) {
        // 计算偏移量
        int offset = page * size;

        // 获取消息（按创建时间倒序，最新的在前）
        List<ChatMessage> messages = messageMapper.selectPagedByConversationIdDesc(conversationId, offset, size);

        // 返回前重新按照创建时间升序排序（前端显示时旧消息在上，新消息在下）
        messages.sort((a, b) -> a.getCreateTime().compareTo(b.getCreateTime()));

        return messages;
    }

    /**
     * 获取会话的消息总数
     *
     * @param conversationId 会话ID
     * @return 消息总数
     */
    @Override
    public int getConversationMessagesCount(Long conversationId) {
        return messageMapper.countByConversationId(conversationId);
    }

    @Override
    public int getUserConversationsCount(Long userId) {
        // 使用LambdaQueryWrapper查询用户有效会话的总数
        LambdaQueryWrapper<ChatConversation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatConversation::getUserId, userId)
                .eq(ChatConversation::getStatus, 1);

        return Math.toIntExact(conversationMapper.selectCount(queryWrapper));
    }

    @Override
    public List<ChatConversation> getUserConversationsPaged(Long userId, Integer page, Integer size) {
        // 计算偏移量
        int offset = page * size;

        // 执行分页查询，按更新时间倒序排列
        List<ChatConversation> conversations = conversationMapper.selectPagedActiveByUserId(userId, offset, size);

        return conversations;
    }

    @Override
    public List<ChatMessage> getMessagesByConversationId(Long conversationId) {
        if (conversationId == null) {
            return List.of();
        }

        // 使用Lambda查询构建器创建按创建时间升序排序的查询
        LambdaQueryWrapper<ChatMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMessage::getConversationId, conversationId)
                .orderByAsc(ChatMessage::getCreateTime);

        return messageMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional
    public boolean deleteMessagesByConversationId(Long conversationId) {
        if (conversationId == null) {
            return false;
        }

        // 构建删除条件
        LambdaQueryWrapper<ChatMessage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChatMessage::getConversationId, conversationId);

        // 执行删除操作
        int deletedCount = messageMapper.delete(queryWrapper);
        log.info("已删除会话 {} 的 {} 条消息", conversationId, deletedCount);

        return deletedCount > 0;
    }

    @Override
    public void handelSSE(StringBuilder mainContentBuilder, StringBuilder thinkingContentBuilder, String rawToken, SynchronousSink<String> sink, AtomicBoolean isInsideThinkingBlock) {
        // 支持 Ollama v0.9.0 的新字段分离格式
        // 使用前缀标识来区分思考内容和主要内容
        
        if (rawToken == null || rawToken.isEmpty()) {
            return;
        }
        
        try {
            if (rawToken.startsWith("THINKING:")) {
                // 处理思考内容
                String thinkingContent = rawToken.substring(9); // 移除 "THINKING:" 前缀
                thinkingContentBuilder.append(thinkingContent);
                
                // 发送思考内容给前端，使用特殊前缀
                String processedForSSE = "THINKING:" + thinkingContent.replace("\n", "__NEWLINE__");
                sink.next(processedForSSE);
                
                log.debug("💭 处理思考内容: {} 字符", thinkingContent.length());
                
            } else if (rawToken.startsWith("CONTENT:")) {
                // 处理主要内容
                String mainContent = rawToken.substring(8); // 移除 "CONTENT:" 前缀
                mainContentBuilder.append(mainContent);
                
                // 发送主要内容给前端，使用特殊前缀
                String processedForSSE = "CONTENT:" + mainContent.replace("\n", "__NEWLINE__");
                sink.next(processedForSSE);
                
                log.debug("📝 处理主要内容: {} 字符", mainContent.length());
                
            } else if (rawToken.startsWith("DONE:")) {
                // 处理完成标识
                sink.next("DONE:");
                log.debug("✅ 收到完成标识");
                
            } else {
                // 处理其他类型的内容（如工具调用结果等）
                String processedForSSE = rawToken.replace("\n", "__NEWLINE__");
                sink.next(processedForSSE);
                log.debug("🔧 处理其他内容: {}", rawToken.substring(0, Math.min(50, rawToken.length())));
            }
            
        } catch (Exception e) {
            log.error("❌ 处理SSE Token时出错: {}", e.getMessage(), e);
            // 发生错误时，仍然尝试发送原始内容
            String processedForSSE = rawToken.replace("\n", "__NEWLINE__");
            sink.next(processedForSSE);
        }
    }


} 