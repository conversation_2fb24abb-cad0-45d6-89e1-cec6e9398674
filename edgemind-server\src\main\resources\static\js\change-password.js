// 显示提示信息
function showMessage(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    const container = document.getElementById('alertContainer');
    container.innerHTML = '';
    container.appendChild(alertDiv);
    
    // 3秒后自动隐藏
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// 设置加载状态
function setLoading(loading) {
    const submitBtn = document.querySelector('.btn-change-password');
    const spinner = document.querySelector('.loading-spinner');
    const btnText = submitBtn.querySelector('i').nextSibling;
    
    if (loading) {
        submitBtn.disabled = true;
        spinner.style.display = 'inline-block';
        btnText.textContent = '修改中...';
    } else {
        submitBtn.disabled = false;
        spinner.style.display = 'none';
        btnText.textContent = '修改密码';
    }
}

// 切换密码显示
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.parentElement.querySelector('.password-toggle');
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'bi bi-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'bi bi-eye';
    }
}

// 检查密码强度
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = [];
    
    // 长度检查
    if (password.length >= 8) {
        strength += 1;
    } else {
        feedback.push('至少8个字符');
    }
    
    // 包含小写字母
    if (/[a-z]/.test(password)) {
        strength += 1;
    } else {
        feedback.push('包含小写字母');
    }
    
    // 包含大写字母
    if (/[A-Z]/.test(password)) {
        strength += 1;
    } else {
        feedback.push('包含大写字母');
    }
    
    // 包含数字
    if (/\d/.test(password)) {
        strength += 1;
    } else {
        feedback.push('包含数字');
    }
    
    // 包含特殊字符
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        strength += 1;
    } else {
        feedback.push('包含特殊字符');
    }
    
    return { strength, feedback };
}

// 更新密码强度显示
function updatePasswordStrength(password) {
    const strengthBar = document.querySelector('.strength-fill');
    const strengthText = document.querySelector('.strength-text');
    
    if (!password) {
        strengthBar.style.width = '0%';
        strengthBar.className = 'strength-fill';
        strengthText.textContent = '';
        return;
    }
    
    const { strength, feedback } = checkPasswordStrength(password);
    const percentage = (strength / 5) * 100;
    
    strengthBar.style.width = percentage + '%';
    
    // 移除所有强度类
    strengthBar.classList.remove('weak', 'medium', 'strong');
    
    let strengthLevel = '';
    if (strength <= 2) {
        strengthBar.classList.add('weak');
        strengthLevel = '弱';
    } else if (strength <= 4) {
        strengthBar.classList.add('medium');
        strengthLevel = '中';
    } else {
        strengthBar.classList.add('strong');
        strengthLevel = '强';
    }
    
    strengthText.textContent = `密码强度: ${strengthLevel}`;
    if (feedback.length > 0) {
        strengthText.textContent += ` (建议: ${feedback.join(', ')})`;
    }
}

// 判断密码强度是否足够
function isPasswordStrong(password) {
    const { strength } = checkPasswordStrength(password);
    return strength >= 3; // 至少需要3个条件
}

// 验证密码
function validatePasswords() {
    const oldPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (!oldPassword) {
        showMessage('请输入当前密码', 'warning');
        return false;
    }
    
    if (!newPassword) {
        showMessage('请输入新密码', 'warning');
        return false;
    }
    
    if (newPassword.length < 6) {
        showMessage('新密码长度至少6位', 'warning');
        return false;
    }
    
    if (oldPassword === newPassword) {
        showMessage('新密码不能与当前密码相同', 'warning');
        return false;
    }
    
    if (newPassword !== confirmPassword) {
        showMessage('两次输入的新密码不一致', 'warning');
        return false;
    }
    
    if (!isPasswordStrong(newPassword)) {
        showMessage('密码强度不够，请设置更复杂的密码', 'warning');
        return false;
    }
    
    return true;
}

// 返回上一页
function goBack() {
    window.history.back();
}

// 处理表单提交
function handleSubmit(event) {
    event.preventDefault();
    
    if (!validatePasswords()) {
        return;
    }
    
    const formData = {
        oldPassword: document.getElementById('currentPassword').value,
        newPassword: document.getElementById('newPassword').value,
        confirmPassword: document.getElementById('confirmPassword').value
    };
    
    setLoading(true);
    
    ApiUtils.post('/admin/change-password', formData)
        .then(response => {
            if (response.code === 200) {
                showMessage('密码修改成功！', 'success');
                // 清空表单
                document.getElementById('changePasswordForm').reset();
                updatePasswordStrength('');
                
                // 2秒后跳转到登录页
                setTimeout(() => {
                    ApiUtils.redirectToLogin();
                }, 2000);
            } else {
                showMessage(response.message || '密码修改失败', 'danger');
            }
        })
        .catch(error => {
            console.error('修改密码失败:', error);
            showMessage('网络错误，请稍后重试', 'danger');
        })
        .finally(() => {
            setLoading(false);
        });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 新密码输入监听
    const newPasswordInput = document.getElementById('newPassword');
    newPasswordInput.addEventListener('input', function() {
        updatePasswordStrength(this.value);
    });
    
    // 聚焦到第一个输入框
    document.getElementById('currentPassword').focus();
    
    // 回车键提交表单
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSubmit(e);
        }
    });
    
    // 表单提交事件
    document.getElementById('changePasswordForm').addEventListener('submit', handleSubmit);
});

// 返回上一页
function goBack() {
    window.history.back();
}