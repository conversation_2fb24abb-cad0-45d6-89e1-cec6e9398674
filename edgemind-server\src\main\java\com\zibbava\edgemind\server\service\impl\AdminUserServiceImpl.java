package com.zibbava.edgemind.server.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.server.common.excepttion.BusinessException;
import com.zibbava.edgemind.server.common.enums.ResultCode;
import com.zibbava.edgemind.server.dto.ChangePasswordDTO;
import com.zibbava.edgemind.server.dto.LoginRequestDTO;
import com.zibbava.edgemind.server.entity.AdminUser;
import com.zibbava.edgemind.server.mapper.AdminUserMapper;
import com.zibbava.edgemind.server.service.AdminUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 管理员用户服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminUserServiceImpl extends ServiceImpl<AdminUserMapper, AdminUser> implements AdminUserService {

    private final AdminUserMapper adminUserMapper;


    @Override
    public String login(LoginRequestDTO loginRequest, String clientIp) {
        // 根据用户名查询管理员
        AdminUser adminUser = adminUserMapper.selectByUsername(loginRequest.getUsername());
        if (adminUser == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "用户名或密码错误");
        }

        // 验证密码 - 使用BCrypt验证加密密码

        if (!SaSecureUtil.sha1(loginRequest.getPassword()).equals(adminUser.getPassword())) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "用户名或密码错误");
        }

        // 检查用户状态
        if (adminUser.getStatus() != 1) {
            throw new BusinessException(ResultCode.FORBIDDEN, "账号已被禁用");
        }

        // 执行登录
        StpUtil.login(adminUser.getId());
        
        // 更新最后登录信息
        updateLastLoginInfo(adminUser.getId(), clientIp);
        
        // 返回token
        return StpUtil.getTokenValue();
    }

    @Override
    public void logout() {
        StpUtil.logout();
    }

    @Override
    public AdminUser getCurrentAdmin() {
        if (!StpUtil.isLogin()) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "用户未登录");
        }
        
        Long adminId = StpUtil.getLoginIdAsLong();
        AdminUser adminUser = adminUserMapper.selectById(adminId);
        if (adminUser == null) {
            throw new BusinessException(ResultCode.DATA_NOT_EXISTS, "用户不存在");
        }
        
        // 清除敏感信息
        adminUser.setPassword(null);
        return adminUser;
    }

    @Override
    public boolean changePassword(ChangePasswordDTO changePasswordDTO) {
        // 检查新密码和确认密码是否一致
        if (!changePasswordDTO.getNewPassword().equals(changePasswordDTO.getConfirmPassword())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "新密码和确认密码不一致");
        }

        // 获取当前登录用户
        AdminUser currentAdmin = getCurrentAdmin();
        
        // 重新查询用户信息（包含密码）
        AdminUser adminUser = adminUserMapper.selectById(currentAdmin.getId());
        if (adminUser == null) {
            throw new BusinessException(ResultCode.DATA_NOT_EXISTS, "用户不存在");
        }

        // 验证原密码
        if (!SaSecureUtil.sha1(changePasswordDTO.getOldPassword()).equals(adminUser.getPassword())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "原密码错误");
        }

        // 使用BCrypt加密新密码
        String encodedNewPassword = SaSecureUtil.sha1(changePasswordDTO.getNewPassword());
        
        // 更新密码
        int result = adminUserMapper.updatePassword(adminUser.getId(), encodedNewPassword, currentAdmin.getUsername());
        
        if (result > 0) {
            log.info("管理员 {} 修改密码成功", currentAdmin.getUsername());
            return true;
        }
        
        return false;
    }

    @Override
    public AdminUser getByUsername(String username) {
        return adminUserMapper.selectByUsername(username);
    }

    @Override
    public void updateLastLoginInfo(Long adminId, String clientIp) {
        adminUserMapper.updateLastLoginInfo(adminId, LocalDateTime.now(), clientIp);
    }

}