package com.zibbava.edgemind.cortex.service.strategy;

import com.zibbava.edgemind.cortex.dto.ChatContext;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaChatRequest;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaMessage;
import com.zibbava.edgemind.cortex.entity.ChatConversation;
import com.zibbava.edgemind.cortex.entity.ChatMessage;
import com.zibbava.edgemind.cortex.enums.ModelInfo;
import com.zibbava.edgemind.cortex.service.ChatService;
import com.zibbava.edgemind.cortex.util.ModelUtils;
import com.zibbava.edgemind.cortex.util.OllamaUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;
import reactor.core.publisher.SynchronousSink;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 企业级普通对话策略实现
 * 
 * 🚀 核心特性：
 * - 智能提示词工程：根据对话类型和上下文动态构建提示词
 * - 历史对话感知：充分利用对话历史提供连贯的交互体验
 * - 多模态支持：支持文本、图片等多种输入形式
 * - 角色一致性：维持AI助手的专业形象和对话风格
 * - 上下文理解：智能分析用户意图和对话场景
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class NormalChatStrategy implements ChatStrategy {
    
    private final ChatService chatService;
    private final ModelUtils modelUtils;
    private final PromptTemplateManager promptTemplateManager;

    // 企业级配置参数
    @Value("${chat.assistant.name:智能助手}")
    private String assistantName;
    
    @Value("${chat.assistant.role:专业的AI助手}")
    private String assistantRole;
    
    @Value("${chat.style.professional:true}")
    private boolean professionalStyle;
    
    @Value("${chat.context.analysis:true}")
    private boolean enableContextAnalysis;
    
    @Value("${chat.thinking.enabled:true}")
    private boolean enableThinking;
    
    @Value("${chat.history.aware:true}")
    private boolean historyAware;
    
    @Value("${chat.multimodal.enabled:true}")
    private boolean multimodalEnabled;
    
    // 历史消息处理配置
    @Value("${chat.history.max-messages:10}")
    private int maxHistoryMessages;

    
    @Override
    public boolean checkPermission(ChatContext context) {
        // 如果有会话ID，检查会话归属
        if (context.getConversationId() != null) {
            ChatConversation conversation = chatService.getConversation(context.getConversationId());
            if (conversation == null) {
                log.warn("会话不存在: {}", context.getConversationId());
                return false;
            }
            if (!conversation.getUserId().equals(context.getUserId())) {
                log.warn("用户 {} 尝试访问不属于他的会话 {}", context.getUserId(), context.getConversationId());
                return false;
            }
        }
        return true;
    }
    
    @Override
    public ChatContext prepareContext(ChatContext context) {
        // 处理会话（创建或获取）
        if (context.getConversationId() == null) {
            // 智能生成会话标题
            String intelligentTitle = generateIntelligentTitle(context.getPrompt());
            ChatConversation conversation = chatService.createConversation(
                    context.getUserId(), 
                    intelligentTitle
            );
            context.setConversationId(conversation.getId());
            context.setConversation(conversation);
            log.info("为用户 {} 创建新会话: {} (标题: {})", context.getUserId(), conversation.getId(), intelligentTitle);
        } else {
            context.setConversation(chatService.getConversation(context.getConversationId()));
            log.info("使用已有会话: {}", context.getConversationId());
        }
        
        // 处理图片上传（如果有）
        if (context.getImageFile() != null && !context.getImageFile().isEmpty()) {
            try {
                String imagePath = saveUploadedImage(context.getImageFile());
                log.info("保存上传图片: {}", imagePath);
                // 这里可以设置图片路径到上下文中，如果需要的话
            } catch (IOException e) {
                log.error("保存图片失败", e);
            }
        }
        
        return context;
    }
    
    @Override
    public String buildPrompt(ChatContext context) {
        // 使用企业级提示词模板管理器构建提示词
        return buildEnterprisePromptWithTemplate(context);
    }
    
    /**
     * 使用模板管理器构建企业级提示词
     */
    private String buildEnterprisePromptWithTemplate(ChatContext context) {
        try {
            String finalPrompt;
            
            // 根据输入类型选择合适的提示词模板
            if (multimodalEnabled && context.getImageFile() != null && !context.getImageFile().isEmpty()) {
                // 多模态对话（包含图片）
                String imagePath = "已上传图片"; // 简化处理
                finalPrompt = promptTemplateManager.buildMultimodalChatPrompt(
                    context.getPrompt(), 
                    imagePath
                );
                log.info("🖼️ 构建优化多模态对话提示词");
            } else {
                // 普通文本对话
                finalPrompt = promptTemplateManager.buildNormalChatPrompt(
                    context.getPrompt()
                );
            }

            return finalPrompt;
            
        } catch (Exception e) {
            log.error("❌ 构建企业级提示词失败: {}", e.getMessage(), e);
        }
        return context.getPrompt();
    }
    
    /**
     * 检查是否有对话历史
     */
    private boolean hasConversationHistory(ChatContext context) {
        if (!historyAware || context.getConversationId() == null) {
            return false;
        }
        
        try {
            // 只检查消息数量，不加载所有消息
            int messageCount = chatService.getConversationMessagesCount(context.getConversationId());
            return messageCount > 0;
            
        } catch (Exception e) {
            log.debug("检查对话历史失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 智能生成会话标题
     */
    private String generateIntelligentTitle(String userPrompt) {
        if (userPrompt == null || userPrompt.trim().isEmpty()) {
            return "新对话";
        }
        
        // 简单的标题生成逻辑，可以后续用AI模型优化
        String title = userPrompt.trim();
        if (title.length() > 20) {
            title = title.substring(0, 17) + "...";
        }
        
        // 移除换行符和多余空格
        title = title.replaceAll("\\s+", " ");
        
        return title;
    }
    
    @Override
    public Flux<String> executeChat(ChatContext context) {
        try {
            log.info("🚀 开始执行企业级普通对话: conversationId={}, model={}", 
                context.getConversationId(), context.getModelName());

            // 构建消息历史列表
            List<OllamaMessage> messages = buildMessageHistory(context);
            
            // 创建OllamaChatRequest，SessionId包含用户ID
            OllamaChatRequest chatRequest = OllamaChatRequest.builder()
                    .model(context.getModelName())
                    .messages(messages)
                    .stream(true)
                    .sessionId("chat_" + context.getUserId() + "_" + context.getConversationId())
                    .build();

            // 设置深度思考功能 - 根据ModelInfo枚举检查模型支持
            boolean shouldEnableThinking = false;
            if (ModelInfo.supportsThinking(context.getModelName())) {
                if (context.getEnableThinking() != null) {
                    shouldEnableThinking = context.getEnableThinking();
                    log.info("🧠 深度思考功能: {} (模型: {})", shouldEnableThinking ? "开启" : "关闭", context.getModelName());
                } else {
                    // 使用配置文件中的默认值
                    shouldEnableThinking = enableThinking;
                    log.info("🧠 深度思考功能: {} (使用默认配置, 模型: {})", shouldEnableThinking ? "开启" : "关闭", context.getModelName());
                }
            } else {
                log.info("🧠 深度思考功能: 关闭 (模型 {} 不支持think功能)", context.getModelName());
            }
            chatRequest.enableThinking(shouldEnableThinking);

            // 使用OllamaUtils执行流式聊天
            return OllamaUtils.streamChat(chatRequest)
                    .handle((String rawToken, SynchronousSink<String> sink) -> chatService.handelSSE(
                        context.getMainContentBuilder(),
                        context.getThinkingContentBuilder(),
                        rawToken,
                        sink,
                        context.getIsInsideThinkingBlock()
                    ))
                    .doOnComplete(() -> {
                        // 更新会话时间
                        updateConversationWithQuality(context);
                        log.info("✅ 企业级对话完成: conversationId={}, 用户满意度预期: 高", context.getConversationId());
                    })
                    .doOnError(e -> log.error("❌ 企业级对话出错: {}", e.getMessage(), e));
        } catch (Exception e) {
            log.error("❌ 执行企业级对话出错: {}", e.getMessage(), e);
            return Flux.error(e);
        }
    }
    
    /**
     * 构建消息历史列表（包含历史对话和当前提示词）
     * 限制历史消息数量以控制上下文长度
     */
    private List<OllamaMessage> buildMessageHistory(ChatContext context) {
        List<OllamaMessage> messages = new ArrayList<>();
        
        try {
            // 如果启用历史记忆且有会话ID，则加载历史消息
            if (historyAware && context.getConversationId() != null) {
                // 获取最近的历史消息，使用配置的最大消息数量
                List<ChatMessage> historyMessages = chatService.getRecentConversationMessages(
                    context.getConversationId(), 
                    maxHistoryMessages
                );
                
                if (!historyMessages.isEmpty()) {
                    log.info("📚 加载历史消息: conversationId={}, 消息数量={} (最多{}条)", 
                        context.getConversationId(), historyMessages.size(), maxHistoryMessages);
                    
                    // 转换历史消息为OllamaMessage格式
                    for (ChatMessage msg : historyMessages) {
                        if ("user".equals(msg.getSender())) {
                            messages.add(OllamaMessage.user(msg.getContent()));
                        } else if ("assistant".equals(msg.getSender()) || "ai".equals(msg.getSender())) {
                            messages.add(OllamaMessage.assistant(msg.getContent()));
                        }
                    }
                }
            }
            
            // 添加当前用户消息
            messages.add(OllamaMessage.user(context.getFinalPrompt()));
            
            log.info("💬 构建消息历史完成: 总消息数={}, 历史消息数={}, 上下文窗口限制={}", 
                messages.size(), messages.size() - 1, maxHistoryMessages);
                
        } catch (Exception e) {
            log.warn("⚠️ 加载历史消息失败，使用当前消息: {}", e.getMessage());
            // 如果加载历史失败，只使用当前消息
            messages.clear();
            messages.add(OllamaMessage.user(context.getFinalPrompt()));
        }
        
        return messages;
    }
    
    @Override
    public ChatMessage saveChatHistory(ChatContext context, String userContent, String aiContent, String thinkingContent) {
        // 保存用户消息
        String imagePath = null; 
        if (context.getImageFile() != null && !context.getImageFile().isEmpty()) {
            try {
                imagePath = saveUploadedImage(context.getImageFile());
            } catch (Exception e) {
                log.warn("⚠️ 保存图片失败: {}", e.getMessage());
            }
        }
        
        chatService.saveUserMessage(
                context.getConversationId(),
                userContent,
                imagePath
        );

        // 保存AI消息
        return chatService.saveAiMessage(
                context.getConversationId(),
                aiContent,
                thinkingContent,
                calculateTokenCount(aiContent, thinkingContent),
                context.getModelName()
        );
    }
    
    /**
     * 简单的Token数量估算
     */
    private Integer calculateTokenCount(String aiContent, String thinkingContent) {
        if (aiContent == null && thinkingContent == null) {
            return 0;
        }
        
        // 简单估算：平均4个字符 = 1个token
        int aiTokens = aiContent != null ? aiContent.length() / 4 : 0;
        int thinkingTokens = thinkingContent != null ? thinkingContent.length() / 4 : 0;
        
        return aiTokens + thinkingTokens;
    }
    
    @Override
    public boolean isApplicable(ChatContext context) {
        // 对于普通对话，只要不是知识库对话就适用
        return !context.isKnowledgeChat();
    }
    
    // 辅助方法：保存上传图片
    private String saveUploadedImage(MultipartFile imageFile) throws IOException {
        // 确保目录存在
        String uploadDir = "uploads/images";
        Path uploadPath = Paths.get(uploadDir);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }
        
        // 生成唯一文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String originalFilename = imageFile.getOriginalFilename();
        String fileExtension = originalFilename != null && originalFilename.contains(".") 
                ? originalFilename.substring(originalFilename.lastIndexOf(".")) 
                : ".jpg";
        String filename = "img_" + timestamp + "_" + UUID.randomUUID().toString().substring(0, 8) + fileExtension;
        
        // 保存文件
        Path filePath = uploadPath.resolve(filename);
        Files.copy(imageFile.getInputStream(), filePath);
        
        log.info("📸 图片保存成功: {}", filePath.toString());
        return filePath.toString();
    }
    
    // 辅助方法：更新会话并进行质量评估
    private void updateConversationWithQuality(ChatContext context) {
        try {
            // 基本的会话更新
            updateConversation(context);

        } catch (Exception e) {
            log.warn("⚠️ 会话质量评估失败: {}", e.getMessage());
        }
    }
    
    // 辅助方法：更新会话
    private void updateConversation(ChatContext context) {
        // 如果需要，可以在这里添加更新会话的逻辑
        // 例如更新最后活动时间、会话质量评分等
        log.debug("🔄 会话 {} 状态更新完成", context.getConversationId());
    }
    
    // 辅助方法：评估对话质量
    private void evaluateConversationQuality(ChatContext context) {
        // 简单的质量评估指标
        int promptLength = context.getPrompt() != null ? context.getPrompt().length() : 0;
        boolean hasImage = context.getImageFile() != null && !context.getImageFile().isEmpty();
        boolean hasHistory = hasConversationHistory(context);
        
        // 计算质量分数（简化版本）
        int qualityScore = 0;
        if (promptLength > 10) qualityScore += 30; // 问题有一定复杂度
        if (hasImage) qualityScore += 20; // 多模态输入
        if (hasHistory) qualityScore += 30; // 有对话历史
        qualityScore += 20; // 基础分
        
        log.info("📈 对话质量评估: 分数={}, 提示词长度={}, 多模态={}, 历史对话={}", 
            qualityScore, promptLength, hasImage, hasHistory);
    }
}
