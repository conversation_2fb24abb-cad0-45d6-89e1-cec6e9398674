package com.zibbava.edgemind.cortex.demo;

import com.zibbava.edgemind.cortex.demo.util.StopWordsLoader;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.ollama.OllamaEmbeddingModel;
import io.weaviate.client.Config;
import io.weaviate.client.WeaviateClient;
import io.weaviate.client.base.Result;
import io.weaviate.client.v1.data.model.WeaviateObject;
import io.weaviate.client.v1.filters.Operator;
import io.weaviate.client.v1.filters.WhereFilter;
import io.weaviate.client.v1.graphql.model.GraphQLResponse;
import io.weaviate.client.v1.graphql.query.argument.Bm25Argument;
import io.weaviate.client.v1.graphql.query.argument.HybridArgument;
import io.weaviate.client.v1.graphql.query.argument.WhereArgument;
import io.weaviate.client.v1.graphql.query.fields.Field;
import io.weaviate.client.v1.misc.model.InvertedIndexConfig;
import io.weaviate.client.v1.misc.model.StopwordConfig;
import io.weaviate.client.v1.schema.model.DataType;
import io.weaviate.client.v1.schema.model.Property;
import io.weaviate.client.v1.schema.model.WeaviateClass;

import java.time.Duration;
import java.time.Instant;
import java.util.*;

/**
 * 实现weaviate混合检索示例 - 文档检索系统
 * 优化配置以适合消费级电脑使用
 * 使用文件加载的停用词配置提升检索质量
 */
public class WeaviateExample {

    private static final String WEAVIATE_URL = "http://localhost:8090";
    private static final String OLLAMA_URL = "http://localhost:11434";
    private static final String EMBEDDING_MODEL = "bge-large:latest";
    private static final String CLASS_NAME = "Document";

    // 文档相关字段定义
    private static final String NODE_ID_FIELD = "node_id";
    private static final String SPACE_ID_FIELD = "space_id";
    private static final String FILE_NAME_FIELD = "file_name";
    private static final String USER_ID_FIELD = "user_id";
    private static final String MIME_TYPE_FIELD = "mime_type";
    private static final String CREATED_TIME_FIELD = "created_time";
    private static final String TITLE = "title";
    private static final String CONTENT = "content";

    private WeaviateClient client;
    private EmbeddingModel embeddingModel;

    /**
     * 初始化Weaviate客户端和Ollama嵌入模型
     */
    public void initialize() {
        try {
            // 初始化Weaviate客户端
            Config config = new Config("http", "localhost:8090");
            this.client = new WeaviateClient(config);

            // 初始化Ollama嵌入模型
            this.embeddingModel = OllamaEmbeddingModel.builder()
                    .baseUrl(OLLAMA_URL)
                    .modelName(EMBEDDING_MODEL)
                    .timeout(Duration.ofMinutes(2))
                    .build();

            // 创建Schema
            createOptimizedSchema();

            System.out.println("Weaviate和Ollama初始化成功");
        } catch (Exception e) {
            System.err.println("初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 使用Ollama将文本转换为向量
     */
    public Float[] textToVector(String text) {
        try {
            var embedding = embeddingModel.embed(text).content();
            return embedding.vectorAsList().stream()
                    .map(d -> d.floatValue())
                    .toArray(Float[]::new);
        } catch (Exception e) {
            System.err.println("文本向量化失败: " + e.getMessage());
            return new Float[0];
        }
    }

    /**
     * 创建优化的Schema - 适合消费级电脑使用，从文件加载中文停用词
     */
    private void createOptimizedSchema() {
        try {

            Result<Boolean> deleteResult = client.schema().classDeleter()
                    .withClassName(CLASS_NAME)
                    .run();
            // 检查类是否已存在
            Result<Boolean> existsResult = client.schema().exists().withClassName(CLASS_NAME).run();
            if (existsResult.hasErrors()) {
                System.err.println("检查Schema失败: " + existsResult.getError());
                return;
            }

            if (existsResult.getResult()) {
                System.out.println("Schema已存在，跳过创建");
                return;
            }

            // 从文件加载停用词配置
            System.out.println("正在从文件加载停用词配置...");
            StopwordConfig stopwordConfig = StopWordsLoader.createStopwordConfig();
            
            // 显示停用词统计信息
            StopWordsLoader.StopWordsInfo stopWordsInfo = StopWordsLoader.getStopWordsInfo();
            System.out.println(stopWordsInfo);

            // 配置倒排索引，包含停用词设置
            InvertedIndexConfig invertedIndexConfig = InvertedIndexConfig.builder()
                    .stopwords(stopwordConfig)
                    .indexTimestamps(false) // 不索引时间戳以节省资源
                    .indexNullState(false)  // 不索引空值状态以节省资源
                    .indexPropertyLength(false) // 不索引属性长度以节省资源
                    .build();

            // 创建文档相关属性，优化索引配置
            List<Property> properties = Arrays.asList(
                    Property.builder()
                            .name(NODE_ID_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("节点ID")
                            .indexFilterable(true)  // 支持过滤查询
                            .indexSearchable(false) // 不需要全文搜索，节省资源
                            .build(),
                    Property.builder()
                            .name(SPACE_ID_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("空间ID")
                            .indexFilterable(true)
                            .indexSearchable(false)
                            .build(),
                    Property.builder()
                            .name(FILE_NAME_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("文件名")
                            .tokenization("gse") // 使用GSE中文分词
                            .indexFilterable(true)
                            .indexSearchable(true)  // 文件名需要搜索
                            .build(),
                    Property.builder()
                            .name(USER_ID_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("用户ID")
                            .indexFilterable(true)
                            .indexSearchable(false)
                            .build(),
                    Property.builder()
                            .name(MIME_TYPE_FIELD)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("文件类型")
                            .indexFilterable(true)
                            .indexSearchable(false)
                            .build(),
                    Property.builder()
                            .name(CREATED_TIME_FIELD)
                            .dataType(Arrays.asList(DataType.DATE))
                            .description("创建时间")
                            .indexFilterable(true)
                            .build(),
                    Property.builder()
                            .name(TITLE)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("文档标题")
                            .tokenization("gse") // 使用GSE中文分词
                            .indexSearchable(true)  // 标题需要全文搜索
                            .indexFilterable(true)
                            .build(),
                    Property.builder()
                            .name(CONTENT)
                            .dataType(Arrays.asList(DataType.TEXT))
                            .description("文档内容")
                            .tokenization("gse") // 使用GSE中文分词
                            .indexSearchable(true)  // 内容需要全文搜索
                            .indexFilterable(false) // 内容不需要精确过滤，节省资源
                            .build()
            );

            // 创建类，配置向量索引优化和停用词
            WeaviateClass weaviateClass = WeaviateClass.builder()
                    .className(CLASS_NAME)
                    .description("文档存储类 - 消费级电脑优化配置，含中文停用词")
                    .properties(properties)
                    .vectorizer("none") // 使用自定义向量
                    .vectorIndexType("hnsw") // 向量索引配置 - 优化内存使用
                    .invertedIndexConfig(invertedIndexConfig) // 配置停用词
                    .build();

            Result<Boolean> result = client.schema().classCreator()
                    .withClass(weaviateClass)
                    .run();

            if (result.hasErrors()) {
                System.err.println("创建Schema失败: " + result.getError());
            } else {
                System.out.println("Schema创建成功，已从文件加载停用词配置和优化设置");
                printSchemaOptimizationInfo();
            }
        } catch (Exception e) {
            System.err.println("创建Schema异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 打印Schema优化信息
     */
    private void printSchemaOptimizationInfo() {
        System.out.println("\n=== 消费级电脑优化配置说明 ===");
        System.out.println("1. 向量索引优化:");
        System.out.println("   - 使用HNSW向量索引，平衡性能和内存使用");
        System.out.println("   - 默认配置适合中小规模数据集");
        System.out.println("2. 属性索引优化:");
        System.out.println("   - 仅对必要字段启用indexSearchable");
        System.out.println("   - 内容字段不启用indexFilterable以节省存储");
        System.out.println("   - 使用GSE中文分词提高搜索质量");
        System.out.println("3. 停用词配置:");
        System.out.println("   - 使用none预设（Weaviate不支持zh预设）");
        System.out.println("   - 从文件加载自定义中文停用词: chinese_stopwords.txt");
        System.out.println("   - 支持分类管理和动态扩展");
        System.out.println("   - 自动优化BM25和混合搜索效果");
        System.out.println("4. 适合场景: 10万以下文档，8GB以上内存");
        System.out.println("5. 索引配置:");
        System.out.println("   - node_id, space_id, user_id, mime_type: 仅过滤索引");
        System.out.println("   - file_name: 过滤+搜索索引");
        System.out.println("   - title, content: 搜索索引+GSE分词+文件停用词过滤");
    }

    /**
     * 添加文档
     */
    public String addDocument(String nodeId, String spaceId, String fileName, String userId,
                              String mimeType, String title, String content) {
        try {
            // 生成向量
            Float[] vector = textToVector(content);
            if (vector.length == 0) {
                System.err.println("向量生成失败");
                return null;
            }

            // 创建文档对象
            Map<String, Object> properties = new HashMap<>();
            properties.put(NODE_ID_FIELD, nodeId);
            properties.put(SPACE_ID_FIELD, spaceId);
            properties.put(FILE_NAME_FIELD, fileName);
            properties.put(USER_ID_FIELD, userId);
            properties.put(MIME_TYPE_FIELD, mimeType);
            properties.put(CREATED_TIME_FIELD, Instant.now().toString());
            properties.put(TITLE, title);
            properties.put(CONTENT, content);

            // 使用基础API创建对象
            String uuid = UUID.randomUUID().toString();
            Result<WeaviateObject> result = client.data().creator()
                    .withClassName(CLASS_NAME)
                    .withID(uuid)
                    .withProperties(properties)
                    .withVector(vector)
                    .run();

            if (result.hasErrors()) {
                System.err.println("添加文档失败: " + result.getError());
                return null;
            }

            System.out.println("文档添加成功，ID: " + uuid);
            return uuid;
        } catch (Exception e) {
            System.err.println("添加文档异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 混合检索 + 节点ID过滤 - 核心方法（停用词由Weaviate内置处理）
     */
    public List<Map<String, Object>> hybridSearchWithNodeFilter(String queryText, String[] nodeIds, int limit) {
        try {
            System.out.println("混合检索查询: " + queryText);
            System.out.println("停用词处理: 由Weaviate内置功能自动处理");

            // 生成查询向量
            Float[] queryVector = textToVector(queryText);
            if (queryVector.length == 0) {
                System.err.println("查询向量生成失败");
                return Collections.emptyList();
            }

            // 构建混合检索参数 - Weaviate会自动处理停用词
            HybridArgument hybrid = HybridArgument.builder()
                    .query(queryText) // 使用原始查询，停用词由Weaviate处理
                    .vector(queryVector)
                    .alpha(0.7f) // 0.7表示更偏向向量搜索，0.3用于BM25关键词搜索
                    .build();

            // 构建节点ID过滤条件
            WhereFilter nodeFilter = WhereFilter.builder()
                    .path(NODE_ID_FIELD)
                    .operator(Operator.ContainsAny)
                    .valueText(nodeIds)
                    .build();

            WhereArgument whereArg = WhereArgument.builder()
                    .filter(nodeFilter)
                    .build();

            // 构建返回字段
            Field[] fields = {
                    Field.builder().name(NODE_ID_FIELD).build(),
                    Field.builder().name(SPACE_ID_FIELD).build(),
                    Field.builder().name(FILE_NAME_FIELD).build(),
                    Field.builder().name(USER_ID_FIELD).build(),
                    Field.builder().name(MIME_TYPE_FIELD).build(),
                    Field.builder().name(CREATED_TIME_FIELD).build(),
                    Field.builder().name(TITLE).build(),
                    Field.builder().name(CONTENT).build(),
                    Field.builder()
                            .name("_additional")
                            .fields(new Field[]{
                                    Field.builder().name("id").build(),
                                    Field.builder().name("score").build()
                            })
                            .build()
            };

            // 执行混合检索 + 过滤
            Result<GraphQLResponse> result = client.graphQL().get()
                    .withClassName(CLASS_NAME)
                    .withHybrid(hybrid)
                    .withWhere(whereArg)
                    .withLimit(limit)
                    .withFields(fields)
                    .run();

            if (result.hasErrors()) {
                System.err.println("混合检索+节点过滤失败: " + result.getError());
                return Collections.emptyList();
            }

            return parseGraphQLResponse(result.getResult(), "混合检索+节点过滤(Weaviate停用词)");
        } catch (Exception e) {
            System.err.println("混合检索+节点过滤异常: " + e.getMessage());
            e.printStackTrace();
            return Collections.emptyList();
        }
    }

    /**
     * BM25关键词搜索 + 节点ID过滤（停用词由Weaviate内置处理）
     */
    public List<Map<String, Object>> bm25SearchWithNodeFilter(String queryText, String[] nodeIds, int limit) {
        try {
            System.out.println("BM25搜索查询: " + queryText);
            System.out.println("停用词处理: 由Weaviate内置功能自动处理");

            // 构建BM25参数进行关键词搜索 - Weaviate会自动处理停用词
            Bm25Argument bm25 = Bm25Argument.builder()
                    .query(queryText) // 使用原始查询，停用词由Weaviate处理
                    .properties(new String[]{TITLE, CONTENT}) // 在标题和内容中搜索
                    .build();

            // 构建节点ID过滤条件
            WhereFilter nodeFilter = WhereFilter.builder()
                    .path(NODE_ID_FIELD)
                    .operator(Operator.ContainsAny)
                    .valueText(nodeIds)
                    .build();

            WhereArgument whereArg = WhereArgument.builder()
                    .filter(nodeFilter)
                    .build();

            // 构建返回字段
            Field[] fields = {
                    Field.builder().name(NODE_ID_FIELD).build(),
                    Field.builder().name(TITLE).build(),
                    Field.builder().name(CONTENT).build(),
                    Field.builder()
                            .name("_additional")
                            .fields(new Field[]{
                                    Field.builder().name("id").build(),
                                    Field.builder().name("score").build()
                            })
                            .build()
            };

            // 执行BM25搜索
            Result<GraphQLResponse> result = client.graphQL().get()
                    .withClassName(CLASS_NAME)
                    .withBm25(bm25)
                    .withWhere(whereArg)
                    .withLimit(limit)
                    .withFields(fields)
                    .run();

            if (result.hasErrors()) {
                System.err.println("BM25搜索失败: " + result.getError());
                return Collections.emptyList();
            }

            return parseGraphQLResponse(result.getResult(), "BM25搜索(Weaviate停用词)");
        } catch (Exception e) {
            System.err.println("BM25搜索异常: " + e.getMessage());
            e.printStackTrace();
            return Collections.emptyList();
        }
    }

    /**
     * 对比测试：混合检索 vs 纯BM25检索 - 使用Weaviate内置停用词处理
     */
    public void compareSearchMethods(String queryText, String[] nodeIds, int limit) {
        System.out.println("\n=== 搜索方法对比测试（Weaviate内置停用词）===");
        System.out.println("查询文本: " + queryText);
        System.out.println("停用词处理: 由Weaviate自动处理");

        // 1. 纯BM25检索
        System.out.println("\n--- 纯BM25检索结果 ---");
        List<Map<String, Object>> bm25Results = bm25SearchWithNodeFilter(queryText, nodeIds, limit);
        for (Map<String, Object> doc : bm25Results) {
            System.out.println("BM25结果: " + doc.get(TITLE) + " - 评分: " + doc.get("score"));
        }

        // 2. 混合检索
        System.out.println("\n--- 混合检索结果 ---");
        List<Map<String, Object>> hybridResults = hybridSearchWithNodeFilter(queryText, nodeIds, limit);
        for (Map<String, Object> doc : hybridResults) {
            System.out.println("混合检索结果: " + doc.get(TITLE) + " - 评分: " + doc.get("score"));
        }

        // 3. 结果对比
        System.out.println("\n--- 结果对比分析 ---");
        System.out.println("BM25检索找到 " + bm25Results.size() + " 个结果");
        System.out.println("混合检索找到 " + hybridResults.size() + " 个结果");

        // 分析重叠结果
        Set<String> bm25Ids = new HashSet<>();
        Set<String> hybridIds = new HashSet<>();

        for (Map<String, Object> doc : bm25Results) {
            bm25Ids.add((String) doc.get("id"));
        }

        for (Map<String, Object> doc : hybridResults) {
            hybridIds.add((String) doc.get("id"));
        }

        Set<String> intersection = new HashSet<>(bm25Ids);
        intersection.retainAll(hybridIds);

        System.out.println("重叠结果数量: " + intersection.size());
        System.out.println("BM25独有结果: " + (bm25Ids.size() - intersection.size()));
        System.out.println("混合检索独有结果: " + (hybridIds.size() - intersection.size()));
        System.out.println("停用词处理: Weaviate内置功能自动优化搜索质量");
    }

    /**
     * 解析GraphQL响应
     */
    private List<Map<String, Object>> parseGraphQLResponse(GraphQLResponse response, String operationType) {
        List<Map<String, Object>> results = new ArrayList<>();

        if (response != null && response.getData() != null) {
            Map<String, Object> data = (Map<String, Object>) response.getData();
            Map<String, Object> getClass = (Map<String, Object>) data.get("Get");

            if (getClass != null) {
                List<Map<String, Object>> documents = (List<Map<String, Object>>) getClass.get(CLASS_NAME);

                if (documents != null) {
                    for (Map<String, Object> doc : documents) {
                        Map<String, Object> resultDoc = new HashMap<>(doc);
                        Map<String, Object> additional = (Map<String, Object>) doc.get("_additional");
                        if (additional != null) {
                            resultDoc.put("id", additional.get("id"));
                            if (additional.get("score") != null) {
                                resultDoc.put("score", additional.get("score"));
                            }
                        }
                        results.add(resultDoc);
                    }
                }
            }
        }

        System.out.println(operationType + "找到 " + results.size() + " 个相关文档");
        return results;
    }

    /**
     * 关闭连接
     */
    public void close() {
        if (client != null) {
            System.out.println("Weaviate连接已关闭");
        }
    }

    /**
     * 示例用法
     */
    public static void main(String[] args) {
        WeaviateExample example = new WeaviateExample();

        try {
            // 初始化
            example.initialize();
            
            // 验证Weaviate停用词功能
            example.testWeaviateStopWordsFunction();

            // 添加中文测试文档
            String id1 = example.addDocument(
                    "node_001",
                    "space_tech",
                    "java_guide.pdf",
                    "user_123",
                    "application/pdf",
                    "Java编程语言指南",
                    "Java是一种面向对象的编程语言，具有跨平台特性。它广泛应用于企业级开发，包括Web应用、移动应用和大数据处理。Java的核心特点是一次编写，到处运行。"
            );

            String id2 = example.addDocument(
                    "node_002",
                    "space_tech",
                    "python_analysis.docx",
                    "user_456",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "Python数据分析技术",
                    "Python在数据科学领域应用广泛，有丰富的库支持。主要包括pandas用于数据处理，numpy用于数值计算，matplotlib用于数据可视化，scikit-learn用于机器学习。"
            );

            String id3 = example.addDocument(
                    "node_003",
                    "space_management",
                    "project_management.txt",
                    "user_789",
                    "text/plain",
                    "敏捷项目管理方法",
                    "敏捷开发是现代软件项目管理的重要方法。它强调迭代开发、团队协作、客户反馈和适应变化。常见的敏捷方法包括Scrum、看板和极限编程。"
            );

            String id4 = example.addDocument(
                    "node_004",
                    "space_tech",
                    "ai_development.md",
                    "user_123",
                    "text/markdown",
                    "人工智能开发实践",
                    "人工智能开发需要掌握机器学习算法、深度学习框架和数据处理技术。主要工具包括TensorFlow、PyTorch、Keras等深度学习框架，以及OpenCV用于计算机视觉。"
            );

            String id5 = example.addDocument(
                    "node_005",
                    "space_tech",
                    "web_development.html",
                    "user_456",
                    "text/html",
                    "现代Web开发技术栈",
                    "现代Web开发涉及前端和后端技术。前端主要使用HTML、CSS、JavaScript，以及React、Vue等框架。后端可以使用Node.js、Spring Boot、Django等技术栈。"
            );

            // 定义测试节点
            String[] testNodeIds = {"node_001", "node_002", "node_003", "node_004", "node_005"};

            // 等待索引完成
            System.out.println("等待索引完成...");
            Thread.sleep(3000);

            // 对比测试不同查询 - 使用Weaviate内置停用词处理
            System.out.println("\n=== 开始搜索对比测试（Weaviate内置停用词）===");
            
            example.compareSearchMethods("我想要学习Python数据处理技术", testNodeIds, 3);
            example.compareSearchMethods("这个机器学习算法是什么样的", testNodeIds, 3);
            example.compareSearchMethods("如何进行Web前端开发工作", testNodeIds, 3);
            example.compareSearchMethods("我们应该怎么做巧克力制作过程", testNodeIds, 3);
            example.compareSearchMethods("java语言是世界上最好的语言吗", testNodeIds, 3);
            example.compareSearchMethods("我今天想要去吃饭了", testNodeIds, 3);
            example.compareSearchMethods("今天的天气怎么样呢", testNodeIds, 3);
            example.compareSearchMethods("吃香蕉有什么好处呢", testNodeIds, 3);

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            example.close();
        }
     }

    /**
     * Weaviate停用词功能验证测试（文件加载版本）
     */
    public void testWeaviateStopWordsFunction() {
        System.out.println("\n=== Weaviate停用词功能验证测试（文件加载）===");
        System.out.println("停用词配置: 使用none预设 + 文件加载自定义中文词汇");
        System.out.println("处理方式: Weaviate内置倒排索引自动处理");
        System.out.println("停用词文件: chinese_stopwords.txt");
        System.out.println("说明: Weaviate不支持zh预设，使用none预设+自定义词汇实现中文停用词");
        
        // 显示停用词统计信息
        StopWordsLoader.StopWordsInfo info = StopWordsLoader.getStopWordsInfo();
        System.out.println("\n停用词统计:");
        System.out.println("总数量: " + info.getTotalCount() + " 个");
        System.out.println("基础停用词: " + info.getBasicWords() + " 个");
        System.out.println("代词: " + info.getPronouns() + " 个");
        System.out.println("助词和语气词: " + info.getParticles() + " 个");
        System.out.println("时间词: " + info.getTimeWords() + " 个");
        System.out.println("程度副词: " + info.getAdverbs() + " 个");
        System.out.println("连词: " + info.getConjunctions() + " 个");
        System.out.println("介词: " + info.getPrepositions() + " 个");
        System.out.println("量词: " + info.getQuantifiers() + " 个");
        System.out.println("网络用语: " + info.getInternetSlang() + " 个");
        System.out.println("其他: " + info.getOthers() + " 个");
        
        // 测试用例
        String[] testQueries = {
            "我想要学习Java编程语言",
            "这个Python数据分析的方法是什么",
            "如何进行Web前端开发",
            "人工智能的应用有哪些",
            "项目管理的重要性",
            "今天天气怎么样",
            "我们应该如何解决这个问题"
        };
        
        System.out.println("\n测试查询示例:");
        for (String query : testQueries) {
            System.out.println("- " + query + " (停用词将由Weaviate自动过滤)");
        }
        
        System.out.println("\n停用词处理说明:");
        System.out.println("1. 使用none预设（空预设）");
        System.out.println("2. 从文件加载自定义中文停用词");
        System.out.println("3. BM25和混合搜索时自动过滤");
        System.out.println("4. 提升搜索精度和相关性");
        System.out.println("5. 支持动态扩展停用词库");
        System.out.println("6. 解决方案: Weaviate不支持zh预设，使用none+自定义词汇");
    }
}
