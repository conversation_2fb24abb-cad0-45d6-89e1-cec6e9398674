# 企业级RBAC权限管理系统

## 📋 系统概述

本系统是基于Spring Boot 3 + Sa-Token + MyBatis Plus构建的企业级RBAC（基于角色的访问控制）权限管理系统，提供完整的用户、角色、权限管理功能，支持细粒度权限控制、操作审计、组织架构管理等企业级特性。

## 🎯 核心功能

### 1. 用户管理
- ✅ 用户的完整生命周期管理（创建、编辑、禁用、删除）
- ✅ 用户基本信息管理（用户名、昵称、邮箱、手机号等）
- ✅ 密码管理（重置密码、密码策略）
- ✅ 账户安全（账户锁定、登录失败次数限制）
- ✅ 用户角色分配
- ✅ 用户数据导入导出

### 2. 角色管理
- ✅ 角色的创建、编辑、删除
- ✅ 角色权限分配
- ✅ 角色复制功能
- ✅ 角色状态管理
- ✅ 角色用户统计

### 3. 权限管理
- ✅ 菜单权限（控制页面访问）
- ✅ 功能权限（控制按钮操作）
- ✅ API权限（控制接口访问）
- ✅ 数据权限（控制数据范围）
- ✅ 权限树形结构展示

### 4. 组织架构
- ✅ 部门树形结构管理
- ✅ 部门层级关系
- ✅ 部门用户管理
- ✅ 部门权限控制

### 5. 操作审计
- ✅ 完整的操作日志记录
- ✅ 用户行为追踪
- ✅ 权限变更审计
- ✅ 日志查询和导出
- ✅ 日志清理策略

### 6. 系统配置
- ✅ 系统参数配置
- ✅ 安全策略配置
- ✅ 界面个性化配置
- ✅ 配置缓存管理

## 🏗️ 系统架构

### 技术栈
- **后端框架**: Spring Boot 3.x
- **权限框架**: Sa-Token
- **ORM框架**: MyBatis Plus
- **数据库**: MySQL 8.0+
- **缓存**: Redis
- **前端**: Thymeleaf + Bootstrap 5 + JavaScript

### 项目结构
```
edgemind-cortex/
├── src/main/java/com/zibbava/edgemind/cortex/
│   ├── annotation/          # 自定义注解
│   │   └── OperationLog.java
│   ├── aspect/              # 切面
│   │   └── OperationLogAspect.java
│   ├── controller/          # 控制器
│   │   ├── UserManagementController.java
│   │   ├── RoleManagementController.java
│   │   ├── DepartmentController.java
│   │   ├── OperationLogController.java
│   │   └── SystemPageController.java
│   ├── dto/                 # 数据传输对象
│   │   ├── UserManagementDTO.java
│   │   └── RoleManagementDTO.java
│   ├── entity/              # 实体类
│   │   ├── User.java
│   │   ├── Role.java
│   │   ├── Permission.java
│   │   ├── Department.java
│   │   ├── OperationLog.java
│   │   ├── DataPermission.java
│   │   ├── UserSession.java
│   │   └── SystemConfig.java
│   ├── mapper/              # 数据访问层
│   │   ├── UserMapper.java
│   │   ├── RoleMapper.java
│   │   ├── PermissionMapper.java
│   │   ├── DepartmentMapper.java
│   │   ├── OperationLogMapper.java
│   │   ├── UserRoleMapper.java
│   │   └── RolePermissionMapper.java
│   └── service/             # 服务层
│       ├── UserManagementService.java
│       ├── RoleManagementService.java
│       ├── OperationLogService.java
│       ├── SystemConfigService.java
│       └── impl/            # 服务实现
├── src/main/resources/
│   ├── sql/                 # 数据库脚本
│   │   ├── rbac_enhancement.sql
│   │   └── rbac_init_data.sql
│   ├── static/js/system/    # 前端JavaScript
│   │   ├── user-management.js
│   │   ├── role-management.js
│   │   └── operation-log.js
│   └── templates/system/    # 页面模板
│       ├── user_management.html
│       ├── role_management.html
│       └── operation_log.html
```

## 🚀 快速开始

### 1. 环境要求
- JDK 17+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 2. 数据库初始化
```sql
-- 1. 执行数据库结构扩展
source edgemind-cortex/src/main/resources/sql/rbac_enhancement.sql;

-- 2. 执行初始化数据
source edgemind-cortex/src/main/resources/sql/rbac_init_data.sql;
```

### 3. 配置文件
更新 `application.properties` 中的数据库和Redis配置：
```properties
# 数据库配置
spring.datasource.url=********************************************************************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password

# Redis配置
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=your_redis_password
```

### 4. 启动应用
```bash
mvn spring-boot:run
```

### 5. 访问系统
- 系统地址: http://localhost:8080
- 默认管理员账号: admin / admin123

## 📖 使用指南

### 权限配置流程
1. **创建部门**: 在部门管理中创建组织架构
2. **创建角色**: 定义不同的角色类型
3. **分配权限**: 为角色分配相应的权限
4. **创建用户**: 创建用户并分配到相应部门
5. **分配角色**: 为用户分配角色

### 权限控制说明

#### 菜单权限
- 控制用户可以访问哪些页面
- 在侧边栏中使用 `th:if="${@stpUtil.hasPermission('menu:system:user')}"` 控制显示

#### 功能权限
- 控制用户可以执行哪些操作
- 在控制器中使用 `@SaCheckPermission("system:user:create")` 注解

#### 数据权限
- 控制用户可以查看哪些数据范围
- 支持：全部数据、本部门数据、本部门及子部门数据、仅本人数据、自定义数据

### 操作日志
系统自动记录所有用户操作，包括：
- 操作时间、用户、类型、模块
- 请求参数、响应结果
- IP地址、用户代理
- 执行时间、操作状态

## 🔧 开发指南

### 添加新权限
1. 在数据库中插入权限记录
2. 在页面中添加权限检查
3. 在控制器中添加权限注解

### 自定义操作日志
使用 `@OperationLog` 注解：
```java
@OperationLog(
    operationType = OperationType.CREATE,
    module = "用户管理",
    description = "创建用户"
)
public void createUser() {
    // 业务逻辑
}
```

### 权限检查
```java
// 检查单个权限
StpUtil.checkPermission("system:user:create");

// 检查多个权限（AND）
StpUtil.checkPermissionAnd("system:user:list", "system:user:detail");

// 检查多个权限（OR）
StpUtil.checkPermissionOr("system:user:create", "system:user:update");
```

## 🔒 安全特性

### 密码安全
- 密码MD5加密存储
- 密码长度限制
- 密码复杂度要求（可配置）

### 登录安全
- 登录失败次数限制
- 账户自动锁定
- 会话超时控制
- 并发会话限制

### 操作安全
- 完整的操作审计
- 敏感操作二次确认
- 权限变更记录

## 📊 系统监控

### 操作统计
- 总操作数统计
- 今日操作统计
- 操作成功率
- 活跃用户数

### 日志管理
- 日志查询和过滤
- 日志导出
- 自动清理过期日志

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件至技术支持团队

---

**注意**: 本系统为企业级应用，请在生产环境中注意数据安全和权限配置。
