/* 新建模态框样式 */
.node-type-selector .btn {
    padding: 0.5rem 1.5rem;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 120px;
}

.node-type-selector .btn.active {
    background-color: #cfe2ff;
    border-color: #0d6efd;
    color: #0d6efd;
    box-shadow: none;
}

.node-type-view {
    transition: all 0.3s ease;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#folderView, #fileView {
    width: 100%;
}

/* Dropzone 容器和外部边框样式 */
.dropzone-container {
    border-radius: 8px;
    background-color: #F8F9FA;
    padding: 5px;
    margin-top: 15px;
}

/* Dropzone 自定义样式 */
.dropzone {
    border: 1px dashed #dee2e6;
    border-radius: 8px;
    background-color: #ffffff;
    min-height: 100px;
    padding: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dropzone:hover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
}

.dropzone .dz-message {
    text-align: center;
    margin: 0.5em 0;
    color: #6c757d;
    font-size: 1rem;
}

/* 文件卡片整体布局 */
.dropzone .dz-preview {
    margin: 0;
    width: 100%;
    position: relative;
}

/* 文件卡片容器 */
.dropzone .file-card {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
    margin: 15px 0 0 0;
    width: 100%;
}

/* 去掉卡片阴影，简化设计 */
.dropzone .file-card:hover {
    border-color: #adb5bd;
}

/* 文件卡片头部 */
.dropzone .file-card-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    position: relative;
    background-color: #fafafa;
    border-bottom: 1px solid #f1f1f1;
}

/* 文件图标容器 */
.dropzone .file-icon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

/* 文件类型图标 */
.dropzone .file-icon i {
    font-size: 22px;
    color: #2d8cff;
}

/* 文件信息区 */
.dropzone .file-info {
    flex: 1;
    min-width: 0; /* 防止溢出 */
    padding: 0 8px;
}

/* 文件名 */
.dropzone .file-name {
    font-weight: 500;
    font-size: 0.95rem;
    color: #343a40;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 文件大小 */
.dropzone .file-size {
    font-size: 0.8rem;
    color: #6c757d;
}

/* 移除按钮 */
.dropzone .file-remove-btn {
    background: none;
    border: none;
    color: #adb5bd;
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    padding: 0;
}

.dropzone .file-remove-btn:hover {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.05);
}

.dropzone .file-remove-btn i {
    font-size: 16px;
}

/* 进度条和状态指示 */
.dropzone .dz-progress {
    height: 4px;
    background-color: #f0f0f0;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden;
    display: none; /* 默认隐藏 */
}

.dropzone .dz-upload {
    display: block;
    height: 100%;
    width: 0;
    background-color: #0d6efd;
    transition: width 0.3s ease;
}

/* 成功和错误标记 */
.dropzone .dz-success-mark,
.dropzone .dz-error-mark {
    display: none; /* 默认隐藏 */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

.dropzone .dz-success-mark i {
    color: #28a745;
    font-size: 2rem;
}

.dropzone .dz-error-mark i {
    color: #dc3545;
    font-size: 2rem;
}

/* 错误信息 */
.dropzone .dz-error-message {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff1f0;
    color: #dc3545;
    padding: 8px 10px;
    border-radius: 0 0 8px 8px;
    font-size: 0.85rem;
    margin-top: 1px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 错误状态下显示错误信息 */
.dropzone .dz-error .dz-error-message {
    display: block;
}

/* 隐藏原始移除链接 */
.dropzone .dz-remove {
    display: none;
}

/* 文件夹视图样式 */
#folderView .bg-light {
    transition: all 0.3s ease;
}

#folderView .bg-light:hover {
    background-color: #f0f7ff !important;
    border-color: #0d6efd;
}
