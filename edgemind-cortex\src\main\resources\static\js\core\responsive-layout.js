/**
 * 响应式布局处理 和 侧边栏切换
 */

const SIDEBAR_COLLAPSED_KEY = 'sidebarCollapsed';

function initialize() {
    const menuToggle = document.getElementById('menu-toggle'); // Mobile toggle
    const sidebarToggle = document.getElementById('sidebar-toggle'); // Desktop toggle
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    const appContainer = document.querySelector('.app-container');
    const toggleIcon = sidebarToggle?.querySelector('i');
    const toggleText = sidebarToggle?.querySelector('.toggle-text');

    if (!sidebar || !appContainer) {
        console.error("Sidebar or App Container not found!");
        return;
    }

    // 初始化时禁用过渡效果
    appContainer.classList.add('no-transition');

    // --- Helper Function --- 
    const setSidebarState = (isCollapsed) => {
        sidebar.classList.toggle('collapsed', isCollapsed);
        appContainer.classList.toggle('sidebar-collapsed', isCollapsed);
        if (toggleIcon) {
            toggleIcon.classList.toggle('bi-arrow-bar-left', !isCollapsed);
            toggleIcon.classList.toggle('bi-arrow-bar-right', isCollapsed);
        }
        if (toggleText) {
            toggleText.textContent = isCollapsed ? '展开' : '收起';
        }
        // Save state to localStorage
        try {
            localStorage.setItem(SIDEBAR_COLLAPSED_KEY, isCollapsed ? 'true' : 'false');
        } catch (e) {
            console.warn("Could not save sidebar state to localStorage:", e);
        }
    };

    // --- Load Initial State (Desktop) --- 
    if (window.innerWidth > 768) {
        let initialStateCollapsed = false;
        try {
            initialStateCollapsed = localStorage.getItem(SIDEBAR_COLLAPSED_KEY) === 'true';
        } catch (e) {
             console.warn("Could not read sidebar state from localStorage:", e);
        }
         if (initialStateCollapsed) {
             // 立即应用折叠状态，避免闪烁
             sidebar.classList.add('collapsed');
             appContainer.classList.add('sidebar-collapsed');
             if (toggleIcon) {
                 toggleIcon.classList.remove('bi-arrow-bar-left');
                 toggleIcon.classList.add('bi-arrow-bar-right');
             }
             if (toggleText) {
                 toggleText.textContent = '展开';
             }
         }
         
         // 移除初始折叠类，让JavaScript接管
         document.documentElement.classList.remove('sidebar-initially-collapsed');
         
         // 重置CSS变量，让正常的CSS类控制生效
         document.documentElement.style.removeProperty('--initial-sidebar-width');
         document.documentElement.style.removeProperty('--initial-main-margin');
         
         // 设置完初始状态后启用过渡效果
         setTimeout(() => {
             appContainer.classList.remove('no-transition');
         }, 50);
    }
    
    // --- Mobile Menu Toggle --- 
    if (menuToggle) {
        menuToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            sidebar.classList.toggle('show');
            if (sidebar.classList.contains('show')) {
                sidebar.classList.remove('collapsed');
                appContainer.classList.remove('sidebar-collapsed');
            } 
        });
    }
        
    // --- Click outside to close mobile menu --- 
    if (mainContent) {
        mainContent.addEventListener('click', function() {
            if (window.innerWidth <= 768 && sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
            }
        });
    }
    document.body.addEventListener('click', function(event) {
         if (window.innerWidth <= 768 && sidebar.classList.contains('show')) {
              if (!sidebar.contains(event.target) && !menuToggle?.contains(event.target)) {
                   sidebar.classList.remove('show');
              }
         }
    });

    // --- Desktop Sidebar Toggle --- 
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            const isCurrentlyCollapsed = sidebar.classList.contains('collapsed');
            setSidebarState(!isCurrentlyCollapsed);
        });
    } else {
        console.warn("Desktop sidebar toggle button (#sidebar-toggle) not found.");
    }
    
    // --- Window Resize Handling --- 
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768 && sidebar.classList.contains('show')) {
            sidebar.classList.remove('show');
        }
         if (window.innerWidth > 768) {
             let storedStateCollapsed = false;
             try { storedStateCollapsed = localStorage.getItem(SIDEBAR_COLLAPSED_KEY) === 'true'; } catch(e){}
             if (sidebar.classList.contains('collapsed') !== storedStateCollapsed) {
                  setSidebarState(storedStateCollapsed);
             }
         } else {
            sidebar.classList.remove('collapsed');
            appContainer.classList.remove('sidebar-collapsed');
         }
    });

}

// Export the initialize function
export { initialize };