package com.zibbava.edgemind.cortex.config;

import cn.hutool.system.SystemUtil;
import com.zibbava.edgemind.cortex.store.WeaviateEmbeddingStore;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.Map;

/**
 * 企业级向量数据库配置类
 * 使用 Weaviate 实现的高性能向量数据库，针对 RAG 场景优化。
 * 支持混合搜索（Dense + BM25 Sparse）功能，使用官方WeightedRanker获得最高精度。
 */
@Configuration
@Slf4j
public class VectorStoreConfig {

    // --- Weaviate 基础配置 ---
    @Value("${weaviate.host:localhost}")
    private String weaviateHost;
    @Value("${weaviate.port:8080}")
    private int weaviatePort;
    @Value("${weaviate.class.name:Document}")
    private String weaviateClassName;
    @Value("${weaviate.dimension:1024}")
    private Integer weaviateDimension;

    // --- 企业级性能配置 ---
    @Value("${weaviate.batch.size:100}")
    private Integer batchSize;
    @Value("${weaviate.connection.timeout:30000}")
    private Long connectionTimeout;
    @Value("${weaviate.retry.attempts:3}")
    private Integer retryAttempts;

    // --- 混合搜索配置 ---
    @Value("${weaviate.hybrid.enabled:false}")
    private Boolean enableHybridSearch;
    @Value("${weaviate.hybrid.alpha:0.7}")
    private Float alpha;
    @Value("${weaviate.hybrid.rank-strategy:WEIGHTED}")
    private String rankStrategyName;

    // --- 搜索优化配置 ---
    @Value("${weaviate.optimization.search.max-results:5}")
    private int searchMaxResults;
    @Value("${weaviate.optimization.search.min-score:0.7}")
    private double searchMinScore;

    /**
     * 创建企业级向量数据库 Bean
     * 使用 Weaviate 实现，支持现代化向量搜索和混合检索优化。
     * <p>
     * 🔧 生命周期管理：
     * - 配置 destroyMethod 确保 Spring 容器关闭时正确释放资源
     * - 支持优雅关闭，避免数据丢失和连接泄漏
     *
     * @return 向量数据库实例
     */
    @Bean
    @Primary
    public EmbeddingStore<TextSegment> embeddingStore() {
        if (SystemUtil.getOsInfo().isMac()) {
            return new InMemoryEmbeddingStore<>();
        }

//        log.info("🚀 初始化企业级 Weaviate 向量数据库");
//        log.info("📍 连接地址: {}:{}", weaviateHost, weaviatePort);
//        log.info("📦 类名称: {}", weaviateClassName);
//        log.info("🎯 向量维度: {}", weaviateDimension);
//        log.info("⚡ 批处理大小: {}", batchSize);
//        log.info("🔗 连接超时: {}ms", connectionTimeout);
//        log.info("🔄 重试次数: {}", retryAttempts);

        // 混合搜索配置
//        if (enableHybridSearch) {
//            log.info("🔀 混合搜索已启用 (官方WeightedRanker)");
//            log.info("📊 Alpha权重: {}", alpha);
//        } else {
//            log.info("📊 使用传统单向量搜索模式");
//        }

        // 解析排序策略
        WeaviateEmbeddingStore.HybridRankStrategy rankStrategy;
        try {
            rankStrategy = WeaviateEmbeddingStore.HybridRankStrategy.valueOf(rankStrategyName);
        } catch (IllegalArgumentException e) {
            log.warn("⚠️ 无效的排序策略: {}, 使用默认 WEIGHTED", rankStrategyName);
            rankStrategy = WeaviateEmbeddingStore.HybridRankStrategy.WEIGHTED;
        }

//        log.info("🎯 混合搜索策略: {} (最精准)", rankStrategy);
//        log.info("📊 搜索配置: maxResults={}, minScore={}",
//                searchMaxResults, searchMinScore);

        try {
            WeaviateEmbeddingStore store = WeaviateEmbeddingStore.builder()
                    .host(weaviateHost)
                    .port(weaviatePort)
                    .className(weaviateClassName)
                    .dimension(weaviateDimension)
                    .batchSize(batchSize)
                    .connectionTimeout(connectionTimeout)
                    .retryAttempts(retryAttempts)
                    .enableHybridSearch(enableHybridSearch)
                    .alpha(alpha)
                    .rankStrategy(rankStrategy)
                    .build();
                    
//            log.info("✅ 企业级 Weaviate 向量数据库初始化成功");

            // 输出详细状态信息
//            Map<String, Object> stats = store.getStats();
//            log.info("📦 类名: {}", stats.get("className"));
//            log.info("🎯 维度: {}", stats.get("dimension"));
//            log.info("🔌 连接状态: {}", stats.get("isConnected"));
//            log.info("📚 Schema就绪: {}", stats.get("isSchemaReady"));
//            log.info("📦 批处理配置: {}", stats.get("batchSize"));

            // 混合搜索状态
//            if (enableHybridSearch) {
//                log.info("🔀 混合搜索状态: {}", stats.get("enableHybridSearch"));
//                log.info("📊 Alpha权重: {}", stats.get("alpha"));
//                log.info("🔧 重排策略: {}", stats.get("rankStrategy"));
//            }

            return store;
        } catch (Exception e) {
            log.error("❌ 企业级 Weaviate 向量数据库初始化失败", e);
            throw new RuntimeException("无法初始化向量数据库", e);
        }
    }
}
