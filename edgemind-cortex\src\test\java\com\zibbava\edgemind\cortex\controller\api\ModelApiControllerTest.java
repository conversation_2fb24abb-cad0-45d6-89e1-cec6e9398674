package com.zibbava.edgemind.cortex.controller.api;

import com.zibbava.edgemind.cortex.base.BaseControllerTest;
import com.zibbava.edgemind.cortex.dto.ModelDownloadRequest;
import com.zibbava.edgemind.cortex.dto.SystemInfoResponse;
import com.zibbava.edgemind.cortex.service.ModelService;
import com.zibbava.edgemind.cortex.service.SystemInfoService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ModelApiController 单元测试
 * 测试模型API相关接口功能，包括模型管理、下载进度等
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
@DisplayName("模型API控制器测试")
class ModelApiControllerTest extends BaseControllerTest {

    @MockBean
    private ModelService modelService;

    @MockBean
    private SystemInfoService systemInfoService;

    private SystemInfoResponse mockSystemInfo;
    private List<Map<String, Object>> mockModels;
    private Map<String, Object> mockDownloadResult;
    private Map<String, Object> mockProgressInfo;

    @Override
    protected void setUp() {
        // 初始化系统信息
        mockSystemInfo = SystemInfoResponse.builder()
                .cpu(SystemInfoResponse.CpuInfo.builder()
                        .model("Intel Core i7-9700K")
                        .cores(8)
                        .threads(16)
                        .build())
                .memory(SystemInfoResponse.MemoryInfo.builder()
                        .total("16GB")
                        .available("8GB")
                        .totalBytes(16L * 1024 * 1024 * 1024)
                        .availableBytes(8L * 1024 * 1024 * 1024)
                        .build())
                .gpu(SystemInfoResponse.GpuInfo.builder()
                        .model("NVIDIA RTX 3080")
                        .memory("10GB")
                        .memoryBytes(10L * 1024 * 1024 * 1024)
                        .available(true)
                        .build())
                .storage(SystemInfoResponse.StorageInfo.builder()
                        .total("1TB")
                        .available("500GB")
                        .totalBytes(1024L * 1024 * 1024 * 1024)
                        .availableBytes(500L * 1024 * 1024 * 1024)
                        .build())
                .recommendedModelSize("7B")
                .build();

        // 初始化模型列表
        Map<String, Object> model1 = new HashMap<>();
        model1.put("name", "llama3.2");
        model1.put("size", "7B");
        model1.put("status", "available");

        Map<String, Object> model2 = new HashMap<>();
        model2.put("name", "qwen2.5");
        model2.put("size", "14B");
        model2.put("status", "installed");

        mockModels = Arrays.asList(model1, model2);

        // 初始化下载结果
        mockDownloadResult = new HashMap<>();
        mockDownloadResult.put("taskId", "task-123");
        mockDownloadResult.put("status", "started");
        mockDownloadResult.put("message", "下载任务已启动");

        // 初始化进度信息
        mockProgressInfo = new HashMap<>();
        mockProgressInfo.put("taskId", "task-123");
        mockProgressInfo.put("progress", 50);
        mockProgressInfo.put("status", "downloading");
        mockProgressInfo.put("downloadedSize", "2.5GB");
        mockProgressInfo.put("totalSize", "5GB");
    }

    @Test
    @DisplayName("获取系统硬件信息 - 成功")
    void getSystemInfo_Success() throws Exception {
        // Given
        when(systemInfoService.getSystemInfo()).thenReturn(mockSystemInfo);

        // When & Then
        mockMvc.perform(withAuth(get("/api/models/system-info")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.cpu.model").value("Intel Core i7-9700K"))
                .andExpect(jsonPath("$.data.memory.total").value("16GB"))
                .andExpect(jsonPath("$.data.gpu.model").value("NVIDIA RTX 3080"))
                .andExpect(jsonPath("$.data.storage.total").value("1TB"));

        verify(systemInfoService).getSystemInfo();
    }

    @Test
    @DisplayName("获取可用模型列表 - 成功")
    void getAvailableModels_Success() throws Exception {
        // Given
        when(modelService.getAvailableModels()).thenReturn(mockModels);

        // When & Then
        mockMvc.perform(withAuth(get("/api/models/available")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].name").value("llama3.2"))
                .andExpect(jsonPath("$.data[0].size").value("7B"))
                .andExpect(jsonPath("$.data[1].name").value("qwen2.5"))
                .andExpect(jsonPath("$.data[1].size").value("14B"));

        verify(modelService).getAvailableModels();
    }

    @Test
    @DisplayName("获取已安装模型列表 - 成功")
    void getInstalledModels_Success() throws Exception {
        // Given
        List<Map<String, Object>> installedModels = Arrays.asList(mockModels.get(1)); // 只有第二个模型已安装
        when(modelService.getInstalledModels()).thenReturn(installedModels);

        // When & Then
        mockMvc.perform(withAuth(get("/api/models/installed")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].name").value("qwen2.5"))
                .andExpect(jsonPath("$.data[0].status").value("installed"));

        verify(modelService).getInstalledModels();
    }

    @Test
    @DisplayName("下载模型 - 成功")
    void downloadModel_Success() throws Exception {
        // Given
        ModelDownloadRequest request = new ModelDownloadRequest();
        request.setModelName("llama3.2");
        request.setModelType("chat");

        when(modelService.startModelDownload(any(ModelDownloadRequest.class))).thenReturn(mockDownloadResult);

        // When & Then
        mockMvc.perform(withAuth(post("/api/models/download"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.taskId").value("task-123"))
                .andExpect(jsonPath("$.data.status").value("started"))
                .andExpect(jsonPath("$.data.message").value("下载任务已启动"));

        verify(modelService).startModelDownload(any(ModelDownloadRequest.class));
    }

    @Test
    @DisplayName("下载模型 - 参数验证失败")
    void downloadModel_ValidationFailed() throws Exception {
        // Given
        ModelDownloadRequest invalidRequest = new ModelDownloadRequest();
        // 不设置modelName，应该验证失败

        // When & Then
        mockMvc.perform(withAuth(post("/api/models/download"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());

        verifyNoInteractions(modelService);
    }

    @Test
    @DisplayName("获取模型下载进度 - 成功")
    void getDownloadProgress_Success() throws Exception {
        // Given
        when(modelService.getDownloadProgress("task-123")).thenReturn(mockProgressInfo);

        // When & Then
        mockMvc.perform(withAuth(get("/api/models/download/progress/task-123")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.taskId").value("task-123"))
                .andExpect(jsonPath("$.data.progress").value(50))
                .andExpect(jsonPath("$.data.status").value("downloading"))
                .andExpect(jsonPath("$.data.downloadedSize").value("2.5GB"))
                .andExpect(jsonPath("$.data.totalSize").value("5GB"));

        verify(modelService).getDownloadProgress("task-123");
    }

    @Test
    @DisplayName("获取模型下载进度 - 任务不存在")
    void getDownloadProgress_TaskNotFound() throws Exception {
        // Given
        when(modelService.getDownloadProgress("invalid-task")).thenReturn(null);

        // When & Then
        mockMvc.perform(withAuth(get("/api/models/download/progress/invalid-task")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isEmpty());

        verify(modelService).getDownloadProgress("invalid-task");
    }

    @Test
    @DisplayName("删除模型 - 成功")
    void deleteModel_Success() throws Exception {
        // Given
        Map<String, String> request = new HashMap<>();
        request.put("modelName", "llama3.2");

        when(modelService.deleteModel("llama3.2")).thenReturn(true);

        // When & Then
        mockMvc.perform(withAuth(post("/api/models/delete"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));

        verify(modelService).deleteModel("llama3.2");
    }

    @Test
    @DisplayName("删除模型 - 删除失败")
    void deleteModel_Failed() throws Exception {
        // Given
        Map<String, String> request = new HashMap<>();
        request.put("modelName", "nonexistent-model");

        when(modelService.deleteModel("nonexistent-model")).thenReturn(false);

        // When & Then
        mockMvc.perform(withAuth(post("/api/models/delete"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(false));

        verify(modelService).deleteModel("nonexistent-model");
    }

    @Test
    @DisplayName("删除模型 - 缺少模型名称")
    void deleteModel_MissingModelName() throws Exception {
        // Given
        Map<String, String> request = new HashMap<>();
        // 不设置modelName

        // When & Then
        mockMvc.perform(withAuth(post("/api/models/delete"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(false));

        verify(modelService).deleteModel(null);
    }

    @Test
    @DisplayName("获取系统硬件信息 - 服务异常")
    void getSystemInfo_ServiceException() throws Exception {
        // Given
        when(systemInfoService.getSystemInfo()).thenThrow(new RuntimeException("系统信息获取失败"));

        // When & Then
        mockMvc.perform(withAuth(get("/api/models/system-info")))
                .andExpect(status().isInternalServerError());

        verify(systemInfoService).getSystemInfo();
    }

    @Test
    @DisplayName("获取可用模型列表 - 服务异常")
    void getAvailableModels_ServiceException() throws Exception {
        // Given
        when(modelService.getAvailableModels()).thenThrow(new RuntimeException("模型列表获取失败"));

        // When & Then
        mockMvc.perform(withAuth(get("/api/models/available")))
                .andExpect(status().isInternalServerError());

        verify(modelService).getAvailableModels();
    }
} 