package com.zibbava.edgemind.server.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 下载统计DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class DownloadStatisticsDTO {

    /**
     * 总下载次数
     */
    private Long totalDownloads;

    /**
     * 今日下载次数
     */
    private Long todayDownloads;

    /**
     * 本月下载次数
     */
    private Long thisMonthDownloads;

    /**
     * 独立IP数量
     */
    private Long uniqueIpCount;

    /**
     * 最近7天下载统计
     */
    private List<Map<String, Object>> recentSevenDays;

    /**
     * 最近30天下载统计
     */
    private List<Map<String, Object>> recentThirtyDays;

    /**
     * 今日下载增长率（相比昨日）
     */
    private Double todayGrowthRate;

    /**
     * 本月下载增长率（相比上月）
     */
    private Double monthGrowthRate;
}