package com.zibbava.edgemind.cortex.config;

/**
 * 应用环境配置中心
 * 统一管理开发模式、基础路径等全局配置
 */
public class EnvironmentConfig {

    /**
     * 是否为开发环境
     * <p>
     * 通过JVM启动参数 {@code -Dedgemind.dev.mode=true} 进行设置。
     * 程序将根据此标志调整其行为，例如跳过某些生产环境检查。
     */
    public static final boolean IS_DEVELOPMENT = "true".equals(System.getProperty("edgemind.dev.mode"));

    /**
     * 开发环境下的基础路径
     * <p>
     * 注意：此路径仅在 {@code IS_DEVELOPMENT} 为 true 时在 {@code getBasePath()} 中生效。
     */
    public static final String DEV_BASE_PATH = "D:\\\\duanzhi-dev";

    /**
     * 获取当前环境的基础路径。
     * <p>
     * 这是决定应用资源（如离线安装包、数据文件等）存放位置的核心方法。
     *
     * @return 如果是开发环境 (IS_DEVELOPMENT is true)，返回预设的开发路径 (DEV_BASE_PATH)；
     *         否则，返回程序实际运行的当前目录 (user.dir)。
     */
    public static String getBasePath() {
        String basePath = IS_DEVELOPMENT ? DEV_BASE_PATH : System.getProperty("user.dir");
        if (IS_DEVELOPMENT) {
            System.out.println("--- DEV MODE ACTIVE: Base path set to " + basePath + " ---");
        }
        return basePath;
    }
} 