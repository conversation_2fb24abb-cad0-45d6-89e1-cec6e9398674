@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo WSL2 Easy Install Script
echo ========================================
echo.
echo This script will install WSL2 for you
echo.

REM Create log file
set LOG_FILE=%~dp0wsl2_install.log
echo WSL2 Install Log - %date% %time% > "%LOG_FILE%"
echo ======================================== >> "%LOG_FILE%"

REM Step 1: Check system and admin rights
echo Step 1: Check system...

REM First check if WSL2 is already installed and working
echo Checking if WSL2 is already installed...
wsl --version >nul 2>&1
if %errorLevel% equ 0 (
    echo.
    echo WSL2 is already installed and working!
    echo [%time%] WSL2 already installed, skipping installation >> "%LOG_FILE%"
    echo.
    echo Checking current WSL status:
    wsl --version
    echo.
    echo Installed distributions:
    wsl --list --verbose
    echo.
    echo WSL2 installation is complete. No further action needed.
    echo [%time%] WSL2 check completed - already installed >> "%LOG_FILE%"
    echo.
    pause
    exit /b 0
)

echo WSL2 not detected, proceeding with installation...
echo [%time%] WSL2 not detected, starting installation >> "%LOG_FILE%"
echo.

net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Need admin rights
    echo Please right-click and "Run as admin"
    echo.
    echo [%time%] ERROR: Need admin rights >> "%LOG_FILE%"
    pause
    exit /b 1
)

echo OK: Admin rights good
echo [%time%] OK: Admin rights confirmed >> "%LOG_FILE%"

REM Check Windows version
for /f "tokens=3" %%i in ('reg query "HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion" /v CurrentBuild 2^>nul') do set BUILD=%%i

if not defined BUILD (
    echo ERROR: Cannot check Windows version
    echo [%time%] ERROR: Cannot get Windows build >> "%LOG_FILE%"
    pause
    exit /b 1
)

echo Windows Build: %BUILD%
echo [%time%] Windows Build: %BUILD% >> "%LOG_FILE%"

if %BUILD% lss 19041 (
    echo ERROR: Need Windows 10 build 19041 or newer
    echo Your build: %BUILD%
    echo [%time%] ERROR: Old Windows version %BUILD% >> "%LOG_FILE%"
    pause
    exit /b 1
)

echo OK: Windows version good
echo [%time%] OK: Windows version good >> "%LOG_FILE%"

REM Check system architecture
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    echo OK: 64-bit system
    echo [%time%] OK: 64-bit system >> "%LOG_FILE%"
) else (
    echo ERROR: Need 64-bit system for WSL2
    echo [%time%] ERROR: Not 64-bit system >> "%LOG_FILE%"
    pause
    exit /b 1
)

REM Check memory
echo Checking system memory...
set TOTAL_MEM=
for /f "skip=1 tokens=2" %%i in ('wmic computersystem get TotalPhysicalMemory /format:value 2^>nul ^| findstr "TotalPhysicalMemory"') do (
    for /f "tokens=2 delims==" %%j in ("%%i") do set TOTAL_MEM=%%j
)

if not defined TOTAL_MEM (
    REM Try alternative method
    for /f "tokens=2 delims==" %%i in ('wmic computersystem get TotalPhysicalMemory /value 2^>nul ^| find "TotalPhysicalMemory"') do set TOTAL_MEM=%%i
)

if defined TOTAL_MEM (
    if "!TOTAL_MEM!"=="" (
        echo WARNING: Memory value is empty
        echo [%time%] WARNING: Memory value empty >> "%LOG_FILE%"
        set MEM_GB=8
        echo Assuming 8GB RAM for safety
    ) else (
        set /a MEM_GB=!TOTAL_MEM!/1073741824
        if !MEM_GB! equ 0 (
            set /a MEM_GB=!TOTAL_MEM!/1000000000
        )
    )
    echo RAM: !MEM_GB! GB
    echo [%time%] RAM: !MEM_GB! GB >> "%LOG_FILE%"
    if !MEM_GB! lss 4 (
        echo ERROR: Need at least 4GB RAM for WSL2
        echo [%time%] ERROR: Low memory !MEM_GB! GB >> "%LOG_FILE%"
        pause
        exit /b 1
    ) else (
        echo OK: Good memory
        echo [%time%] OK: Good memory >> "%LOG_FILE%"
    )
) else (
    echo WARNING: Cannot check memory, assuming sufficient
    echo [%time%] WARNING: Cannot check memory >> "%LOG_FILE%"
    set MEM_GB=8
)

REM Check Hyper-V compatibility
echo Checking Hyper-V support...
systeminfo | findstr /C:"Hyper-V Requirements" >nul 2>&1
if %errorLevel% equ 0 (
    systeminfo | findstr /C:"VM Monitor Mode Extensions: Yes" >nul 2>&1
    if !errorLevel! equ 0 (
        echo OK: Hyper-V supported
        echo [%time%] OK: Hyper-V supported >> "%LOG_FILE%"
    ) else (
        echo WARNING: Hyper-V may not work properly
        echo [%time%] WARNING: Hyper-V compatibility issue >> "%LOG_FILE%"
    )
) else (
    echo WARNING: Cannot check Hyper-V support
    echo [%time%] WARNING: Cannot check Hyper-V >> "%LOG_FILE%"
)

REM Check BIOS virtualization
echo Checking virtualization in BIOS...
wmic cpu get VirtualizationFirmwareEnabled /value 2>nul | findstr "True" >nul 2>&1
if %errorLevel% equ 0 (
    echo OK: Virtualization enabled in BIOS
    echo [%time%] OK: BIOS virtualization enabled >> "%LOG_FILE%"
) else (
    echo WARNING: Virtualization may be disabled in BIOS
    echo Please check BIOS settings
    echo [%time%] WARNING: BIOS virtualization issue >> "%LOG_FILE%"
)
echo.
echo Press any key to continue...
pause >nul
echo.

REM Step 2: Check and enable WSL features
echo Step 2: Check WSL features...

REM Check WSL feature
powershell -Command "Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Windows-Subsystem-Linux | Select-Object State" | findstr "Enabled" >nul 2>&1
if %errorLevel% equ 0 (
    echo OK: WSL feature is on
    echo [%time%] OK: WSL feature enabled >> "%LOG_FILE%"
    set WSL_ENABLED=1
) else (
    echo INFO: WSL feature is off
    echo [%time%] INFO: WSL feature not enabled >> "%LOG_FILE%"
    set WSL_ENABLED=0
)

REM Check Virtual Machine Platform
powershell -Command "Get-WindowsOptionalFeature -Online -FeatureName VirtualMachinePlatform | Select-Object State" | findstr "Enabled" >nul 2>&1
if %errorLevel% equ 0 (
    echo OK: VM Platform is on
    echo [%time%] OK: VM Platform enabled >> "%LOG_FILE%"
    set VM_ENABLED=1
) else (
    echo INFO: VM Platform is off
    echo [%time%] INFO: VM Platform not enabled >> "%LOG_FILE%"
    set VM_ENABLED=0
)

REM Enable features if needed
if "%WSL_ENABLED%"=="0" (
    echo.
    echo Need to turn on WSL feature
    set /p choice="Turn on WSL? (y/n): "
    if /i "!choice!"=="y" (
        echo Turning on WSL...
        echo [%time%] Enabling WSL feature >> "%LOG_FILE%"
        dism /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
        if !errorLevel! neq 0 (
            echo ERROR: Cannot turn on WSL
            echo [%time%] ERROR: Failed to enable WSL >> "%LOG_FILE%"
            pause
            exit /b 1
        )
        echo OK: WSL turned on
        echo [%time%] OK: WSL enabled >> "%LOG_FILE%"
        set REBOOT_REQUIRED=1
    ) else (
        echo Skip WSL setup
        echo [%time%] User skipped WSL >> "%LOG_FILE%"
        goto :end
    )
)

if "%VM_ENABLED%"=="0" (
    echo.
    echo Need to turn on VM Platform
    set /p choice="Turn on VM Platform? (y/n): "
    if /i "!choice!"=="y" (
        echo Turning on VM Platform...
        echo [%time%] Enabling VM Platform >> "%LOG_FILE%"
        dism /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
        if !errorLevel! neq 0 (
            echo ERROR: Cannot turn on VM Platform
            echo [%time%] ERROR: Failed to enable VM Platform >> "%LOG_FILE%"
            pause
            exit /b 1
        )
        echo OK: VM Platform turned on
        echo [%time%] OK: VM Platform enabled >> "%LOG_FILE%"
        set REBOOT_REQUIRED=1
    ) else (
        echo Skip VM Platform setup
        echo [%time%] User skipped VM Platform >> "%LOG_FILE%"
        goto :end
    )
)
echo.
echo Press any key to continue...
pause >nul
echo.

REM Check if restart is needed
if "%REBOOT_REQUIRED%"=="1" (
    echo.
    echo ========================================
    echo NEED RESTART
    echo ========================================
    echo.
    echo Windows features are on but need restart
    echo Run this script again after restart
    echo [%time%] Restart needed >> "%LOG_FILE%"
    echo.
    set /p choice="Restart now? (y/n): "
    if /i "!choice!"=="y" (
        echo Restart in 10 seconds...
        echo [%time%] User chose restart >> "%LOG_FILE%"
        timeout /t 10
        shutdown /r /t 0
        exit /b 0
    ) else (
        echo Please restart and run script again
        echo [%time%] User chose manual restart >> "%LOG_FILE%"
        goto :end
    )
)

REM Step 3: Install WSL2 kernel
echo Step 3: Install WSL2 kernel...
echo.
echo Do you want to install WSL2 kernel?
set /p choice="Install kernel? (y/n): "
if /i "!choice!"=="y" (
    echo Getting WSL2 kernel...
    echo [%time%] Starting kernel download >> "%LOG_FILE%"
    set KERNEL_URL=https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi
    set KERNEL_FILE=%TEMP%\wsl_update_x64.msi
    
    powershell -Command "try { Invoke-WebRequest -Uri '%KERNEL_URL%' -OutFile '%KERNEL_FILE%' -UseBasicParsing; exit 0 } catch { exit 1 }" 2>nul
    if !errorLevel! neq 0 (
        echo ERROR: Cannot get kernel
        echo Get it from: https://aka.ms/wsl2kernel
        echo [%time%] ERROR: Kernel download failed >> "%LOG_FILE%"
        echo.
        set /p choice="Continue without kernel? (y/n): "
        if /i "!choice!" neq "y" (
            echo Install stopped
            echo [%time%] Install stopped - no kernel >> "%LOG_FILE%"
            pause
            exit /b 1
        )
        goto :step4
    )
    
    echo Installing kernel...
    echo [%time%] Installing kernel >> "%LOG_FILE%"
    msiexec /i "%KERNEL_FILE%" /quiet /norestart
    if !errorLevel! neq 0 (
        echo ERROR: Cannot install kernel
        echo [%time%] ERROR: Kernel install failed >> "%LOG_FILE%"
        echo.
        set /p choice="Continue anyway? (y/n): "
        if /i "!choice!" neq "y" (
            echo Install stopped
            pause
            exit /b 1
        )
    ) else (
        echo OK: Kernel installed
        echo [%time%] OK: Kernel installed >> "%LOG_FILE%"
    )
    
    REM Clean up
    if exist "%KERNEL_FILE%" del "%KERNEL_FILE%" 2>nul
) else (
    echo Skip kernel install
    echo [%time%] User skipped kernel >> "%LOG_FILE%"
)

:step4
echo.
echo Press any key to continue...
pause >nul
echo.

REM Step 4: Set WSL2 default and configure memory
echo Step 4: Set WSL2 and configure memory...
echo.
echo Setting WSL2 as default...
echo [%time%] Setting WSL2 as default >> "%LOG_FILE%"
wsl --set-default-version 2
if !errorLevel! neq 0 (
    echo WARNING: Cannot set WSL2 default
    echo [%time%] WARNING: Cannot set WSL2 default >> "%LOG_FILE%"
) else (
    echo OK: WSL2 is default
    echo [%time%] OK: WSL2 set as default >> "%LOG_FILE%"
)

REM Configure WSL2 memory settings
echo.
echo Configuring WSL2 memory settings...
set WSLCONFIG_FILE=%USERPROFILE%\.wslconfig
echo [%time%] Creating .wslconfig file >> "%LOG_FILE%"

REM Calculate recommended memory based on system RAM
if defined TOTAL_MEM (
    if !MEM_GB! leq 8 (
        set RECOMMENDED_MEM=3
    ) else (
        set RECOMMENDED_MEM=4
    )
) else (
    set RECOMMENDED_MEM=3
)

echo Recommended WSL2 memory: !RECOMMENDED_MEM!GB
echo Do you want to set WSL2 memory limit?
echo This prevents WSL2 from using too much RAM
set /p choice="Set memory limit? (y/n): "
if /i "!choice!"=="y" (
    echo.
    echo Enter memory limit in GB (recommended: !RECOMMENDED_MEM!GB):
    set /p user_memory="Memory limit (GB): "
    if "!user_memory!"=="" set user_memory=!RECOMMENDED_MEM!
    
    echo Creating .wslconfig file...
    echo [wsl2] > "%WSLCONFIG_FILE%"
    echo memory=!user_memory!GB >> "%WSLCONFIG_FILE%"
    echo processors=2 >> "%WSLCONFIG_FILE%"
    echo swap=1GB >> "%WSLCONFIG_FILE%"
    echo localhostForwarding=true >> "%WSLCONFIG_FILE%"
    
    echo OK: WSL2 memory set to !user_memory!GB
    echo [%time%] OK: .wslconfig created with !user_memory!GB memory >> "%LOG_FILE%"
    echo.
    echo WSL2 configuration saved to: %WSLCONFIG_FILE%
) else (
    echo Skip memory configuration
    echo [%time%] User skipped memory config >> "%LOG_FILE%"
)

echo.
echo Press any key to continue...
pause >nul
echo.

REM Step 5: Install Ubuntu
echo Step 5: Install Ubuntu...
echo.
echo Do you want to install Ubuntu?
set /p choice="Install Ubuntu? (y/n): "
if /i "!choice!"=="y" (
    echo Installing Ubuntu...
    echo [%time%] Installing Ubuntu >> "%LOG_FILE%"
    
    REM Try winget first
    winget install Canonical.Ubuntu --accept-source-agreements --accept-package-agreements >nul 2>&1
    if !errorLevel! equ 0 (
        echo OK: Ubuntu installed
        echo [%time%] OK: Ubuntu installed via winget >> "%LOG_FILE%"
    ) else (
        echo Trying WSL install...
        wsl --install -d Ubuntu >nul 2>&1
        if !errorLevel! equ 0 (
            echo OK: Ubuntu installed
            echo [%time%] OK: Ubuntu installed via WSL >> "%LOG_FILE%"
        ) else (
            echo WARNING: Cannot install Ubuntu
            echo Get it from Microsoft Store
            echo [%time%] WARNING: Ubuntu install failed >> "%LOG_FILE%"
        )
    )
) else (
    echo Skip Ubuntu install
    echo [%time%] User skipped Ubuntu >> "%LOG_FILE%"
)

echo.
echo Press any key to continue...
pause >nul
echo.

REM Step 6: Final check and diagnostics
echo Step 6: Final check and diagnostics...
echo.
echo Checking WSL installation...
echo [%time%] Final verification >> "%LOG_FILE%"

REM Check WSL version
wsl --version >nul 2>&1
if %errorLevel% equ 0 (
    echo WSL version:
    wsl --version
    echo [%time%] WSL version OK >> "%LOG_FILE%"
) else (
    echo ERROR: WSL not working properly
    echo [%time%] ERROR: WSL version failed >> "%LOG_FILE%"
    echo.
    echo Possible issues:
    echo - WSL features not enabled
    echo - Need system restart
    echo - Windows version too old
)

echo.
REM Check WSL status
echo Checking WSL status...
wsl --status >nul 2>&1
if %errorLevel% equ 0 (
    echo WSL status:
    wsl --status
    echo [%time%] WSL status OK >> "%LOG_FILE%"
) else (
    echo WARNING: Cannot get WSL status
    echo [%time%] WARNING: WSL status failed >> "%LOG_FILE%"
)

echo.
REM Check installed distributions
echo Checking installed distributions...
wsl --list --verbose >nul 2>&1
if %errorLevel% equ 0 (
    wsl --list --verbose
    echo [%time%] Distribution list OK >> "%LOG_FILE%"
) else (
    echo No distributions found
    echo [%time%] No distributions found >> "%LOG_FILE%"
    echo.
    echo To install distributions:
    echo - Open Microsoft Store
    echo - Search for "Ubuntu" or "Debian"
    echo - Or use: wsl --install -d Ubuntu
)

echo.
REM Check .wslconfig file
echo Checking WSL configuration...
if exist "%USERPROFILE%\.wslconfig" (
    echo WSL config file found: %USERPROFILE%\.wslconfig
    echo Contents:
    type "%USERPROFILE%\.wslconfig"
    echo [%time%] .wslconfig file exists >> "%LOG_FILE%"
) else (
    echo No .wslconfig file found
    echo [%time%] No .wslconfig file >> "%LOG_FILE%"
)

echo.
REM Check Windows features
echo Checking Windows features...
powershell -Command "Get-WindowsOptionalFeature -Online -FeatureName Microsoft-Windows-Subsystem-Linux | Select-Object State" | findstr "Enabled" >nul 2>&1
if %errorLevel% equ 0 (
    echo OK: WSL feature enabled
) else (
    echo ERROR: WSL feature not enabled
)

powershell -Command "Get-WindowsOptionalFeature -Online -FeatureName VirtualMachinePlatform | Select-Object State" | findstr "Enabled" >nul 2>&1
if %errorLevel% equ 0 (
    echo OK: Virtual Machine Platform enabled
) else (
    echo ERROR: Virtual Machine Platform not enabled
)

:end
echo.
echo ============================================
echo WSL2 Setup Complete!
echo ============================================
echo.
echo Log saved to: %LOG_FILE%
echo.
echo To use WSL:
echo 1. Open Start Menu and find your Linux app
echo 2. Or type 'wsl' in Command Prompt
echo 3. Set up username and password
echo.
echo [%time%] Setup completed >> "%LOG_FILE%"

echo Press any key to exit...
pause >nul