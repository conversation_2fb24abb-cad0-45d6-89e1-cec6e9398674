package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.entity.DataPermission;

import java.util.List;

/**
 * 数据权限服务接口
 * 
 * <AUTHOR>
 */
public interface DataPermissionService extends IService<DataPermission> {

    /**
     * 根据权限ID查询数据权限配置
     * 
     * @param permissionId 权限ID
     * @return 数据权限列表
     */
    List<DataPermission> getDataPermissionsByPermissionId(Long permissionId);

    /**
     * 根据角色ID查询数据权限配置
     * 
     * @param roleId 角色ID
     * @return 数据权限列表
     */
    List<DataPermission> getDataPermissionsByRoleId(Long roleId);

    /**
     * 根据用户ID查询数据权限配置
     * 
     * @param userId 用户ID
     * @return 数据权限列表
     */
    List<DataPermission> getDataPermissionsByUserId(Long userId);

    /**
     * 为权限配置数据权限
     * 
     * @param permissionId 权限ID
     * @param dataScope 数据范围
     * @param deptIds 部门ID列表（当数据范围为自定义时使用）
     * @param filterSql 过滤SQL（当数据范围为自定义时使用）
     * @return 数据权限配置
     */
    DataPermission configureDataPermission(Long permissionId, DataPermission.DataScope dataScope, List<Long> deptIds, String filterSql);

    /**
     * 删除权限的数据权限配置
     * 
     * @param permissionId 权限ID
     */
    void removeDataPermissionByPermissionId(Long permissionId);

    /**
     * 根据用户ID和资源类型构建数据权限过滤SQL
     * 
     * @param userId 用户ID
     * @param resourceType 资源类型（如：user、dept等）
     * @return 过滤SQL
     */
    String buildDataPermissionSql(Long userId, String resourceType);

    /**
     * 获取用户可访问的部门ID列表
     * 
     * @param userId 用户ID
     * @return 部门ID列表
     */
    List<Long> getAccessibleDeptIds(Long userId);

    /**
     * 检查用户是否可以访问指定部门的数据
     * 
     * @param userId 用户ID
     * @param deptId 部门ID
     * @return 是否可以访问
     */
    boolean canAccessDeptData(Long userId, Long deptId);
}
