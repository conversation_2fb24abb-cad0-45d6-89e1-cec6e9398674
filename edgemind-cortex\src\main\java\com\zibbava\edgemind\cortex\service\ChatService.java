package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.entity.ChatConversation;
import com.zibbava.edgemind.cortex.entity.ChatMessage;
import reactor.core.publisher.SynchronousSink;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 聊天服务接口
 */
public interface ChatService {

    /**
     * 创建新会话
     *
     * @param userId 用户ID
     * @param title 会话标题
     * @return 新创建的会话
     */
    ChatConversation createConversation(Long userId, String title);

    /**
     * 获取用户的所有会话
     *
     * @param userId 用户ID
     * @return 会话列表
     */
    List<ChatConversation> getUserConversations(Long userId);

    /**
     * 获取会话详情
     *
     * @param conversationId 会话ID
     * @return 会话详情
     */
    ChatConversation getConversation(Long conversationId);

    /**
     * 获取会话的所有消息
     *
     * @param conversationId 会话ID
     * @return 消息列表
     */
    List<ChatMessage> getConversationMessages(Long conversationId);

    /**
     * 保存用户消息
     *
     * @param conversationId 会话ID
     * @param content 消息内容
     * @param imagePath 图片路径（可选）
     * @return 保存的消息
     */
    ChatMessage saveUserMessage(Long conversationId, String content, String imagePath);

    /**
     * 保存AI消息
     *
     * @param conversationId 会话ID
     * @param content 消息内容
     * @param thinkingContent 思考过程内容
     * @param tokenCount token数量
     * @param modelName 使用的模型名称
     * @return 保存的消息
     */
    ChatMessage saveAiMessage(Long conversationId, String content, String thinkingContent, Integer tokenCount, String modelName);

    /**
     * 更新会话标题
     *
     * @param conversationId 会话ID
     * @param title 新标题
     * @return 是否更新成功
     */
    boolean updateConversationTitle(Long conversationId, String title);

    /**
     * 删除会话（软删除）
     *
     * @param conversationId 会话ID
     * @return 是否删除成功
     */
    boolean deleteConversation(Long conversationId);

    /**
     * 获取用户的最近N条会话
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 会话列表
     */
    List<ChatConversation> getRecentUserConversations(Long userId, Integer limit);

    /**
     * 获取会话的最近N条消息
     *
     * @param conversationId 会话ID
     * @param limit 限制数量
     * @return 消息列表
     */
    List<ChatMessage> getRecentConversationMessages(Long conversationId, Integer limit);

    /**
     * 分页获取会话的消息
     *
     * @param conversationId 会话ID
     * @param page 页码（从0开始）
     * @param size 每页数量
     * @return 消息列表
     */
    List<ChatMessage> getConversationMessagesPaged(Long conversationId, Integer page, Integer size);

    /**
     * 获取会话的消息总数
     *
     * @param conversationId 会话ID
     * @return 消息总数
     */
    int getConversationMessagesCount(Long conversationId);

    /**
     * 获取用户的会话总数
     *
     * @param userId 用户ID
     * @return 会话总数
     */
    int getUserConversationsCount(Long userId);

    /**
     * 分页获取用户的会话
     *
     * @param userId 用户ID
     * @param page 页码（从0开始）
     * @param size 每页数量
     * @return 会话列表
     */
    List<ChatConversation> getUserConversationsPaged(Long userId, Integer page, Integer size);

    void handelSSE(StringBuilder mainContentBuilder, StringBuilder thinkingContentBuilder, String rawToken, SynchronousSink<String> sink, AtomicBoolean isInsideThinkingBlock);

    /**
     * 获取会话的所有消息（按时间顺序）
     * 用于会话记忆功能
     *
     * @param conversationId 会话ID
     * @return 按创建时间排序的消息列表
     */
    List<ChatMessage> getMessagesByConversationId(Long conversationId);

    /**
     * 删除会话的所有消息
     * 用于清除会话记忆
     *
     * @param conversationId 会话ID
     * @return 是否删除成功
     */
    boolean deleteMessagesByConversationId(Long conversationId);
}