package com.zibbava.edgemind.cortex.dto.knowledgebase;

import com.zibbava.edgemind.cortex.common.enums.NodeType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 创建知识库节点请求的数据传输对象 (DTO)。
 * 用于 API 请求体。
 */
@Data
public class NodeCreateRequest {

    /**
     * 节点所属的知识空间ID (必填)。
     */
    @NotBlank(message = "知识空间不能为空")
    private String spaceId;

    /**
     * 父节点 ID。
     * 如果为根节点（直接在知识空间下创建），则此字段为 null 或空字符串。
     */
    private String parentNodeId;

    /**
     * 新节点的名称 (必填，有长度限制)。 文档要带有扩展名
     * 只允许字母、数字、中文、下划线、横线、空格、小数点和圆括号（包括中文圆括号）。
     */
    @NotBlank(message = "不能为空")
    @Size(max = 200, message = "长度不能超过 200 个字符")
    private String name;

    /**
     * 新节点的类型 (必填)，FOLDER 或 FILE。
     */
    @NotNull(message = "节点类型不能为空")
    private NodeType type;
}