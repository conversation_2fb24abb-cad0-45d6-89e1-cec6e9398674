<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>许可证生成工具</title>
    <!-- 引入 Bootstrap 和其他通用样式 -->
    <link rel="stylesheet" href="/aistudio/css/vendor/bootstrap.min.css" />
    <link rel="stylesheet" href="/aistudio/css/vendor/bootstrap-icons.css" />
    <link rel="stylesheet" href="/aistudio/css/components/scrollbar-styles.css" />
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 20px;
        }
        .card-body {
            padding: 20px;
        }
        .form-label {
            font-weight: 500;
        }
        .result-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">许可证生成工具</h1>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">生成许可证</h5>
            </div>
            <div class="card-body">
                <form id="licenseForm">
                    <div class="mb-3">
                        <label for="secretKey" class="form-label">密钥</label>
                        <input type="password" class="form-control" id="secretKey" name="secretKey" placeholder="输入管理密钥" required>
                        <div class="form-text">请输入正确的管理密钥以生成许可证。</div>
                    </div>

                    <div class="mb-3">
                        <label for="hardwareFingerprint" class="form-label">硬件指纹</label>
                        <input type="text" class="form-control" id="hardwareFingerprint" placeholder="输入客户的硬件指纹" required>
                        <div class="form-text">客户可以在授权页面获取硬件指纹</div>
                    </div>

                    <div class="mb-3">
                        <label for="systemIdentifier" class="form-label">系统标识</label>
                        <input type="text" class="form-control" id="systemIdentifier" placeholder="输入客户的系统标识" required>
                        <div class="form-text">客户可以在授权页面获取系统标识</div>
                    </div>

                    <div class="mb-3">
                        <label for="licenseType" class="form-label">授权类型</label>
                        <select class="form-select" id="licenseType">
                            <option value="trial">试用版</option>
                            <option value="standard">标准版</option>
                            <option value="professional">专业版</option>
                            <option value="enterprise">企业版</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="expireType" class="form-label">过期类型</label>
                        <select class="form-select" id="expireType" onchange="toggleExpireDate()">
                            <option value="never">永不过期</option>
                            <option value="date">指定日期</option>
                        </select>
                    </div>

                    <div class="mb-3" id="expireDateGroup" style="display: none;">
                        <label for="expireDate" class="form-label">过期日期</label>
                        <input type="date" class="form-control" id="expireDate">
                    </div>

                    <button type="button" class="btn btn-primary" onclick="generateLicense()">生成许可证</button>
                </form>

                <div id="resultContainer" style="display: none;">
                    <div class="result-box" id="licenseResult"></div>
                    <div class="mt-3">
                        <button type="button" class="btn btn-outline-primary" onclick="copyLicense()">
                            <i class="bi bi-clipboard"></i> 复制许可证
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">使用说明</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li>输入客户提供的硬件指纹和系统标识</li>
                    <li>选择授权类型和过期时间</li>
                    <li>点击"生成许可证"按钮</li>
                    <li>复制生成的许可证，发送给客户</li>
                    <li>客户在授权页面输入许可证并激活</li>
                </ol>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    注意：生成的许可证只能在指定的硬件和系统上使用，请确保输入信息准确。
                </div>
            </div>
        </div>
    </div>

    <script src="/aistudio/js/features/admin/license-generator.js"></script>
</body>
</html>
