/**
 * 代码块处理脚本
 * 这个脚本不使用ES模块，确保在页面加载后立即执行
 */

// 将关键函数暴露为全局函数，以便其他模块调用
window.processAllCodeBlocks = processAllCodeBlocks;
window.highlightAllCodeBlocks = highlightAllCodeBlocks; // 新增：立即高亮所有代码块的函数

// 在页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {

    // 初始化代码块处理
    initCodeBlockHandling();

    // 监听DOM变化，处理新添加的代码块
    observeContentChanges();
});

// 初始化代码块处理
function initCodeBlockHandling() {
    processAllCodeBlocks();
}

// 处理页面上所有代码块 (用于流式渲染完成后的即时处理)
function highlightAllCodeBlocks() {

    
    // 仅处理未被高亮过的代码块，减少不必要的DOM操作
    document.querySelectorAll('pre code:not(.hljs)').forEach(block => {

        try {
            // 高亮代码块
            if (typeof hljs !== 'undefined') {
                hljs.highlightElement(block);
            }
            
            // 确保代码块已被处理(添加复制按钮等)
            if (!block.classList.contains('processed')) {
                processCodeBlockUI(block);
            }
        } catch (e) {
            console.error("强制高亮代码块时出错:", e, block);
        }
    });
}

// 处理页面上所有代码块
function processAllCodeBlocks() {
    // 只处理未处理过的代码块，减少重复操作
    document.querySelectorAll('pre code:not(.processed)').forEach(block => {
        processCodeBlock(block);
    });
}

// 处理单个代码块
function processCodeBlock(block) {
    try {
        // 如果已经处理过，跳过
        if (block.classList.contains('processed')) {
            return;
        }

        // 标记为已处理
        block.classList.add('processed');
        
        // 获取代码语言
        let language = '';
        if (block.className) {
            const langMatch = block.className.match(/language-([\w-]+)/);
            if (langMatch && langMatch[1]) {
                language = langMatch[1];
            }
        }

        // 高亮代码 (如果还没高亮过)
        if (typeof hljs !== 'undefined' && !block.classList.contains('hljs')) {
            try {
                hljs.highlightElement(block);
            } catch (e) {
                console.error("使用 hljs.highlightElement 高亮失败:", e);
            }
        }

        // 添加UI元素 (复制按钮等)
        processCodeBlockUI(block);
    } catch (e) {
        console.error("处理代码块时出错:", e, block);
    }
}

// 处理代码块的UI元素 (分离逻辑以减少不必要的DOM操作)
function processCodeBlockUI(block) {
    try {
        // 获取代码语言
        let language = '';
        if (block.className) {
            const langMatch = block.className.match(/language-([\w-]+)/);
            if (langMatch && langMatch[1]) {
                language = langMatch[1];
            }
        }

        const pre = block.parentElement;
        if (pre && pre.tagName === 'PRE') {
            // 添加语言标签
            if (language && !pre.hasAttribute('data-language')) {
                pre.setAttribute('data-language', language);
            }

            // 添加复制按钮 (只在需要时添加)
            if (!pre.querySelector('.copy-btn')) {
                const copyBtn = document.createElement('button');
                copyBtn.classList.add('copy-btn');
                copyBtn.innerHTML = '<i class="bi bi-clipboard"></i> 复制';
                copyBtn.addEventListener('click', () => handleCodeCopy(block, copyBtn));
                pre.appendChild(copyBtn);
            }
        }
    } catch (e) {
        console.error("处理代码块UI时出错:", e, block);
    }
}

// 处理代码复制功能
function handleCodeCopy(codeBlock, button) {
    const code = codeBlock.textContent;
    navigator.clipboard.writeText(code)
        .then(() => showCopySuccess(button))
        .catch(err => {
            console.error('剪贴板API复制失败:', err);
            fallbackCopy(code, button); // 尝试旧方法
        });
}

// 显示复制成功的反馈
function showCopySuccess(button) {
    if (button._copyTimeout) clearTimeout(button._copyTimeout); // 清除之前的定时器
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="bi bi-check-lg"></i> 已复制';
    button.classList.add('copied', 'btn-success');
    button.classList.remove('btn-outline-secondary');
    button.disabled = true;

    button._copyTimeout = setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('copied', 'btn-success');
        button.classList.add('btn-outline-secondary');
        button.disabled = false;
        delete button._copyTimeout;
    }, 2000);
}

// 回退复制方法
function fallbackCopy(text, button) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed'; textArea.style.opacity = '0';
    document.body.appendChild(textArea);
    textArea.select();
    try {
        document.execCommand('copy');
        showCopySuccess(button);
    } catch (err) {
        console.error('回退复制方法失败:', err);
        button.innerHTML = '<i class="bi bi-x-lg"></i> 复制失败';
        button.classList.add('btn-danger');
        button.classList.remove('btn-outline-secondary');
        button.disabled = true;
        setTimeout(() => {
             button.innerHTML = '<i class="bi bi-clipboard"></i> 复制';
             button.classList.remove('btn-danger');
             button.classList.add('btn-outline-secondary');
             button.disabled = false;
        }, 2000);
    }
    document.body.removeChild(textArea);
}

// 监听DOM变化，处理新添加的代码块
function observeContentChanges() {
    // 创建一个观察器实例
    const observer = new MutationObserver(function(mutations) {
        let hasNewCodeBlocks = false;
        
        // 检查是否有新的代码块添加
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // 元素节点
                        // 检查添加的节点是否包含代码块
                        const codeBlocks = node.querySelectorAll ? node.querySelectorAll('pre code:not(.processed)') : [];
                        if (codeBlocks.length > 0) {
                            hasNewCodeBlocks = true;
                        }

                        // 如果节点本身是代码块
                        if (node.tagName === 'CODE' && node.parentElement && node.parentElement.tagName === 'PRE' && !node.classList.contains('processed')) {
                            hasNewCodeBlocks = true;
                        }
                    }
                });
            }
            // 检查属性变化，如果有代码块的内容变化，也要处理
            if (mutation.type === 'characterData' || mutation.type === 'attributes') {
                const target = mutation.target;
                if (target.nodeType === 1 && target.tagName === 'CODE' && target.parentElement && target.parentElement.tagName === 'PRE') {
                    hasNewCodeBlocks = true;
                }
                // 如果是文本节点，检查其父节点
                if (target.nodeType === 3 && target.parentNode && target.parentNode.tagName === 'CODE' &&
                    target.parentNode.parentNode && target.parentNode.parentNode.tagName === 'PRE') {
                    hasNewCodeBlocks = true;
                }
            }
        });

        // 如果有新的代码块，处理它们
        if (hasNewCodeBlocks) {

            // 在流式渲染过程中，只标记为已处理，不立即执行高亮
            document.querySelectorAll('pre code:not(.processed)').forEach(block => {
                block.classList.add('processed');
            });
        }
    });

    // 配置观察选项
    const config = {
        childList: true,
        subtree: true,
        characterData: true, // 监听文本变化
        attributes: true // 监听属性变化
    };

    // 开始观察整个文档
    observer.observe(document.body, config);

    // 减少检查频率，降低性能消耗
    setInterval(processAllCodeBlocks, 2000);
}

// 在窗口加载完成后再次处理所有代码块，确保不会遗漏
window.addEventListener('load', function() {
    setTimeout(highlightAllCodeBlocks, 500); // 使用新的函数替代，确保内容都已加载
});
