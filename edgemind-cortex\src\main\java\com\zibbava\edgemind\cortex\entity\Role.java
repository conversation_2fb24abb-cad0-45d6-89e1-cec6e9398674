package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

@Data
@TableName("sys_role")
public class Role {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("role_name")
    private String roleName; // 角色名称, e.g., "管理员"

    @TableField("role_code")
    private String roleCode; // 角色编码, e.g., "ROLE_ADMIN", 用于 Sa-Token

    @TableField("description")
    private String description; // 角色描述

    @TableField("status")
    private Integer status; // 状态 (0: 禁用, 1: 启用)

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    // 添加软删除标记字段
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 删除时间 (软删除时记录删除时间)
    @TableField("delete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deleteTime;

    // 删除操作人ID
    @TableField("delete_user_id")
    private Long deleteUserId;

    // ----- 关联关系 -----

    // 与用户是多对多 (通过 UserRole 关联)
    // 这里不直接映射 User 集合，避免循环依赖和复杂查询
    // 通常在 Service 层处理

    // 与权限是多对多 (通过 RolePermission 关联)
    @TableField(exist = false) // 表示数据库表中不存在该字段
    private Set<Permission> permissions; // 存储角色拥有的权限对象 (查询时填充)

    // 也可以只存储 permissionIds
    // @TableField(exist = false)
    // private Set<Long> permissionIds;

} 