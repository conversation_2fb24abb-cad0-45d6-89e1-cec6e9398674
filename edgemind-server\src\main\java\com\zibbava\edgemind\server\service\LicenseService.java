package com.zibbava.edgemind.server.service;


import com.zibbava.edgemind.server.dto.FreeTrialRequestDTO;
import com.zibbava.edgemind.server.dto.LicenseInfoDTO;

/**
 * 许可证服务接口
 */
public interface LicenseService {


    /**
     * 生成许可证（仅供管理员使用）
     *
     * @param licenseInfo 许可证信息
     * @return 生成的许可证密钥
     */
    String generateLicense(LicenseInfoDTO licenseInfo);

    /**
     * 生成免费试用许可证
     *
     * @param request 免费试用请求
     * @return 生成的许可证密钥，如果该硬件指纹已使用过免费额度则返回null
     */
    String generateFreeTrialLicense(FreeTrialRequestDTO request);
}
