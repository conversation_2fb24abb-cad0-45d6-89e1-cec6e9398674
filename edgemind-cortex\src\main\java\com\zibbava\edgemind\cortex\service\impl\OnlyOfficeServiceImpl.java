package com.zibbava.edgemind.cortex.service.impl;

import com.onlyoffice.model.documenteditor.Callback;
import com.onlyoffice.model.documenteditor.Config;
import com.onlyoffice.model.documenteditor.config.document.Type;
import com.onlyoffice.model.documenteditor.config.editorconfig.Mode;
import com.onlyoffice.service.documenteditor.callback.CallbackService;
import com.onlyoffice.service.documenteditor.config.ConfigService;
import com.zibbava.edgemind.cortex.common.enums.NodeType;
import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.entity.KnowledgeDocument;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.service.KnowledgeBaseService;
import com.zibbava.edgemind.cortex.service.OnlyOfficeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * OnlyOffice 服务实现类
 * 整合ConfigService和CallbackService，提供业务层面的接口
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OnlyOfficeServiceImpl implements OnlyOfficeService {

    private final KnowledgeBaseService knowledgeBaseService;
    private final ConfigService configService;
    private final CallbackService callbackService;

    @Override
    public Map<String, Object> getDocumentEditorConfig(String nodeId, Long userId, String userName) {
        log.info("生成节点 {} 的 ONLYOFFICE 配置", nodeId);

        // 1. 获取节点信息并检查权限和类型
        KnowledgeNode node = knowledgeBaseService.findNodeById(nodeId);
        if (node == null) {
            throw new BusinessException(ResultCode.RESOURCE_NOT_FOUND, "节点不存在: " + nodeId);
        }
        if (node.getType() != NodeType.FILE) {
            throw new BusinessException(ResultCode.NODE_TYPE_MISMATCH, "节点类型错误，无法为文件夹生成 ONLYOFFICE 配置");
        }
        knowledgeBaseService.checkAccess(node.getSpaceId(), userId); // 检查用户权限

        // 2. 获取关联的文档记录
        KnowledgeDocument document = knowledgeBaseService.findDocumentByNodeId(nodeId);
        String vectorStatus = "UNKNOWN"; // Default status
        if (document != null && document.getVectorStatus() != null) {
            vectorStatus = document.getVectorStatus().name();
        } else {
            log.warn("节点 {} (文件类型) 缺少关联的文档记录或文档状态未知。", nodeId);
            throw new BusinessException(ResultCode.RESOURCE_NOT_FOUND, "找不到节点的关联文档信息");
        }

        try {
            // 使用ConfigService创建配置
            Config onlyOfficeConfig = configService.createConfig(nodeId, Mode.VIEW, Type.DESKTOP);
            log.info("为节点 {} 生成 ONLYOFFICE 配置成功", nodeId);

            Map<String, Object> result = new HashMap<>();
            result.put("onlyOfficeConfig", onlyOfficeConfig);
            result.put("vectorStatus", vectorStatus);

            return result;
        } catch (Exception e) {
            log.error("生成ONLYOFFICE配置失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "生成文档编辑器配置失败: " + e.getMessage());
        }
    }

    @Override
    public boolean processEditCallback(String nodeId, Callback callback) {
        try {
            log.info("接收到节点 {} 的编辑回调", nodeId);

            // 使用CallbackService处理回调
            callbackService.processCallback(callback, nodeId);
            return true;
        } catch (Exception e) {
            log.error("处理编辑回调失败: {}", e.getMessage(), e);
            return false;
        }
    }
}