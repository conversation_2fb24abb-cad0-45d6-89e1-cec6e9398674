package com.zibbava.edgemind.cortex.controller.api;

import com.zibbava.edgemind.cortex.base.BaseControllerTest;
import com.zibbava.edgemind.cortex.dto.PersonalStoragePathDTO;
import com.zibbava.edgemind.cortex.dto.SystemSettingsDTO;
import com.zibbava.edgemind.cortex.service.SystemSettingsService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * SystemSettingsController 单元测试
 * 测试系统设置相关接口功能，包括设置获取、更新、同步等
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
@DisplayName("系统设置控制器测试")
class SystemSettingsControllerTest extends BaseControllerTest {

    @MockBean
    private SystemSettingsService systemSettingsService;

    @Test
    @DisplayName("获取系统设置 - 成功")
    void getSettings_Success() throws Exception {
        // Given
        Map<String, Object> syncStatus = new HashMap<>();
        syncStatus.put("syncStatus", 1);
        syncStatus.put("syncMessage", "同步成功");
        syncStatus.put("lastSyncTime", LocalDateTime.now());
        
        when(systemSettingsService.getSettingValue(anyString(), anyString())).thenReturn("1.0.0");
        when(systemSettingsService.getSystemIdentifier()).thenReturn("system-001");
        when(systemSettingsService.getKnowledgeStoragePath()).thenReturn("/data/knowledge");
        when(systemSettingsService.getPersonalKnowledgeStoragePath()).thenReturn("/data/personal");
        when(systemSettingsService.getKnowledgeSyncStatus()).thenReturn(syncStatus);

        // When & Then
        mockMvc.perform(withAuth(get("/api/settings")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.version").value("1.0.0"))
                .andExpect(jsonPath("$.data.systemId").value("system-001"))
                .andExpect(jsonPath("$.data.storagePath").value("/data/knowledge"));
        
        verify(systemSettingsService).getSystemIdentifier();
        verify(systemSettingsService).getKnowledgeStoragePath();
        verify(systemSettingsService).getPersonalKnowledgeStoragePath();
    }

    @Test
    @DisplayName("获取系统设置 - 服务异常")
    void getSettings_ServiceException() throws Exception {
        // Given
        when(systemSettingsService.getSystemIdentifier()).thenThrow(new RuntimeException("获取系统标识失败"));

        // When & Then
        mockMvc.perform(withAuth(get("/api/settings")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false));
        
        verify(systemSettingsService).getSystemIdentifier();
    }

    @Test
    @DisplayName("更新系统设置 - 成功")
    void updateSettings_Success() throws Exception {
        // Given
        SystemSettingsDTO settingsDTO = new SystemSettingsDTO();
        settingsDTO.setStoragePath("/new/data/knowledge");
        settingsDTO.setPersonalStoragePath("/new/data/personal");
        
        doNothing().when(systemSettingsService).updateKnowledgeStoragePath(anyString());

        // When & Then
        mockMvc.perform(withAuth(post("/api/settings")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(settingsDTO))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        verify(systemSettingsService).updateKnowledgeStoragePath("/new/data/knowledge");
    }

    @Test
    @DisplayName("更新个人存储路径 - 成功")
    void updatePersonalStoragePath_Success() throws Exception {
        // Given
        PersonalStoragePathDTO pathDTO = new PersonalStoragePathDTO();
        pathDTO.setPersonalStoragePath("/new/personal/path");
        
        doNothing().when(systemSettingsService).updatePersonalKnowledgeStoragePath(anyString());

        // When & Then
        mockMvc.perform(withAuth(post("/api/settings/personal")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(pathDTO))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        verify(systemSettingsService).updatePersonalKnowledgeStoragePath("/new/personal/path");
    }

    @Test
    @DisplayName("获取同步状态 - 成功")
    void getSyncStatus_Success() throws Exception {
        // Given
        Map<String, Object> syncStatus = new HashMap<>();
        syncStatus.put("syncStatus", 1);
        syncStatus.put("syncMessage", "同步完成");
        syncStatus.put("lastSyncTime", LocalDateTime.now());
        
        when(systemSettingsService.getKnowledgeSyncStatus()).thenReturn(syncStatus);

        // When & Then
        mockMvc.perform(withAuth(get("/api/settings/sync/status")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.syncMessage").value("同步完成"));
        
        verify(systemSettingsService).getKnowledgeSyncStatus();
    }

    @Test
    @DisplayName("同步知识库 - 成功")
    void syncKnowledgeBase_Success() throws Exception {
        // Given
        doNothing().when(systemSettingsService).syncKnowledgeBase();

        // When & Then
        mockMvc.perform(withAuth(post("/api/settings/sync")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
        
        verify(systemSettingsService).syncKnowledgeBase();
    }

    @Test
    @DisplayName("同步知识库 - 同步失败")
    void syncKnowledgeBase_Failed() throws Exception {
        // Given
        doThrow(new RuntimeException("同步服务不可用"))
                .when(systemSettingsService).syncKnowledgeBase();

        // When & Then
        mockMvc.perform(withAuth(post("/api/settings/sync")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false));
        
        verify(systemSettingsService).syncKnowledgeBase();
    }
}