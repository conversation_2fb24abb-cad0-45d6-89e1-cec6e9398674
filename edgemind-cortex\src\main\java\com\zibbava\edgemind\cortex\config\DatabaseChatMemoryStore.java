//package com.zibbava.edgemind.cortex.config;
//
//import com.zibbava.edgemind.cortex.service.ChatService;
//import dev.langchain4j.data.message.AiMessage;
//import dev.langchain4j.data.message.ChatMessage;
//import dev.langchain4j.data.message.UserMessage;
//import dev.langchain4j.store.memory.chat.ChatMemoryStore;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 基于数据库的聊天记忆存储实现
// * 将langchain4j的ChatMessage与系统内的ChatMessage进行映射
// */
//@Component
//@RequiredArgsConstructor
//@Slf4j
//public class DatabaseChatMemoryStore implements ChatMemoryStore {
//
//    public static final int DEFAULT_MAX_MESSAGES = 20;
//
//    private final ChatService chatService;
//
//    /**
//     * 获取指定会话的消息历史，最多返回20条最新消息
//     * @param memoryId 会话ID
//     * @return langchain4j格式的聊天消息列表
//     */
//    @Override
//    public List<ChatMessage> getMessages(Object memoryId) {
//        if (memoryId == null) {
//            return new ArrayList<>();
//        }
//
//        try {
//            Long conversationId = Long.valueOf(memoryId.toString());
//
//            // 仅获取最近20条消息
//            List<com.zibbava.edgemind.cortex.entity.ChatMessage> messages = chatService.getRecentConversationMessages(conversationId, DEFAULT_MAX_MESSAGES);
//
//            // 手动转换消息，不使用stream
//            List<ChatMessage> langChainMessages = new ArrayList<>();
//            for (com.zibbava.edgemind.cortex.entity.ChatMessage message : messages) {
//                langChainMessages.add(convertToLangChainMessage(message));
//            }
//
//            log.debug("会话 {} 加载了 {} 条记忆消息", conversationId, langChainMessages.size());
//            return langChainMessages;
//        } catch (NumberFormatException e) {
//            log.error("无效的会话ID格式: {}", memoryId, e);
//            return new ArrayList<>();
//        } catch (Exception e) {
//            log.error("获取会话消息失败: {}", memoryId, e);
//            return new ArrayList<>();
//        }
//    }
//
//    /**
//     * 更新指定会话的消息
//     * 将langchain4j消息列表中的最后一条消息保存到数据库
//     * 此方法会在AI服务生成回复后被调用
//     *
//     * @param memoryId 会话ID
//     * @param messages 消息列表，包含最新的用户消息和AI回复
//     */
//    @Override
//    public void updateMessages(Object memoryId, List<ChatMessage> messages) {
//        if (memoryId == null || messages == null || messages.isEmpty()) {
//            log.warn("尝试更新消息但memoryId或messages为空");
//            return;
//        }
//
//        try {
//            Long conversationId = Long.valueOf(memoryId.toString());
//
//            // 直接获取最后一条消息进行处理
//            ChatMessage lastMessage = messages.get(messages.size() - 1);
//
//            if (lastMessage instanceof UserMessage userMessage) {
//                // 保存用户消息
//                String userContent = userMessage.singleText();
//                chatService.saveUserMessage(
//                        conversationId,
//                        userContent,
//                        null // 没有图片路径
//                );
//                log.debug("已保存用户消息到会话 {}: {}", conversationId, userContent);
//            } else if (lastMessage instanceof AiMessage aiMessage) {
//                // 保存AI消息
//                String fullContent = aiMessage.text();
//
//                // 处理思考过程（切分<think></think>标签之间的内容）
//                String aiContent = fullContent;
//                String thinkingContent = null;
//
//                // 切分思考内容
//                int thinkStart = fullContent.indexOf("<think>");
//                int thinkEnd = fullContent.lastIndexOf("</think>");
//
//                if (thinkStart >= 0 && thinkEnd > thinkStart) {
//                    // 提取思考内容（不包含标签）
//                    thinkingContent = fullContent.substring(thinkStart + 7, thinkEnd);
//
//                    // 将主要内容与思考内容分开
//                    StringBuilder contentBuilder = new StringBuilder();
//                    contentBuilder.append(fullContent.substring(0, thinkStart));
//                    contentBuilder.append(fullContent.substring(thinkEnd + 8));
//                    aiContent = contentBuilder.toString();
//                }
//
//                // 保存到数据库
//                chatService.saveAiMessage(
//                        conversationId,
//                        aiContent,
//                        thinkingContent,
//                        0,  // tokenCount暂不计算
//                        "unknown" // 模型名称未知
//                );
//
//                if (thinkingContent != null) {
//                    log.debug("已保存AI消息到会话 {}, 包含思考内容", conversationId);
//                } else {
//                    log.debug("已保存AI消息到会话 {}", conversationId);
//                }
//            }
//
//            log.info("会话 {} 成功保存了最后一条消息", conversationId);
//
//        } catch (NumberFormatException e) {
//            log.error("无效的会话ID格式: {}", memoryId, e);
//        } catch (Exception e) {
//            log.error("保存会话消息失败: {}", memoryId, e);
//        }
//    }
//
//    /**
//     * 删除指定会话的所有消息
//     * @param memoryId 会话ID
//     */
//    @Override
//    public void deleteMessages(Object memoryId) {
//        if (memoryId == null) {
//            return;
//        }
//
//        try {
//            Long conversationId = Long.valueOf(memoryId.toString());
//            // 删除会话的所有消息
//            chatService.deleteMessagesByConversationId(conversationId);
//            log.info("已删除会话 {} 的所有消息", conversationId);
//        } catch (Exception e) {
//            log.error("删除会话消息失败: {}", memoryId, e);
//        }
//    }
//
//    /**
//     * 将系统内的ChatMessage转换为langchain4j的ChatMessage
//     */
//    private ChatMessage convertToLangChainMessage(com.zibbava.edgemind.cortex.entity.ChatMessage message) {
//        // 根据发送者类型确定是用户消息还是AI消息
//        if ("user".equals(message.getSender())) {
//            return UserMessage.from(message.getContent());
//        } else {
//            return AiMessage.from(message.getContent());
//        }
//    }
//}
