package com.zibbava.edgemind.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 许可证记录实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("license_record")
public class LicenseRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 许可证密钥
     */
    private String licenseKey;

    /**
     * 硬件指纹
     */
    private String hardwareFingerprint;

    /**
     * 系统标识符
     */
    private String systemIdentifier;

    /**
     * 许可证类型：1-免费试用，2-管理员生成
     */
    private Integer licenseType;

    /**
     * 状态：0-未激活，1-已激活，2-已过期，3-已禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 激活时间
     */
    private LocalDateTime activatedTime;

    /**
     * 过期时间，null表示永不过期
     */
    private LocalDateTime expireTime;

    /**
     * 生成者（管理员用户名或系统标识）
     */
    private String generatedBy;

    /**
     * 客户端信息（IP、用户代理等）
     */
    private String clientInfo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 许可证类型枚举
     */
    public enum LicenseType {
        FREE_TRIAL(1, "免费试用"),
        ADMIN_GENERATED(2, "管理员生成");

        private final Integer code;
        private final String description;

        LicenseType(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static LicenseType fromCode(Integer code) {
            for (LicenseType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 许可证状态枚举
     */
    public enum LicenseStatus {
        INACTIVE(0, "未激活"),
        ACTIVE(1, "已激活"),
        EXPIRED(2, "已过期"),
        DISABLED(3, "已禁用");

        private final Integer code;
        private final String description;

        LicenseStatus(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static LicenseStatus fromCode(Integer code) {
            for (LicenseStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return null;
        }
    }
}