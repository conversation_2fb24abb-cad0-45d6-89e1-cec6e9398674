spring.application.name=edgemind-cortex
server.port=8080


# ================= \u8FD0\u884C\u73AF\u5883\u914D\u7F6E =================
run.env=dev
onlyoffice.server-url=http://localhost:8055/

#run.env=demo
#onlyoffice.server-url=https://duanzhi.cc/onlyoffice/

# ================= AI \u6A21\u578B\u914D\u7F6E =================
ai.model.type=ollama
ai.model.name=qwen3:14b
ai.model.baseUrl=http://localhost:11434
ai.model.apiKey=your-api-key

# ================= \u8BB8\u53EF\u8BC1\u670D\u52A1\u5668\u914D\u7F6E =================
app.license.server.url=https://duanzhi.cc
app.version=1.0.0

# ================= Weaviate \u5411\u91CF\u6570\u636E\u5E93\u914D\u7F6E =================
# \u57FA\u7840\u8FDE\u63A5\u914D\u7F6E
weaviate.host=localhost
weaviate.port=8090
weaviate.class.name=Wgk_document_prod
weaviate.dimension=1024

# \u6027\u80FD\u4F18\u5316\u914D\u7F6E
weaviate.batch.size=100
weaviate.connection.timeout=30000
weaviate.retry.attempts=1

# ================= Weaviate \u6DF7\u5408\u641C\u7D22\u914D\u7F6E =================
# \u542F\u7528\u6DF7\u5408\u641C\u7D22\uFF08Dense + BM25 Sparse\uFF09
weaviate.hybrid.enabled=true
# \u5411\u91CF\u641C\u7D22\u6743\u91CD\uFF08\u8BED\u4E49\u641C\u7D22\uFF09- \u9488\u5BF9\u6D88\u8D39\u7EA7\u7535\u8111\u4F18\u5316
weaviate.hybrid.alpha=0.3
# \u6DF7\u5408\u641C\u7D22\u91CD\u6392\u7B56\u7565
weaviate.hybrid.rank-strategy=WEIGHTED

# ================= RAG \u641C\u7D22\u4F18\u5316\u914D\u7F6E =================
# \u641C\u7D22\u53C2\u6570
weaviate.optimization.search.max-results=10
# \u63D0\u9AD8\u6700\u5C0F\u5206\u6570\u9608\u503C\uFF0C\u8FC7\u6EE4\u4F4E\u8D28\u91CF\u5339\u914D
weaviate.optimization.search.min-score=0.6

# ================= \u6587\u4EF6\u4E0A\u4F20\u914D\u7F6E =================
spring.servlet.multipart.max-file-size=30MB
spring.servlet.multipart.max-request-size=30MB

# ================= \u670D\u52A1\u5668\u914D\u7F6E =================
server.tomcat.max-swallow-size=30MB
server.servlet.context-path=/wkg

# ================= \u5F00\u53D1\u5DE5\u5177\u914D\u7F6E =================
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.thymeleaf.cache=false
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.cache.period=0

# ================= \u6570\u636E\u5E93\u914D\u7F6E =================
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# \u6570\u636E\u5E93\u8FDE\u63A5URL (\u8BF7\u66FF\u6362 your_db_name)
spring.datasource.url=**********************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=ENC(ZsG+BLcamXysQdYt03YcTOUxP0AWcVqo)

jasypt.encryptor.algorithm=PBEWithMD5AndDES
jasypt.encryptor.password=0f7b0a5d-46bc-40fd-b8ed-3181d21d644f
jasypt.encryptor.iv-generator-classname=org.jasypt.iv.NoIvGenerator

# ================= Redis \u914D\u7F6E =================
spring.data.redis.host=127.0.0.1
spring.data.redis.port=6379
spring.data.redis.database=0
spring.data.redis.timeout=10000

# ================= Sa-Token \u914D\u7F6E =================
sa-token.token-name=satoken
sa-token.is-log=true
sa-token.token-style=uuid
sa-token.is-concurrent=true
sa-token.is-share=true
#sa-token.max-login-count=1

# ================= MyBatis Plus \u914D\u7F6E =================
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.zibbava.edgemind.cortex.entity


# ================= ONLYOFFICE \u914D\u7F6E =================
# Document Server \u5730\u5740
onlyoffice.api-js-url=${onlyoffice.server-url}web-apps/apps/api/documents/api.js
onlyoffice.security-key=wkg123456

# \u8BED\u8A00\u4E0E\u533A\u57DF\u914D\u7F6E
onlyoffice.language=zh-CN
onlyoffice.region=zh-CN

# \u7F16\u8F91\u5668\u6A21\u5F0F\u914D\u7F6E
onlyoffice.mode=view
onlyoffice.type=desktop
onlyoffice.co-editing-mode=fast
onlyoffice.co-editing-mode-changeable=false

# \u529F\u80FD\u914D\u7F6E
onlyoffice.show-comments=false
onlyoffice.show-chat=false
onlyoffice.compact-toolbar=true
onlyoffice.show-feedback-button=false
onlyoffice.show-help-button=false
onlyoffice.hide-toolbar=true
onlyoffice.hide-statusbar=true
onlyoffice.hide-right-menu=true

# ================= \u4F01\u4E1A\u7EA7\u5BF9\u8BDD\u7B56\u7565\u914D\u7F6E =================

# \u5BF9\u8BDD\u98CE\u683C\u914D\u7F6E
chat.style.professional=true
chat.style.friendly=true
chat.style.structured=true

# \u529F\u80FD\u5F00\u5173\u914D\u7F6E
chat.context.analysis=true
chat.thinking.enabled=true
chat.history.aware=true

# \u5BF9\u8BDD\u8D28\u91CF\u914D\u7F6E
chat.quality.accuracy-first=true
chat.quality.completeness-check=true
chat.quality.relevance-filter=true

# \u589E\u5F3A\u68C0\u7D22\u914D\u7F6E
# \u542F\u7528\u589E\u5F3A\u68C0\u7D22\u529F\u80FD\uFF08\u67E5\u8BE2\u6269\u5C55\uFF09
enhanced.retrieval.enabled=true
# \u5B50\u67E5\u8BE2\u6570\u91CF
enhanced.retrieval.sub-queries=3
# \u5047\u8BBE\u6587\u6863\u6570\u91CF
enhanced.retrieval.hypothetical-docs=2

# ================= \u5386\u53F2\u6D88\u606F\u5904\u7406\u914D\u7F6E =================
chat.history.max-messages=10

