/* 主应用样式 */
:root {
    /* 侧边栏变量 - 这些在主应用中也需要，用于计算主内容区域的布局 */
    --sidebar-width-expanded: 260px;
    --sidebar-width-collapsed: 68px;
    --sidebar-transition-duration: 0.3s;
    
    /* 颜色系统 - 浅蓝色+白色主题 */
    --primary-color: #4285f4;
    --primary-light: #e8f0fe;
    --primary-dark: #3367d6;
    --text-primary: #2c3e50;
    --text-secondary: #5f6368;
    --hover-bg: #f5f8ff;
    --active-bg: #e8f0fe;
    --border-color: #e6f0ff;
    --sidebar-bg: #ffffff;
    --sidebar-border: #e6f0ff;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    color: var(--text-primary);
}

/* 应用容器 - 只负责整体布局，不干涉侧边栏 */
.app-container {
    height: 100vh;
    width: 100%;
    overflow: hidden;
    display: flex;
    position: relative; /* 确保子元素相对于它定位 */
}

/* 主内容区域 - 完全独立于侧边栏 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0; /* 允许子元素正确收缩，防止溢出 */
    transition: margin-left var(--sidebar-transition-duration) ease;
    background-color: #ffffff;
    margin-left: var(--sidebar-width-expanded); /* 默认状态下为侧边栏腾出空间 */
}

/* 使用初始变量的情况 */
.app-container:not(.sidebar-collapsed) .main-content {
    margin-left: var(--initial-main-margin, var(--sidebar-width-expanded));
}

/* 初始加载时禁用过渡效果，避免闪烁 */
.app-container.no-transition .sidebar,
.app-container.no-transition .main-content {
    transition: none !important;
}

/* 当侧边栏折叠时，调整主内容区域的左边距 */
.app-container.sidebar-collapsed .main-content {
    margin-left: var(--sidebar-width-collapsed);
}

/* 通用图标样式 */
.bi {
    display: inline-block;
    font-size: inherit;
    font-style: normal;
    line-height: 1;
    text-align: center;
    vertical-align: -0.125em;
}

/* Bootstrap图标特定调整 */
button .bi {
    margin-right: 0.25rem;
}

/* 响应式布局 - 仅处理主内容区域 */
@media (max-width: 768px) {
    .app-container {
        position: relative;
    }

    /* 移动端主内容区域不受侧边栏影响 */
    .main-content {
        width: 100%;
        margin-left: 0 !important; /* 覆盖桌面端样式 */
    }

    .app-container.sidebar-collapsed .main-content {
        margin-left: 0 !important; /* 确保折叠时也无边距 */
    }

    /* 添加汉堡菜单按钮 */
    .menu-toggle {
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 1001;
        background-color: #ffffff;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        padding: 5px;
        cursor: pointer;
    }
    
    /* 菜单按钮的图标 */
    .menu-toggle .bi {
        font-size: 1.5rem;
    }
}

/* iframe样式 */
#contentFrame {
    flex: 1 1 auto;
    min-height: 0;
    z-index: 1 !important;
}

/* ---- z-index Overrides ---- */

/* 确保模态对话框显示在最上层 */
.modal-backdrop.show {
  z-index: 9999 !important;
}

.modal.show {
  z-index: 10000 !important;
}

/* 确保模态对话框内容不被遮挡 */
.modal-content {
  position: relative;
  z-index: 10001 !important;
}

/* 修复下拉菜单遮挡问题 */
.dropdown-menu {
  z-index: 1055 !important;
}