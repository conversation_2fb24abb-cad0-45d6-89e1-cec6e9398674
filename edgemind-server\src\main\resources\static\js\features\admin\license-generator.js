/**
 * license-generator.js - 许可证生成工具页面功能
 */
// 许可证生成工具初始化逻辑已移至main.html中的initializeLicenseGenerator函数
// 这样可以实现按需加载，避免页面加载时所有脚本都执行

// 切换过期日期输入框显示状态
function toggleExpireDate() {
    const expireType = document.getElementById('expireType').value;
    const expireDateGroup = document.getElementById('expireDateGroup');
    
    if (expireType === 'date') {
        expireDateGroup.style.display = 'block';
    } else {
        expireDateGroup.style.display = 'none';
    }
}

// 生成许可证
function generateLicense() {
    const secretKey = document.getElementById('secretKey').value;
    const hardwareFingerprint = document.getElementById('hardwareFingerprint').value.trim();
    const systemIdentifier = document.getElementById('systemIdentifier').value.trim();
    const licenseType = document.getElementById('licenseType').value;
    const expireType = document.getElementById('expireType').value;
    
    if (!secretKey) {
        alert('请输入密钥');
        return;
    }
    if (!hardwareFingerprint || !systemIdentifier) {
        alert('请输入硬件指纹和系统标识');
        return;
    }
    
    // 构建请求数据
    const requestData = {
        secretKey: secretKey,
        hardwareFingerprint: hardwareFingerprint,
        systemIdentifier: systemIdentifier,
        licenseType: licenseType
    };
    
    // 如果选择了指定日期过期
    if (expireType === 'date') {
        const expireDate = document.getElementById('expireDate').value;
        if (!expireDate) {
            alert('请选择过期日期');
            return;
        }
        
        // 设置过期时间为当天23:59:59
        const expireDateTime = new Date(expireDate);
        expireDateTime.setHours(23, 59, 59, 999);
        
        requestData.expireTime = expireDateTime.toISOString();
    }
    
    // 发送请求
    ApiUtils.post('/api/license/generate', requestData)
        .then(data => {
            if (data.code === 200 && data.data) {
                // 显示结果
                document.getElementById('licenseResult').textContent = data.data;
                document.getElementById('resultContainer').style.display = 'block';
                ApiUtils.showSuccess('许可证生成成功');
            } else {
                ApiUtils.showError('生成许可证失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('生成许可证失败:', error);
            ApiUtils.showError('生成许可证失败，请稍后再试');
        });
}

// 复制许可证
function copyLicense() {
    const licenseText = document.getElementById('licenseResult').textContent;
    
    // 创建临时文本区域
    const textarea = document.createElement('textarea');
    textarea.value = licenseText;
    textarea.style.position = 'fixed';
    document.body.appendChild(textarea);
    textarea.select();
    
    try {
        // 执行复制命令
        document.execCommand('copy');
        alert('许可证已复制到剪贴板');
    } catch (err) {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制');
    }
    
    // 删除临时文本区域
    document.body.removeChild(textarea);
}
