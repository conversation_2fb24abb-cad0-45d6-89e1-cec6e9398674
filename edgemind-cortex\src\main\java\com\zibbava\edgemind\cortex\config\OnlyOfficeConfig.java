package com.zibbava.edgemind.cortex.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * ONLYOFFICE 相关配置
 */
@Configuration
@ConfigurationProperties(prefix = "onlyoffice")
public class OnlyOfficeConfig {


    /**
     * ONLYOFFICE Document Server 地址
     * 默认为 http://localhost:8055/
     */
    private String serverUrl = "http://localhost:8055/";

    /**
     * ONLYOFFICE Document Server API URL
     * 默认为 http://localhost:8055/web-apps/apps/api/documents/api.js
     */
    private String apiJsUrl = serverUrl + "web-apps/apps/api/documents/api.js";

    /**
     * ONLYOFFICE 安全密钥
     * 用于文档操作的签名验证
     */
    private String securityKey = "";

    /**
     * 界面语言
     * 可选值：en, zh-CN, de, fr, es, pt, ru等
     * 默认为 zh-CN
     */
    private String language = "zh-CN";

    /**
     * 地区设置，影响日期和数字格式
     * 可选值：en-US, zh-CN, de-DE, fr-FR等
     * 默认为 zh-CN
     */
    private String region = "zh-CN";

    /**
     * 文档查看模式
     * 可选值：view, edit
     * 默认为 edit
     */
    private String mode = "view";

    /**
     * 界面类型
     * 可选值：desktop, mobile, embedded
     * 默认为 desktop
     */
    private String type = "desktop";

    /**
     * 共同编辑模式
     * 可选值：fast, strict
     * 默认为 fast
     */
    private String coEditingMode = "fast";

    /**
     * 是否允许修改共同编辑模式
     * 默认为 true
     */
    private boolean coEditingModeChangeable = true;

    /**
     * 是否显示评论
     * 默认为 true
     */
    private boolean showComments = true;

    /**
     * 是否显示聊天
     * 默认为 true
     */
    private boolean showChat = true;

    /**
     * 是否使用紧凑工具栏
     * 默认为 false
     */
    private boolean compactToolbar = false;

    /**
     * 是否显示反馈按钮
     * 默认为 false
     */
    private boolean showFeedbackButton = false;

    /**
     * 是否显示帮助按钮
     * 默认为 true
     */
    private boolean showHelpButton = true;

    /**
     * 是否隐藏工具栏
     * 默认为 true
     */
    private boolean hideToolbar = true;

    /**
     * 是否隐藏状态栏
     * 默认为 true
     */
    private boolean hideStatusbar = true;

    /**
     * 是否隐藏右侧菜单
     * 默认为 true
     */
    private boolean hideRightMenu = true;

    public String getApiJsUrl() {
        return apiJsUrl;
    }

    public void setApiJsUrl(String apiJsUrl) {
        this.apiJsUrl = apiJsUrl;
    }

    public String getServerUrl() {
        return serverUrl;
    }

    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public String getSecurityKey() {
        return securityKey;
    }

    public void setSecurityKey(String securityKey) {
        this.securityKey = securityKey;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCoEditingMode() {
        return coEditingMode;
    }

    public void setCoEditingMode(String coEditingMode) {
        this.coEditingMode = coEditingMode;
    }

    public boolean isCoEditingModeChangeable() {
        return coEditingModeChangeable;
    }

    public void setCoEditingModeChangeable(boolean coEditingModeChangeable) {
        this.coEditingModeChangeable = coEditingModeChangeable;
    }

    public boolean isShowComments() {
        return showComments;
    }

    public void setShowComments(boolean showComments) {
        this.showComments = showComments;
    }

    public boolean isShowChat() {
        return showChat;
    }

    public void setShowChat(boolean showChat) {
        this.showChat = showChat;
    }

    public boolean isCompactToolbar() {
        return compactToolbar;
    }

    public void setCompactToolbar(boolean compactToolbar) {
        this.compactToolbar = compactToolbar;
    }

    public boolean isShowFeedbackButton() {
        return showFeedbackButton;
    }

    public void setShowFeedbackButton(boolean showFeedbackButton) {
        this.showFeedbackButton = showFeedbackButton;
    }

    public boolean isShowHelpButton() {
        return showHelpButton;
    }

    public void setShowHelpButton(boolean showHelpButton) {
        this.showHelpButton = showHelpButton;
    }

    public boolean isHideToolbar() {
        return hideToolbar;
    }

    public void setHideToolbar(boolean hideToolbar) {
        this.hideToolbar = hideToolbar;
    }

    public boolean isHideStatusbar() {
        return hideStatusbar;
    }

    public void setHideStatusbar(boolean hideStatusbar) {
        this.hideStatusbar = hideStatusbar;
    }

    public boolean isHideRightMenu() {
        return hideRightMenu;
    }

    public void setHideRightMenu(boolean hideRightMenu) {
        this.hideRightMenu = hideRightMenu;
    }
} 