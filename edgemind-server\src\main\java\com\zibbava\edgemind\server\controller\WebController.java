package com.zibbava.edgemind.server.controller;


import com.zibbava.edgemind.server.service.DownloadStatisticsService;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import jakarta.servlet.http.HttpServletRequest;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Controller
@RequiredArgsConstructor
public class WebController {

    private final DownloadStatisticsService downloadStatisticsService;

    // --- 官网页面路由 ---
    @GetMapping("/")
    public String website(Model model) {
        return "website"; // 返回官网页面
    }

    // --- 文件下载接口 ---
    @GetMapping("/download")
    public ResponseEntity<Resource> downloadFile(HttpServletRequest request) {
        String fileName = "duanzhi.zip";
        Integer downloadStatus = 1; // 1-成功，0-失败
        
        try {
            // Linux文件路径
            Path filePath = Paths.get("/usr/data/duanzhi.zip");
            File file = filePath.toFile();
            
            // 检查文件是否存在
            if (!file.exists()) {
                downloadStatus = 0;
                // 记录下载失败统计
                downloadStatisticsService.recordDownload(request, fileName, 0L, downloadStatus);
                return ResponseEntity.notFound().build();
            }
            
            // 创建资源对象
            Resource resource = new FileSystemResource(file);
            
            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
            
            // 记录下载成功统计
            downloadStatisticsService.recordDownload(request, fileName, file.length(), downloadStatus);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .body(resource);
                    
        } catch (Exception e) {
            downloadStatus = 0;
            // 记录下载失败统计
            downloadStatisticsService.recordDownload(request, fileName, 0L, downloadStatus);
            return ResponseEntity.internalServerError().build();
        }
    }
}
