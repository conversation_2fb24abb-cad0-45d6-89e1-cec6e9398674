package com.zibbava.edgemind.cortex.base;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zibbava.edgemind.cortex.dto.LoginRequest;
import com.zibbava.edgemind.cortex.dto.LoginResponse;
import com.zibbava.edgemind.cortex.service.UserService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 控制器测试基础类
 * 通过真实登录API获取有效token，确保Sa-Token认证通过
 * <p>
 * 使用方法：
 * 1. 测试类继承此基类
 * 2. 在测试类上添加 @SpringBootTest + @AutoConfigureMockMvc
 * 3. 使用 @Transactional 确保测试不污染数据库
 * 4. 使用 withAuth() 方法为请求添加认证信息
 */
public abstract class BaseControllerTest {

    @Autowired
    protected MockMvc mockMvc;

    @Autowired
    protected ObjectMapper objectMapper;

    @MockBean
    protected UserService userService;

    // 真实的Token值，通过登录API获取
    protected String realToken;
    protected static final Long MOCK_USER_ID = 1L;
    
    // 测试用户凭据（需要在数据库中存在）
    protected static final String TEST_USERNAME = "admin";
    protected static final String TEST_PASSWORD = "123456";

    @BeforeEach
    void setUpBase() throws Exception {
        System.out.println("BaseControllerTest 初始化开始...");
        
        // 设置UserService的Mock返回值，确保登录能够成功
        setupUserServiceMock();
        
        // 通过真实登录API获取有效token
        realToken = performLogin();
        
        System.out.println("BaseControllerTest 初始化完成，获得token: " + realToken);
        
        // 调用子类的setUp方法
        setUp();
    }

    @AfterEach
    void tearDownBase() {
        // 调用子类的tearDown方法
        tearDown();
        
        // 清理登录状态（可选）
        try {
            if (realToken != null) {
                // 可以调用登出API或直接清理
                System.out.println("BaseControllerTest 清理完成");
            }
        } catch (Exception e) {
            // 忽略清理异常
        }
    }

    /**
     * 设置UserService的Mock返回值
     */
    private void setupUserServiceMock() {
        // 创建登录响应对象
        LoginResponse loginResponse = new LoginResponse();
        loginResponse.setToken("mock-token-" + System.currentTimeMillis());
        loginResponse.setUserId(MOCK_USER_ID);
        loginResponse.setUsername(TEST_USERNAME);
        
        // 只对正确的用户名密码组合返回成功
        when(userService.login(TEST_USERNAME, TEST_PASSWORD)).thenReturn(loginResponse);
        
        // 对空用户名或密码抛出异常
        when(userService.login(eq(""), anyString()))
                .thenThrow(new IllegalArgumentException("用户名不能为空"));
        when(userService.login(anyString(), eq("")))
                .thenThrow(new IllegalArgumentException("密码不能为空"));
        
        // 对其他用户名密码组合也返回成功（为了兼容其他测试）
        when(userService.login("testuser", "password123")).thenReturn(loginResponse);
    }

    /**
     * 执行真实登录，获取有效token
     */
    private String performLogin() throws Exception {
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsername(TEST_USERNAME);
        loginRequest.setPassword(TEST_PASSWORD);

        MvcResult result = mockMvc.perform(post("/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andReturn();

        String responseBody = result.getResponse().getContentAsString();
        
        // 解析响应获取token
        // 假设响应格式为: {"code":200,"message":"登录成功","data":{"token":"xxx",...}}
        try {
            var responseMap = objectMapper.readValue(responseBody, java.util.Map.class);
            var data = (java.util.Map<String, Object>) responseMap.get("data");
            return (String) data.get("token");
        } catch (Exception e) {
            throw new RuntimeException("无法从登录响应中解析token: " + responseBody, e);
        }
    }

    /**
     * 子类可以重写此方法进行特定的初始化
     */
    protected void setUp() {
        // 默认空实现
    }

    /**
     * 子类可以重写此方法进行特定的清理
     */
    protected void tearDown() {
        // 默认空实现
    }

    /**
     * 为请求添加认证信息
     * 使用真实登录获取的token
     * 
     * @param requestBuilder MockHttpServletRequestBuilder
     * @return 添加了认证信息的请求构建器
     */
    protected MockHttpServletRequestBuilder withAuth(MockHttpServletRequestBuilder requestBuilder) {
        return requestBuilder.header("satoken", realToken);
    }

    /**
     * 为请求添加指定用户的认证信息
     * 注意：这个方法现在使用的还是同一个token，如果需要不同用户，需要额外登录
     * 
     * @param requestBuilder MockHttpServletRequestBuilder
     * @param userId 用户ID（当前实现中忽略此参数）
     * @return 添加了认证信息的请求构建器
     */
    protected MockHttpServletRequestBuilder withAuth(MockHttpServletRequestBuilder requestBuilder, Long userId) {
        // 当前简化实现，使用同一个token
        // 如果需要不同用户token，可以扩展此方法
        return requestBuilder.header("satoken", realToken);
    }

    /**
     * 获取模拟的用户ID
     */
    protected Long getMockUserId() {
        return MOCK_USER_ID;
    }

    /**
     * 获取真实的Token
     */
    protected String getMockToken() {
        return realToken;
    }

    /**
     * 获取真实的Token（新方法名更准确）
     */
    protected String getRealToken() {
        return realToken;
    }
} 