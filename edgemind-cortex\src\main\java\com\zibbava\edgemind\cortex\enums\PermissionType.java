package com.zibbava.edgemind.cortex.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 权限类型枚举
 * 定义系统中不同类型的权限
 */
@Getter
public enum PermissionType {
    
    /**
     * 菜单权限 - 控制菜单的显示和访问
     */
    MENU("MENU", "菜单权限", "控制菜单的显示和访问权限"),
    
    /**
     * 按钮权限 - 控制页面按钮的显示和操作
     */
    BUTTON("BUTTON", "按钮权限", "控制页面按钮的显示和操作权限"),
    
    /**
     * API权限 - 控制API接口的访问
     */
    API("API", "接口权限", "控制API接口的访问权限"),
    
    /**
     * 数据权限 - 控制数据的访问范围
     */
    DATA("DATA", "数据权限", "控制数据的访问范围权限");

    /**
     * 权限类型编码 - 存储到数据库的值
     */
    @EnumValue
    @JsonValue
    private final String code;
    
    /**
     * 权限类型名称 - 显示名称
     */
    private final String name;
    
    /**
     * 权限类型描述
     */
    private final String description;

    PermissionType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    /**
     * 根据编码获取权限类型
     * @param code 权限类型编码
     * @return 权限类型枚举，如果未找到返回null
     */
    public static PermissionType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (PermissionType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查是否为菜单类型权限
     * @return true如果是菜单权限
     */
    public boolean isMenu() {
        return this == MENU;
    }

    /**
     * 检查是否为按钮类型权限
     * @return true如果是按钮权限
     */
    public boolean isButton() {
        return this == BUTTON;
    }

    /**
     * 检查是否为API类型权限
     * @return true如果是API权限
     */
    public boolean isApi() {
        return this == API;
    }

    /**
     * 检查是否为数据类型权限
     * @return true如果是数据权限
     */
    public boolean isData() {
        return this == DATA;
    }

    /**
     * 获取所有权限类型的编码列表
     * @return 权限类型编码列表
     */
    public static String[] getAllCodes() {
        return new String[]{MENU.getCode(), BUTTON.getCode(), API.getCode(), DATA.getCode()};
    }

    /**
     * 获取所有权限类型的名称列表
     * @return 权限类型名称列表
     */
    public static String[] getAllNames() {
        return new String[]{MENU.getName(), BUTTON.getName(), API.getName(), DATA.getName()};
    }
}
