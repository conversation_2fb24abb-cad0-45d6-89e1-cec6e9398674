/* settings.css - 系统设置页面样式 */

:root {
    --primary-color: #007bff;
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --bg-light: #f8f9fa;
    --card-bg: #ffffff;
    --hover-bg: #f1f3f4;
}

body {
    background-color: var(--bg-light);
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    font-weight: 400;
    min-height: 100vh;
}

.settings-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
}

.page-header {
    display: none;
}

.section-title {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 1.125rem;
    margin-bottom: 1rem;
}

.settings-header h2 {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.settings-section {
    margin-bottom: 1rem;
}

.settings-section-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.5rem;
    margin-bottom: 2rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--primary-color);
}

.settings-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.2s ease;
}

.settings-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.settings-card .card-header {
    background: var(--hover-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem 1rem;
    border-radius: 8px 8px 0 0;
}

.settings-card .card-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.125rem;
    margin: 0;
}

.settings-card .card-body {
    padding: 2rem;
}

.settings-form-label {
    font-weight: 500;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.form-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

/* 输入框样式 */
.form-control {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    background-color: var(--card-bg);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    outline: none;
    background-color: var(--card-bg);
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-outline-secondary:hover {
    background-color: var(--hover-bg);
    border-color: var(--text-secondary);
    color: var(--text-primary);
}

/* 系统信息行样式 */
.system-info-row {
    line-height: 1.5;
    padding: 0.75rem 0;
    min-height: auto;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.system-info-row:last-child {
    border-bottom: none;
}

.system-info-row .label {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.4;
}

.system-info-row .value {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
    line-height: 1.4;
}

/* 系统信息卡片样式 - 保持紧凑 */
.settings-card:first-child .card-body {
    margin: 0;
    padding: 0 1.5rem;
}

.settings-card:first-child .row {
    margin: 0;
    align-items: center;
}

.settings-card:first-child .row.mb-3 {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 0 !important;
}

.settings-card:first-child .row.mb-3:last-child {
    border-bottom: none;
}

.settings-card:first-child .row .col-3 {
    padding: 0.5rem 1rem 0.5rem 0;
    display: flex;
    align-items: center;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.settings-card:first-child .row .col-9 {
    padding: 0.5rem 0 0.5rem 1rem;
    display: flex;
    align-items: center;
}

.settings-card:first-child .row .col-9 .d-flex {
    align-items: center;
    gap: 0.5rem;
}

/* 知识库设置卡片样式 - 保持宽松 */
.settings-card:not(:first-child) .card-body {
    padding: 2rem;
}

/* 调整复制按钮大小 */
.settings-card:first-child #copyIdentifierBtn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
    height: auto;
    min-width: auto;
    border-radius: 4px;
}

.settings-card:first-child #copyIdentifierBtn i {
    font-size: 0.7rem;
}

.fw-medium.text-secondary {
    font-size: 0.85rem;
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
}

.text-primary.fw-medium {
    font-size: 0.85rem;
    color: var(--text-primary) !important;
    font-weight: 500 !important;
}

/* 警告提示框样式 */
.alert {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.25rem 1.5rem;
    margin-top: 1rem;
    margin-bottom: 1.5rem;
    background-color: var(--card-bg);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background-color: #e7f3ff;
    border-color: #b8daff;
    color: #0c5460;
    border-left: 4px solid var(--primary-color);
}

.alert-heading {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    color: inherit;
}

.alert ul {
    margin-bottom: 0;
    padding-left: 1.25rem;
}

.alert li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
    font-size: 0.9rem;
}

.alert li:last-child {
    margin-bottom: 0;
}

/* 输入组样式 */
.input-group {
    border-radius: 0;
    overflow: hidden;
}

.input-group .form-control {
    border-right: none;
    border-radius: 0;
}

.input-group .btn {
    border-radius: 0;
    border-left: none;
}

/* 模态框样式 */
.modal-content {
    border-radius: 0;
    border: 1px solid var(--border-color);
    box-shadow: none;
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    padding: 2rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .settings-container {
        padding: 1rem;
        max-width: 100%;
    }
    
    .page-header {
        text-align: center;
        margin-bottom: 1.5rem;
    }
    
    .page-header h1 {
        font-size: 1.75rem;
    }
    
    .settings-section-title {
        font-size: 1.25rem;
        text-align: center;
    }
    
    .settings-card .card-header {
        padding: 1rem;
    }
    
    .settings-card .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
    
    .alert {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .form-control {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}
