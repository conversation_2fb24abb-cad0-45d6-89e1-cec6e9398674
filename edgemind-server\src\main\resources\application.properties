spring.application.name=mcp-server

spring.ai.mcp.server.name=mcp-server
spring.ai.mcp.server.version=0.0.1
spring.ai.mcp.server.sse-endpoint=/sse

spring.ai.mcp.server.description=mcp\u96C6\u5408

# Server port
server.port=8081

server.servlet.context-path=/aistudio

spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.thymeleaf.cache=false
spring.web.resources.static-locations=classpath:/static/
spring.web.resources.cache.period=0

# ================= \u6570\u636E\u5E93\u914D\u7F6E =================
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
# \u6570\u636E\u5E93\u8FDE\u63A5URL (\u8BF7\u66FF\u6362 your_db_name)
spring.datasource.url=jdbc:mysql://**************:3306/aistudio?allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&useSSL=false
spring.datasource.username=root
spring.datasource.password=wkg@llqzer1123

# ================= MyBatis Plus\u914D\u7F6E =================
mybatis-plus.mapper-locations=classpath*:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.zibbava.edgemind.server.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# ================= Redis\u914D\u7F6E =================
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.database=0
spring.data.redis.timeout=10000ms
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-wait=-1ms
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0

# ================= Sa-Token\u914D\u7F6E =================
# token\u540D\u79F0 (\u540C\u65F6\u4E5F\u662Fcookie\u540D\u79F0)
sa-token.token-name=satoken
# token\u6709\u6548\u671F\uFF0C\u5355\u4F4Ds \u9ED8\u8BA430\u5929, -1\u4EE3\u8868\u6C38\u4E0D\u8FC7\u671F
sa-token.timeout=2592000
# \u662F\u5426\u5141\u8BB8\u540C\u4E00\u8D26\u53F7\u5E76\u53D1\u767B\u5F55 (\u4E3Atrue\u65F6\u5141\u8BB8\u4E00\u8D77\u767B\u5F55, \u4E3Afalse\u65F6\u65B0\u767B\u5F55\u6324\u6389\u65E7\u767B\u5F55)
sa-token.is-concurrent=true
# \u5728\u591A\u4EBA\u767B\u5F55\u540C\u4E00\u8D26\u53F7\u65F6\uFF0C\u662F\u5426\u5171\u7528\u4E00\u4E2Atoken (\u4E3Atrue\u65F6\u6240\u6709\u767B\u5F55\u5171\u7528\u4E00\u4E2Atoken, \u4E3Afalse\u65F6\u6BCF\u6B21\u767B\u5F55\u65B0\u5EFA\u4E00\u4E2Atoken)
sa-token.is-share=true
# token\u98CE\u683C
sa-token.token-style=uuid
# \u662F\u5426\u8F93\u51FA\u64CD\u4F5C\u65E5\u5FD7
sa-token.is-log=false
