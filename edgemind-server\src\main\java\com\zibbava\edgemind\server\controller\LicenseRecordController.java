package com.zibbava.edgemind.server.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zibbava.edgemind.server.common.Result;
import com.zibbava.edgemind.server.dto.*;
import com.zibbava.edgemind.server.service.LicenseRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 许可证记录管理控制器
 */
@RestController
@RequestMapping("/api/license-records")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "许可证记录管理", description = "许可证记录的查询、管理和统计")
public class LicenseRecordController {

    private final LicenseRecordService licenseRecordService;

    @PostMapping("/admin/generate")
    @Operation(summary = "管理员生成许可证", description = "管理员为指定硬件指纹生成许可证")
    public Result<String> generateAdminLicense(
            @RequestBody LicenseInfoDTO licenseInfo,
            @Parameter(description = "生成者") @RequestParam(required = false) String generatedBy,
            HttpServletRequest request) {
        
        String clientInfo = getClientInfo(request);
        String actualGeneratedBy = generatedBy != null ? generatedBy : "ADMIN";
        
        String licenseKey = licenseRecordService.generateAdminLicense(licenseInfo, actualGeneratedBy, clientInfo);
        
        if (licenseKey != null) {
            return Result.success(licenseKey, "管理员许可证生成成功");
        } else {
            return Result.error("管理员许可证生成失败");
        }
    }

    @PostMapping("/free-trial/generate")
    @Operation(summary = "生成免费试用许可证", description = "为新用户生成免费试用许可证")
    public Result<String> generateFreeTrialLicense(
            @RequestBody FreeTrialRequestDTO request,
            HttpServletRequest httpRequest) {
        if (!"wkg-token-3321".equals(request.getToken())){
            log.warn("许可证接口被人为调用 {}", request);
            return Result.error("免费试用许可证生成失败");
        }

        String clientInfo = getClientInfo(httpRequest);
        String licenseKey = licenseRecordService.generateFreeTrialLicense(request, clientInfo);
        
        if (licenseKey != null) {
            return Result.success("免费试用许可证生成成功", licenseKey);
        } else {
            return Result.error("免费试用许可证生成失败，可能已使用过免费额度");
        }
    }

    @PostMapping("/page")
    @Operation(summary = "分页查询许可证记录", description = "根据条件分页查询许可证记录")
    public Result<IPage<LicenseRecordDTO>> getLicenseRecordPage(@RequestBody LicenseRecordQueryDTO queryDTO) {
        IPage<LicenseRecordDTO> page = licenseRecordService.getLicenseRecordPage(queryDTO);
        return Result.success(page);
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取许可证记录", description = "根据记录ID获取许可证详细信息")
    public Result<LicenseRecordDTO> getLicenseRecordById(
            @Parameter(description = "记录ID") @PathVariable Long id) {
        LicenseRecordDTO record = licenseRecordService.getLicenseRecordById(id);
        if (record != null) {
            return Result.success(record);
        } else {
            return Result.error("许可证记录不存在");
        }
    }

    @GetMapping("/by-key/{licenseKey}")
    @Operation(summary = "根据许可证密钥获取记录", description = "根据许可证密钥获取许可证详细信息")
    public Result<LicenseRecordDTO> getLicenseRecordByKey(
            @Parameter(description = "许可证密钥") @PathVariable String licenseKey) {
        LicenseRecordDTO record = licenseRecordService.getLicenseRecordByKey(licenseKey);
        if (record != null) {
            return Result.success(record);
        } else {
            return Result.error("许可证记录不存在");
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除许可证记录", description = "删除指定的许可证记录")
    public Result<Void> deleteLicenseRecord(
            @Parameter(description = "记录ID") @PathVariable Long id) {
        
        boolean success = licenseRecordService.deleteLicenseRecord(id);
        if (success) {
            return Result.success("许可证记录删除成功", null);
        } else {
            return Result.error("许可证记录删除失败");
        }
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取许可证统计信息", description = "获取许可证的各种统计数据")
    public Result<LicenseRecordStatisticsDTO> getLicenseStatistics() {
        LicenseRecordStatisticsDTO statistics = licenseRecordService.getLicenseStatistics();
        return Result.success(statistics);
    }

    @GetMapping("/expiring")
    @Operation(summary = "获取即将过期的许可证", description = "获取指定天数内即将过期的许可证列表")
    public Result<List<LicenseRecordDTO>> getExpiringLicenses(
            @Parameter(description = "天数") @RequestParam(defaultValue = "7") Integer days) {
        
        List<LicenseRecordDTO> expiringLicenses = licenseRecordService.getExpiringLicenses(days);
        return Result.success(expiringLicenses);
    }

    // 已移除过期状态更新功能 - 仅保留记录查询功能

    @GetMapping("/check-free-trial/{hardwareFingerprint}")
    @Operation(summary = "检查免费试用使用情况", description = "检查指定硬件指纹是否已使用过免费试用")
    public Result<Boolean> checkFreeTrialUsage(
            @Parameter(description = "硬件指纹") @PathVariable String hardwareFingerprint) {
        
        boolean hasUsed = licenseRecordService.hasUsedFreeTrial(hardwareFingerprint);
        return Result.success(hasUsed);
    }

    @GetMapping("/validate/{licenseKey}")
    @Operation(summary = "验证许可证有效性", description = "验证指定许可证是否有效")
    public Result<Boolean> validateLicense(
            @Parameter(description = "许可证密钥") @PathVariable String licenseKey) {
        
        boolean isValid = licenseRecordService.isLicenseValid(licenseKey);
        return Result.success(isValid);
    }

    /**
     * 获取客户端信息
     */
    private String getClientInfo(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        String remoteAddr = request.getRemoteAddr();
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        
        // 构建JSON格式的客户端信息
        StringBuilder jsonBuilder = new StringBuilder();
        jsonBuilder.append("{");
        jsonBuilder.append("\"ip\":\"").append(remoteAddr != null ? remoteAddr : "").append("\"");
        
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            jsonBuilder.append(",\"xForwardedFor\":\"").append(xForwardedFor).append("\"");
        }
        
        if (userAgent != null && !userAgent.isEmpty()) {
            // 转义用户代理字符串中的特殊字符
            String escapedUserAgent = userAgent.replace("\\", "\\\\")
                                               .replace("\"", "\\\"")
                                               .replace("\n", "\\n")
                                               .replace("\r", "\\r")
                                               .replace("\t", "\\t");
            jsonBuilder.append(",\"userAgent\":\"").append(escapedUserAgent).append("\"");
        }
        
        jsonBuilder.append("}");
        return jsonBuilder.toString();
    }
}