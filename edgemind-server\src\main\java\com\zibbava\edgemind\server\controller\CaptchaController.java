package com.zibbava.edgemind.server.controller;

import com.zibbava.edgemind.server.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Random;

/**
 * 验证码控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Controller
@RequestMapping("/captcha")
public class CaptchaController {

    private static final String CAPTCHA_SESSION_KEY = "captcha_code";
    private static final int CAPTCHA_WIDTH = 120;
    private static final int CAPTCHA_HEIGHT = 40;
    private static final int CAPTCHA_LENGTH = 4;
    
    // 验证码字符集（去除容易混淆的字符）
    private static final String CAPTCHA_CHARS = "23456789ABCDEFGHJKLMNPQRSTUVWXYZ";
    
    /**
     * 生成验证码图片
     */
    @GetMapping("/image")
    public void generateCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 设置响应头
        response.setContentType("image/jpeg");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);
        
        // 生成验证码
        String captchaCode = generateRandomCode();
        
        // 将验证码存储到session中
        HttpSession session = request.getSession();
        session.setAttribute(CAPTCHA_SESSION_KEY, captchaCode.toLowerCase());
        session.setMaxInactiveInterval(300); // 5分钟过期
        
        log.debug("生成验证码: {}", captchaCode);
        
        // 创建图片
        BufferedImage image = createCaptchaImage(captchaCode);
        
        // 输出图片
        ImageIO.write(image, "JPEG", response.getOutputStream());
        response.getOutputStream().flush();
    }
    
    /**
     * 验证验证码
     */
    @GetMapping("/verify")
    @ResponseBody
    public Result<Boolean> verifyCaptcha(String code, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            String sessionCode = (String) session.getAttribute(CAPTCHA_SESSION_KEY);
            
            if (sessionCode == null) {
                return Result.error("验证码已过期，请刷新");
            }
            
            boolean isValid = sessionCode.equalsIgnoreCase(code);
            
            if (isValid) {
                // 验证成功后清除session中的验证码
                session.removeAttribute(CAPTCHA_SESSION_KEY);
                return Result.success("验证码正确", true);
            } else {
                return Result.error("验证码错误");
            }
        } catch (Exception e) {
            log.error("验证码验证失败", e);
            return Result.error("验证码验证失败");
        }
    }
    
    /**
     * 生成随机验证码
     */
    private String generateRandomCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        
        for (int i = 0; i < CAPTCHA_LENGTH; i++) {
            code.append(CAPTCHA_CHARS.charAt(random.nextInt(CAPTCHA_CHARS.length())));
        }
        
        return code.toString();
    }
    
    /**
     * 创建验证码图片
     */
    private BufferedImage createCaptchaImage(String code) {
        BufferedImage image = new BufferedImage(CAPTCHA_WIDTH, CAPTCHA_HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 填充背景
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, CAPTCHA_WIDTH, CAPTCHA_HEIGHT);
        
        // 绘制干扰线
        Random random = new Random();
        g2d.setColor(new Color(200, 200, 200));
        for (int i = 0; i < 5; i++) {
            int x1 = random.nextInt(CAPTCHA_WIDTH);
            int y1 = random.nextInt(CAPTCHA_HEIGHT);
            int x2 = random.nextInt(CAPTCHA_WIDTH);
            int y2 = random.nextInt(CAPTCHA_HEIGHT);
            g2d.drawLine(x1, y1, x2, y2);
        }
        
        // 绘制验证码字符
        Font font = new Font("Arial", Font.BOLD, 24);
        g2d.setFont(font);
        
        int charWidth = CAPTCHA_WIDTH / CAPTCHA_LENGTH;
        for (int i = 0; i < code.length(); i++) {
            // 随机颜色
            g2d.setColor(new Color(random.nextInt(100), random.nextInt(100), random.nextInt(100)));
            
            // 随机位置和角度
            int x = i * charWidth + random.nextInt(10) + 5;
            int y = CAPTCHA_HEIGHT / 2 + random.nextInt(10) + 5;
            
            // 随机旋转
            double angle = (random.nextDouble() - 0.5) * 0.5;
            g2d.rotate(angle, x, y);
            
            g2d.drawString(String.valueOf(code.charAt(i)), x, y);
            
            // 恢复旋转
            g2d.rotate(-angle, x, y);
        }
        
        // 绘制干扰点
        for (int i = 0; i < 50; i++) {
            int x = random.nextInt(CAPTCHA_WIDTH);
            int y = random.nextInt(CAPTCHA_HEIGHT);
            g2d.setColor(new Color(random.nextInt(255), random.nextInt(255), random.nextInt(255)));
            g2d.fillOval(x, y, 1, 1);
        }
        
        g2d.dispose();
        return image;
    }
}