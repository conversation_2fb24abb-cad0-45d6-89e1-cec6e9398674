package com.zibbava.edgemind.cortex.service;

import com.onlyoffice.model.documenteditor.Callback;

import java.util.Map;

/**
 * ONLYOFFICE 文档服务接口
 * 提供与 ONLYOFFICE Document Server 交互的相关功能
 */
public interface OnlyOfficeService {

    /**
     * 获取指定文件节点的 ONLYOFFICE 配置
     *
     * @param nodeId 文件节点ID
     * @param userId 当前用户ID
     * @param userName 当前用户名称
     * @return ONLYOFFICE 编辑器配置对象及附加信息
     */
    Map<String, Object> getDocumentEditorConfig(String nodeId, Long userId, String userName);
    
    /**
     * 处理 ONLYOFFICE 编辑文档后的回调
     * 
     * @param nodeId 文件节点ID
     * @param callback 回调数据
     * @return 处理结果
     */
    boolean processEditCallback(String nodeId, Callback callback);
} 