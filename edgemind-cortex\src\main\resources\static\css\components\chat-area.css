/* 聊天区域独立样式 */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    min-height: 0; /* 修复Firefox中flex布局的问题 */
}

.chat-messages {
    flex: 1 1 auto; /* 使用flex简写属性确保它能填充容器 */
    overflow-y: auto;
    padding: 1rem 1.5rem; /* 增加左右内边距 */
    background-color: #ffffff; /* 明确背景色 */
    min-height: 0; /* 确保在Firefox中flex项能够正确收缩 */

    /* 下面的样式已在chat.html中定义，这里不再重复 */
    /* max-width: 900px; */
    /* margin: 0 auto; */
    /* width: 100%; */
}

/* 响应式调整 */
@media (max-width: 576px) {
    .chat-messages {
        padding: 1rem; /* 在小屏幕上减小内边距 */
    }
}

/* 使用通用滚动条样式 */
.chat-messages {
    /* 通用滚动条样式将自动应用 */
}

.message {
    max-width: 85%;
    margin-bottom: 1.5rem;
    line-height: 1.5;
    /* 移除边框和阴影 */
    border: none;
    box-shadow: none;
    /* 注意：具体的padding和border-radius已移到子元素中 */
}

.user-message {
    background-color: transparent; /* 移除消息元素的背景色 */
    color: #0a58ca;
    margin-left: auto; /* 将消息元素推到右边 */
    margin-right: 0;
    /* 完全重写用户消息样式，解决空白蓝条问题 */
    text-align: right;
    width: auto !important; /* 强制设置宽度为自适应 */
    max-width: 85%;
    display: block;
    padding: 0; /* 移除消息元素的内边距 */
}

/* 用户消息内容对齐 */
.user-message > div {
    text-align: left; /* 消息内容左对齐 */
    display: inline-block; /* 内容块不铺满整个消息元素 */
    max-width: 100%; /* 确保内容不超出消息元素 */
    background-color: #e7f3ff; /* 将背景色移到内容元素 */
    padding: 12px 16px; /* 为内容元素添加内边距 */
    border-radius: 10px; /* 为内容元素添加圆角 */
    border-bottom-right-radius: 2px; /* 右下角特殊处理 */
}

.ai-message {
    background-color: #ffffff; /* 改为白色背景 */
    color: #212529;
    /* 铺满聊天框，不再偏左 */
    margin-right: 0;
    margin-left: 0;
    max-width: 100%;
    width: 100%;
    border-radius: 0;
    padding-left: 0; /* 移除左侧内边距 */
    padding-right: 0; /* 移除右侧内边距 */
}

/* AI消息内容铺满 */
.ai-message > div,
.ai-message .ai-main-content {
    width: 100%;
    display: block;
    padding-left: 16px; /* 为内容添加左侧内边距 */
    padding-right: 16px; /* 为内容添加右侧内边距 */
}

.welcome-message {
    background-color: #ffffff; /* 改为白色背景 */
    color: #212529;
    /* 与AI消息保持一致 */
    margin-right: 0;
    margin-left: 0;
    max-width: 100%;
    width: 100%;
    border-radius: 0;
    padding-left: 0; /* 移除左侧内边距 */
    padding-right: 0; /* 移除右侧内边距 */
}

/* 欢迎消息内容铺满 */
.welcome-message > p,
.welcome-message > div {
    width: 100%;
    display: block;
    padding-left: 16px; /* 为内容添加左侧内边距 */
    padding-right: 16px; /* 为内容添加右侧内边距 */
}

/* 代码高亮样式 */
pre {
    background-color: #f6f8fa;
    border-radius: 6px;
    padding: 1rem;
    overflow-x: auto;
    margin: 1rem 0;
}

code {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.9em;
    padding: 0.2em 0.4em;
    background-color: rgba(27,31,35,0.05);
    border-radius: 3px;
}

pre code {
    padding: 0;
    background-color: transparent;
}

/* 错误消息样式 */
.error-message {
    background-color: #f8d7da;
    color: #842029;
    padding: 0.75rem 1.25rem;
    border-radius: 6px;
    margin-bottom: 1rem;
}

/* 加载中动画 */
.typing-indicator {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.typing-indicator span {
    height: 8px;
    width: 8px;
    border-radius: 50%;
    background-color: #6c757d;
    margin: 0 2px;
    display: inline-block;
    animation: typing 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(1) {
    animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.6);
        opacity: 0.6;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}