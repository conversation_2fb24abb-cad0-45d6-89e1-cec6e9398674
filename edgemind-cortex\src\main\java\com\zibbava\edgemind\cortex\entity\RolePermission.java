package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_role_permission") // 对应数据库中间表名
public class RolePermission {

    // 联合主键 (role_id, permission_id)

    @TableField("role_id")
    private Long roleId;

    @TableField("permission_id")
    private Long permissionId;

} 