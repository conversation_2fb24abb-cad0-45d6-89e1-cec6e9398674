package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.entity.KnowledgeNodeClosure;
import com.zibbava.edgemind.cortex.mapper.KnowledgeNodeClosureMapper;
import com.zibbava.edgemind.cortex.mapper.KnowledgeNodeMapper;
import com.zibbava.edgemind.cortex.service.KnowledgeNodeClosureService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 知识库节点闭包表服务实现类
 * 提供对节点树结构的高效查询和管理功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class KnowledgeNodeClosureServiceImpl implements KnowledgeNodeClosureService {

    private final KnowledgeNodeMapper nodeMapper;
    private final KnowledgeNodeClosureMapper closureMapper;

    @Override
    @Transactional
    public void initNodeClosure(String nodeId) {
        KnowledgeNode node = nodeMapper.selectById(nodeId);
        if (node == null) {
            log.error("节点不存在，无法初始化闭包表关系: nodeId={}", nodeId);
            return;
        }

        // 1. 添加自身关系 (depth = 0)
        KnowledgeNodeClosure selfClosure = new KnowledgeNodeClosure();
        selfClosure.setAncestorId(nodeId);
        selfClosure.setDescendantId(nodeId);
        selfClosure.setDepth(0);
        closureMapper.insert(selfClosure);

        // 2. 如果有父节点，添加与所有祖先节点的关系
        if (StringUtils.hasText(node.getParentNodeId())) {
            // 查询父节点的所有祖先关系
            LambdaQueryWrapper<KnowledgeNodeClosure> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(KnowledgeNodeClosure::getDescendantId, node.getParentNodeId());
            List<KnowledgeNodeClosure> parentClosures = closureMapper.selectList(queryWrapper);

            // 为每个祖先节点创建与当前节点的关系
            for (KnowledgeNodeClosure parentClosure : parentClosures) {
                KnowledgeNodeClosure newClosure = new KnowledgeNodeClosure();
                newClosure.setAncestorId(parentClosure.getAncestorId());
                newClosure.setDescendantId(nodeId);
                newClosure.setDepth(parentClosure.getDepth() + 1);
                closureMapper.insert(newClosure);
            }
        }

        log.info("节点闭包表关系初始化成功: nodeId={}", nodeId);
    }

    @Override
    @Transactional
    public void deleteNodeClosure(String nodeId) {
        // 1. 删除所有以该节点为后代的关系
        LambdaQueryWrapper<KnowledgeNodeClosure> descendantWrapper = new LambdaQueryWrapper<>();
        descendantWrapper.eq(KnowledgeNodeClosure::getDescendantId, nodeId);
        closureMapper.delete(descendantWrapper);

        // 2. 删除所有以该节点为祖先的关系
        LambdaQueryWrapper<KnowledgeNodeClosure> ancestorWrapper = new LambdaQueryWrapper<>();
        ancestorWrapper.eq(KnowledgeNodeClosure::getAncestorId, nodeId);
        closureMapper.delete(ancestorWrapper);

        log.info("节点闭包表关系删除成功: nodeId={}", nodeId);
    }

    @Override
    @Transactional
    public void updateNodeClosure(String nodeId, String newParentId) {
        // 1. 获取当前节点及其所有后代
        List<String> descendants = new ArrayList<>();
        descendants.add(nodeId);

        LambdaQueryWrapper<KnowledgeNodeClosure> descendantsQuery = new LambdaQueryWrapper<>();
        descendantsQuery.eq(KnowledgeNodeClosure::getAncestorId, nodeId)
                        .ne(KnowledgeNodeClosure::getDescendantId, nodeId); // 排除自身

        List<KnowledgeNodeClosure> descendantClosures = closureMapper.selectList(descendantsQuery);
        for (KnowledgeNodeClosure closure : descendantClosures) {
            descendants.add(closure.getDescendantId());
        }

        // 2. 删除这些节点与其旧祖先的关系
        for (String descendantId : descendants) {
            LambdaQueryWrapper<KnowledgeNodeClosure> oldRelationsQuery = new LambdaQueryWrapper<>();
            oldRelationsQuery.eq(KnowledgeNodeClosure::getDescendantId, descendantId)
                            .ne(KnowledgeNodeClosure::getAncestorId, descendantId); // 保留自身关系
            closureMapper.delete(oldRelationsQuery);
        }

        // 3. 如果有新父节点，为这些节点创建与新祖先的关系
        if (StringUtils.hasText(newParentId)) {
            // 获取新父节点的所有祖先
            LambdaQueryWrapper<KnowledgeNodeClosure> newAncestorsQuery = new LambdaQueryWrapper<>();
            newAncestorsQuery.eq(KnowledgeNodeClosure::getDescendantId, newParentId);
            List<KnowledgeNodeClosure> newAncestorClosures = closureMapper.selectList(newAncestorsQuery);

            // 为每个后代节点创建与每个新祖先的关系
            for (String descendantId : descendants) {
                for (KnowledgeNodeClosure ancestorClosure : newAncestorClosures) {
                    KnowledgeNodeClosure newClosure = new KnowledgeNodeClosure();
                    newClosure.setAncestorId(ancestorClosure.getAncestorId());
                    newClosure.setDescendantId(descendantId);
                    newClosure.setDepth(ancestorClosure.getDepth() + getDepthBetween(nodeId, descendantId));
                    closureMapper.insert(newClosure);
                }
            }
        }

        // 4. 更新节点的父节点引用
        KnowledgeNode node = new KnowledgeNode();
        node.setNodeId(nodeId);
        node.setParentNodeId(newParentId);
        nodeMapper.updateById(node);

        log.info("节点闭包表关系更新成功: nodeId={}, newParentId={}", nodeId, newParentId);
    }

    /**
     * 获取两个节点之间的深度差
     * 如果descendantId就是nodeId，则返回0
     */
    private int getDepthBetween(String nodeId, String descendantId) {
        if (nodeId.equals(descendantId)) {
            return 0;
        }

        LambdaQueryWrapper<KnowledgeNodeClosure> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeNodeClosure::getAncestorId, nodeId)
                   .eq(KnowledgeNodeClosure::getDescendantId, descendantId);

        KnowledgeNodeClosure closure = closureMapper.selectOne(queryWrapper);
        return closure != null ? closure.getDepth() : 1; // 默认为1，表示直接子节点
    }

    @Override
    public List<KnowledgeNode> getDescendants(String ancestorId) {
        return closureMapper.findDescendants(ancestorId);
    }

    @Override
    public List<KnowledgeNode> getAncestors(String descendantId) {
        return closureMapper.findAncestors(descendantId);
    }

    @Override
    public List<KnowledgeNode> getDirectChildren(String parentId) {
        return closureMapper.findDirectChildren(parentId);
    }

    @Override
    public List<KnowledgeNode> getRootNodes(String spaceId) {
        return closureMapper.findRootNodes(spaceId);
    }

    @Override
    public List<KnowledgeNode> getNodesPaged(String spaceId, String ancestorId, int offset, int limit) {
        return closureMapper.findNodesPaged(spaceId, ancestorId, offset, limit);
    }

    @Override
    public int countNodes(String spaceId, String ancestorId) {
        return closureMapper.countNodes(spaceId, ancestorId);
    }

    @Override
    @Transactional
    public void deleteAllClosures() {
        log.info("开始删除所有节点闭包关系");
        try {
            closureMapper.delete(new LambdaQueryWrapper<>());
            log.info("所有节点闭包关系删除成功");
        } catch (Exception e) {
            log.error("删除节点闭包关系时出错: {}", e.getMessage(), e);
            throw new RuntimeException("删除节点闭包关系失败", e);
        }
    }
}
