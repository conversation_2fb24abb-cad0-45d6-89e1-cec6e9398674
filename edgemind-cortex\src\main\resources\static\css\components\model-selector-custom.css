/* 自定义模型选择器样式，覆盖Bootstrap-select默认样式 */

/* 增加模型选择器容器宽度 */
.model-selector-container {
    margin-right: 10px;
}

/* 增加bootstrap-select按钮容器宽度 */
.bootstrap-select {
    max-width: 180px !important;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

/* 清理默认样式 */
.bootstrap-select .dropdown-toggle {
    border: none;
    background-color: transparent;
    box-shadow: none;
}

/* 下拉菜单选项文本 */
.bootstrap-select .dropdown-menu li a {
    white-space: normal !important; /* 允许文本换行 */
    word-wrap: break-word !important;
    padding: 6px 10px;
}

/* 选择框内文本 */
.bootstrap-select .filter-option-inner-inner {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 覆盖下拉菜单的最小高度 */
.model-selector .bootstrap-select .dropdown-menu {
    min-height: auto !important; /* 覆盖默认的最小高度 */
    height: auto !important;
}

/* 覆盖内部容器的最小高度 */
.model-selector .bootstrap-select .dropdown-menu .inner {
    min-height: auto !important;
    height: auto !important;
}

/* 确保下拉菜单只占用必要的空间 */
.model-selector .bootstrap-select .dropdown-menu ul {
    padding-bottom: 0 !important;
}
