package com.zibbava.edgemind.cortex.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * 统一聊天请求对象
 */
@Data
public class UnifiedChatRequest {
    // 普通对话参数
    @NotBlank(message = "提示词不能为空")
    @Size(max = 10000, message = "提示词不能超过10000个字符")
    private String prompt;
    
    @NotBlank(message = "模型名称不能为空")
    private String model;
    
    private MultipartFile image;
    private Long conversationId;
    private String title;
    
    // 知识库对话参数
    private String knowledgeNodeId;
    
    // 深度思考参数
    private Boolean enableThinking = true; // 默认开启深度思考
    
    // 增强检索参数
    private Boolean enableEnhancedRetrieval = true; // 默认开启增强检索
}
