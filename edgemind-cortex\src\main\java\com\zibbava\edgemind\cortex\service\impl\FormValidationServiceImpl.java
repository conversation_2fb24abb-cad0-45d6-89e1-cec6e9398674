package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.cortex.dto.ValidationRequest;
import com.zibbava.edgemind.cortex.dto.ValidationResponse;
import com.zibbava.edgemind.cortex.entity.User;
import com.zibbava.edgemind.cortex.entity.Role;
import com.zibbava.edgemind.cortex.entity.Department;
import com.zibbava.edgemind.cortex.entity.Permission;
import com.zibbava.edgemind.cortex.mapper.UserMapper;
import com.zibbava.edgemind.cortex.service.FormValidationService;
import com.zibbava.edgemind.cortex.service.UserService;
import com.zibbava.edgemind.cortex.service.RoleService;
import com.zibbava.edgemind.cortex.service.DepartmentService;
import com.zibbava.edgemind.cortex.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.regex.Pattern;

/**
 * 表单验证服务实现类
 * 提供统一的表单验证功能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FormValidationServiceImpl implements FormValidationService {

    private final UserService userService;
    private final RoleService roleService;
    private final DepartmentService departmentService;
    private final PermissionService permissionService;

    // 验证规则常量
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{3,20}$");
    private static final Pattern PASSWORD_PATTERN = Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$");

    @Override
    public ValidationResponse validateForm(ValidationRequest request) {
        String formType = request.getFormType();
        String mode = request.getValidationMode();
        Map<String, Object> formData = request.getFormData();
        
        switch (formType.toLowerCase()) {
            case "user":
                Long excludeUserId = getExcludeId(request.getExtraParams(), "userId");
                return validateUserForm(formData, mode, excludeUserId);
            case "role":
                Long excludeRoleId = getExcludeId(request.getExtraParams(), "roleId");
                return validateRoleForm(formData, mode, excludeRoleId);
            case "department":
                Long excludeDeptId = getExcludeId(request.getExtraParams(), "deptId");
                return validateDepartmentForm(formData, mode, excludeDeptId);
            case "permission":
                Long excludePermissionId = getExcludeId(request.getExtraParams(), "permissionId");
                return validatePermissionForm(formData, mode, excludePermissionId);
            default:
                return ValidationResponse.error("不支持的表单类型: " + formType);
        }
    }

    @Override
    public ValidationResponse validateUserForm(Map<String, Object> formData, String mode, Long excludeUserId) {
        ValidationResponse response = new ValidationResponse();
        
        // 验证用户名
        String username = getString(formData, "username");
        if (!StringUtils.hasText(username)) {
            response.addFieldError("username", "用户名不能为空");
        } else if (!USERNAME_PATTERN.matcher(username).matches()) {
            response.addFieldError("username", "用户名只能包含字母、数字和下划线，长度3-20位");
        } else if (!isFieldUnique("sys_user", "username", username, excludeUserId)) {
            response.addFieldError("username", "用户名已存在");
        }
        
        // 验证密码（新增时必填）
        String password = getString(formData, "password");
        if ("create".equals(mode)) {
            if (!StringUtils.hasText(password)) {
                response.addFieldError("password", "密码不能为空");
            } else if (!PASSWORD_PATTERN.matcher(password).matches()) {
                response.addFieldError("password", "密码至少8位，包含大小写字母和数字");
            }
        } else if (StringUtils.hasText(password) && !PASSWORD_PATTERN.matcher(password).matches()) {
            response.addFieldError("password", "密码至少8位，包含大小写字母和数字");
        }
        
        // 验证邮箱
        String email = getString(formData, "email");
        if (StringUtils.hasText(email)) {
            if (!EMAIL_PATTERN.matcher(email).matches()) {
                response.addFieldError("email", "邮箱格式不正确");
            } else if (!isFieldUnique("sys_user", "email", email, excludeUserId)) {
                response.addFieldError("email", "邮箱已被使用");
            }
        }
        
        // 验证手机号
        String phone = getString(formData, "phone");
        if (StringUtils.hasText(phone)) {
            if (!PHONE_PATTERN.matcher(phone).matches()) {
                response.addFieldError("phone", "手机号格式不正确");
            } else if (!isFieldUnique("sys_user", "phone", phone, excludeUserId)) {
                response.addFieldError("phone", "手机号已被使用");
            }
        }
        
        // 验证昵称
        String nickname = getString(formData, "nickname");
        if (!StringUtils.hasText(nickname)) {
            response.addFieldError("nickname", "昵称不能为空");
        } else if (nickname.length() > 50) {
            response.addFieldError("nickname", "昵称长度不能超过50个字符");
        }
        
        return response.hasFieldErrors() ? response : ValidationResponse.success("用户信息验证通过");
    }

    @Override
    public ValidationResponse validateRoleForm(Map<String, Object> formData, String mode, Long excludeRoleId) {
        ValidationResponse response = new ValidationResponse();
        
        // 验证角色名称
        String roleName = getString(formData, "roleName");
        if (!StringUtils.hasText(roleName)) {
            response.addFieldError("roleName", "角色名称不能为空");
        } else if (roleName.length() > 50) {
            response.addFieldError("roleName", "角色名称长度不能超过50个字符");
        } else if (roleService.isRoleNameExists(roleName, excludeRoleId)) {
            response.addFieldError("roleName", "角色名称已存在");
        }
        
        // 验证角色编码
        String roleCode = getString(formData, "roleCode");
        if (!StringUtils.hasText(roleCode)) {
            response.addFieldError("roleCode", "角色编码不能为空");
        } else if (!Pattern.matches("^[A-Z_]{2,20}$", roleCode)) {
            response.addFieldError("roleCode", "角色编码只能包含大写字母和下划线，长度2-20位");
        } else if (roleService.isRoleCodeExists(roleCode, excludeRoleId)) {
            response.addFieldError("roleCode", "角色编码已存在");
        }
        
        // 验证描述
        String description = getString(formData, "description");
        if (StringUtils.hasText(description) && description.length() > 200) {
            response.addFieldError("description", "描述长度不能超过200个字符");
        }
        
        return response.hasFieldErrors() ? response : ValidationResponse.success("角色信息验证通过");
    }

    @Override
    public ValidationResponse validateDepartmentForm(Map<String, Object> formData, String mode, Long excludeDeptId) {
        ValidationResponse response = new ValidationResponse();
        
        // 验证部门名称
        String deptName = getString(formData, "deptName");
        if (!StringUtils.hasText(deptName)) {
            response.addFieldError("deptName", "部门名称不能为空");
        } else if (deptName.length() > 50) {
            response.addFieldError("deptName", "部门名称长度不能超过50个字符");
        } else if (!isFieldUnique("sys_department", "dept_name", deptName, excludeDeptId)) {
            response.addFieldError("deptName", "部门名称已存在");
        }
        
        // 验证部门编码
        String deptCode = getString(formData, "deptCode");
        if (!StringUtils.hasText(deptCode)) {
            response.addFieldError("deptCode", "部门编码不能为空");
        } else if (!Pattern.matches("^[A-Z0-9_]{2,20}$", deptCode)) {
            response.addFieldError("deptCode", "部门编码只能包含大写字母、数字和下划线，长度2-20位");
        } else if (!isFieldUnique("sys_department", "dept_code", deptCode, excludeDeptId)) {
            response.addFieldError("deptCode", "部门编码已存在");
        }
        
        // 验证联系电话
        String contactPhone = getString(formData, "contactPhone");
        if (StringUtils.hasText(contactPhone) && !Pattern.matches("^\\d{3,4}-?\\d{7,8}$|^1[3-9]\\d{9}$", contactPhone)) {
            response.addFieldError("contactPhone", "联系电话格式不正确");
        }
        
        // 验证联系邮箱
        String contactEmail = getString(formData, "contactEmail");
        if (StringUtils.hasText(contactEmail) && !EMAIL_PATTERN.matcher(contactEmail).matches()) {
            response.addFieldError("contactEmail", "联系邮箱格式不正确");
        }
        
        return response.hasFieldErrors() ? response : ValidationResponse.success("部门信息验证通过");
    }

    @Override
    public ValidationResponse validatePermissionForm(Map<String, Object> formData, String mode, Long excludePermissionId) {
        ValidationResponse response = new ValidationResponse();
        
        // 验证权限名称
        String permissionName = getString(formData, "permissionName");
        if (!StringUtils.hasText(permissionName)) {
            response.addFieldError("permissionName", "权限名称不能为空");
        } else if (permissionName.length() > 50) {
            response.addFieldError("permissionName", "权限名称长度不能超过50个字符");
        }
        
        // 验证权限编码
        String permissionCode = getString(formData, "permissionCode");
        if (!StringUtils.hasText(permissionCode)) {
            response.addFieldError("permissionCode", "权限编码不能为空");
        } else if (!Pattern.matches("^[a-z]+:[a-z]+:[a-z]+$", permissionCode)) {
            response.addFieldError("permissionCode", "权限编码格式应为：模块:功能:操作（如：user:manage:create）");
        } else if (!isFieldUnique("sys_permission", "permission_code", permissionCode, excludePermissionId)) {
            response.addFieldError("permissionCode", "权限编码已存在");
        }
        
        return response.hasFieldErrors() ? response : ValidationResponse.success("权限信息验证通过");
    }

    @Override
    public boolean isFieldUnique(String tableName, String fieldName, Object fieldValue, Long excludeId) {
        // 这里应该使用通用的数据库查询来检查唯一性
        // 为了简化，这里使用具体的服务方法
        try {
            switch (tableName) {
                case "sys_user":
                    if ("username".equals(fieldName)) {
                        return !userService.isUsernameExists((String) fieldValue, excludeId);
                    } else if ("email".equals(fieldName)) {
                        return !userService.isEmailExists((String) fieldValue, excludeId);
                    } else if ("phone".equals(fieldName)) {
                        return !userService.isPhoneExists((String) fieldValue, excludeId);
                    }
                    break;
                case "sys_role":
                    if ("role_name".equals(fieldName)) {
                        return !roleService.isRoleNameExists((String) fieldValue, excludeId);
                    } else if ("role_code".equals(fieldName)) {
                        return !roleService.isRoleCodeExists((String) fieldValue, excludeId);
                    }
                    break;
                // 可以继续添加其他表的检查
            }
            return true;
        } catch (Exception e) {
            log.error("检查字段唯一性失败: table={}, field={}, value={}", tableName, fieldName, fieldValue, e);
            return false;
        }
    }

    private Long getExcludeId(Map<String, Object> extraParams, String key) {
        if (extraParams == null || !extraParams.containsKey(key)) {
            return null;
        }
        Object value = extraParams.get(key);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString().trim() : null;
    }
}
