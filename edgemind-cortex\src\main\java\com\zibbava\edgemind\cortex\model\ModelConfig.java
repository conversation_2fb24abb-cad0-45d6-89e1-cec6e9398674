package com.zibbava.edgemind.cortex.model;

import java.util.Map;

/**
 * 模型配置类，用于存储模型信息
 */
public class ModelConfig {
    private String value;  // 模型标识符
    private String name;   // 模型名称
    private ModelType type; // 模型类型
    
    public ModelConfig(String value, String name, ModelType type) {
        this.value = value;
        this.name = name;
        this.type = type;
    }
    
    public String getValue() {
        return value;
    }
    
    public String getName() {
        return name;
    }
    
    public ModelType getType() {
        return type;
    }
    
    /**
     * 创建一个Map表示的模型信息
     */
    public Map<String, String> toMap() {
        return Map.of("value", value, "name", name);
    }
} 