package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.Department;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 部门 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DepartmentMapper extends BaseMapper<Department> {

    /**
     * 根据父部门ID查询子部门列表
     * 
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    @Select("SELECT * FROM sys_department WHERE parent_id = #{parentId} AND status = 1 ORDER BY sort_order, id")
    List<Department> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 查询部门树（包含负责人信息）
     * 
     * @return 部门列表
     */
    @Select("SELECT d.*, u.nickname as manager_name FROM sys_department d " +
            "LEFT JOIN sys_user u ON d.manager_id = u.id " +
            "WHERE d.status = 1 ORDER BY d.sort_order, d.id")
    List<Department> selectDepartmentTreeWithManager();

    /**
     * 统计部门下的用户数量
     * 
     * @param deptId 部门ID
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM sys_user WHERE dept_id = #{deptId}")
    Long countUsersByDeptId(@Param("deptId") Long deptId);

    /**
     * 统计部门下的子部门数量
     * 
     * @param deptId 部门ID
     * @return 子部门数量
     */
    @Select("SELECT COUNT(*) FROM sys_department WHERE parent_id = #{deptId}")
    Long countChildDepartments(@Param("deptId") Long deptId);

    /**
     * 获取部门的所有祖先部门ID
     * 
     * @param deptId 部门ID
     * @return 祖先部门ID列表
     */
    @Select("<script>" +
            "WITH RECURSIVE dept_ancestors AS (" +
            "  SELECT id, parent_id, dept_name, 0 as level FROM sys_department WHERE id = #{deptId} " +
            "  UNION ALL " +
            "  SELECT d.id, d.parent_id, d.dept_name, da.level + 1 " +
            "  FROM sys_department d " +
            "  INNER JOIN dept_ancestors da ON d.id = da.parent_id " +
            "  WHERE d.parent_id != 0 " +
            ") " +
            "SELECT id FROM dept_ancestors WHERE id != #{deptId} ORDER BY level DESC" +
            "</script>")
    List<Long> selectAncestorIds(@Param("deptId") Long deptId);

    /**
     * 获取部门的所有后代部门ID
     * 
     * @param deptId 部门ID
     * @return 后代部门ID列表
     */
    @Select("<script>" +
            "WITH RECURSIVE dept_descendants AS (" +
            "  SELECT id, parent_id, dept_name, 0 as level FROM sys_department WHERE id = #{deptId} " +
            "  UNION ALL " +
            "  SELECT d.id, d.parent_id, d.dept_name, dd.level + 1 " +
            "  FROM sys_department d " +
            "  INNER JOIN dept_descendants dd ON d.parent_id = dd.id " +
            ") " +
            "SELECT id FROM dept_descendants WHERE id != #{deptId}" +
            "</script>")
    List<Long> selectDescendantIds(@Param("deptId") Long deptId);
}
