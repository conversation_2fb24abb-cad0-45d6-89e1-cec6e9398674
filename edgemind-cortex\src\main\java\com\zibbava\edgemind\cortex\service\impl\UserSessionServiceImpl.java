package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zibbava.edgemind.cortex.entity.UserSession;
import com.zibbava.edgemind.cortex.mapper.UserSessionMapper;
import com.zibbava.edgemind.cortex.service.UserSessionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户会话服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserSessionServiceImpl extends ServiceImpl<UserSessionMapper, UserSession> implements UserSessionService {

    private final UserSessionMapper userSessionMapper;

    @Override
    @Transactional
    public UserSession createSession(Long userId, String token, String sessionId, String ipAddress, String userAgent, LocalDateTime expireTime) {
        UserSession session = new UserSession();
        session.setUserId(userId);
        session.setToken(token);
        session.setSessionId(sessionId);
        session.setIpAddress(ipAddress);
        session.setUserAgent(userAgent);
        session.setLoginTime(LocalDateTime.now());
        session.setLastAccessTime(LocalDateTime.now());
        session.setExpireTime(expireTime);
        session.setStatus(1); // 活跃状态
        
        this.save(session);
        return session;
    }

    @Override
    public UserSession getSessionByToken(String token) {
        return userSessionMapper.selectByToken(token);
    }

    @Override
    public UserSession getSessionBySessionId(String sessionId) {
        return userSessionMapper.selectBySessionId(sessionId);
    }

    @Override
    public List<UserSession> getActiveSessionsByUserId(Long userId) {
        return userSessionMapper.selectActiveSessionsByUserId(userId);
    }

    @Override
    @Transactional
    public void updateLastAccessTime(String sessionId, LocalDateTime lastAccessTime) {
        userSessionMapper.updateLastAccessTime(sessionId, lastAccessTime);
    }

    @Override
    @Transactional
    public void invalidateUserSessions(Long userId) {
        int count = userSessionMapper.invalidateUserSessions(userId);
        log.info("用户 {} 的 {} 个会话已失效", userId, count);
    }

    @Override
    @Transactional
    public void invalidateSession(String sessionId) {
        int count = userSessionMapper.invalidateSession(sessionId);
        if (count > 0) {
            log.info("会话 {} 已失效", sessionId);
        }
    }

    @Override
    @Transactional
    public int cleanExpiredSessions() {
        int count = userSessionMapper.cleanExpiredSessions();
        if (count > 0) {
            log.info("清理了 {} 个过期会话", count);
        }
        return count;
    }

    @Override
    public Long countOnlineUsers() {
        return userSessionMapper.countOnlineUsers();
    }

    @Override
    public boolean isExceedMaxConcurrentSessions(Long userId, int maxConcurrentSessions) {
        List<UserSession> activeSessions = getActiveSessionsByUserId(userId);
        return activeSessions.size() >= maxConcurrentSessions;
    }

    @Override
    @Transactional
    public void forceLogoutOldestSessions(Long userId, int keepCount) {
        List<UserSession> activeSessions = getActiveSessionsByUserId(userId);
        
        if (activeSessions.size() <= keepCount) {
            return;
        }
        
        // 按登录时间排序，保留最新的keepCount个会话
        activeSessions.sort((s1, s2) -> s2.getLoginTime().compareTo(s1.getLoginTime()));
        
        // 使多余的会话失效
        for (int i = keepCount; i < activeSessions.size(); i++) {
            invalidateSession(activeSessions.get(i).getSessionId());
        }
        
        log.info("用户 {} 超过最大并发会话数，已强制下线 {} 个最旧会话", userId, activeSessions.size() - keepCount);
    }
}
