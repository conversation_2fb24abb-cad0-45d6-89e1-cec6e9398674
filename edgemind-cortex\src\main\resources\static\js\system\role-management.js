/**
 * 角色管理页面JavaScript - 使用EdgeMind组件系统
 */

// 全局变量
let currentPage = 1;
let pageSize = 10;
let selectedRoleIds = [];
let permissionTree = [];

// 获取上下文路�?
const CONTEXT_PATH = window.location.pathname.startsWith('/wkg') ? '/wkg' : '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPage();
});

/**
 * 初始化页�?
 */
function initPage() {
    loadRoleList();
    bindEvents();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 搜索表单提交
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadRoleList();
    });

    // 全选复选框
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="roleCheckbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedRoles();
    });
}

/**
 * 统一的API请求方法 - 使用EdgeMind标准
 */
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include'
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(`${CONTEXT_PATH}${url}`, finalOptions);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.code !== 200) {
            throw new Error(data.message || '操作失败');
        }

        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

/**
 * 显示Toast提示 - 使用EdgeMind组件
 */
function showToast(message, type = 'info') {
    // 检查是否有EdgeMind的showToast函数
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        // 回退到简单的alert
        alert(`${type.toUpperCase()}: ${message}`);
    }
}

/**
 * 加载角色列表
 */
async function loadRoleList() {
    try {
        const searchParams = {
            pageNum: currentPage,
            pageSize: pageSize,
            roleName: document.getElementById('searchRoleName').value,
            roleCode: document.getElementById('searchRoleCode').value,
            status: document.getElementById('searchStatus').value
        };

        // 移除空值参�?
        Object.keys(searchParams).forEach(key => {
            if (!searchParams[key]) {
                delete searchParams[key];
            }
        });

        const queryString = new URLSearchParams(searchParams).toString();
        const data = await apiRequest(`/api/system/role/list?${queryString}`);

        renderRoleTable(data.data.records || []);
        renderPagination(data.data);
    } catch (error) {
        showToast('加载角色列表失败: ' + error.message, 'danger');
    }
}

/**
 * 渲染角色表格
 */
function renderRoleTable(roles) {
    const tbody = document.getElementById('roleTableBody');
    tbody.innerHTML = '';

    roles.forEach(role => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input" name="roleCheckbox" value="${role.id}" onchange="updateSelectedRoles()">
            </td>
            <td>${role.roleName}</td>
            <td><code>${role.roleCode}</code></td>
            <td>${role.description || '-'}</td>
            <td>
                <span class="badge status-badge ${role.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${role.status === 1 ? '启用' : '禁用'}
                </span>
            </td>
            <td>
                <span class="badge bg-info">${role.userCount || 0}</span>
            </td>
            <td>${formatDateTime(role.createTime)}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="editRole(${role.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="showPermissionModal(${role.id})" title="权限">
                        <i class="bi bi-key"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="showCopyModal(${role.id})" title="复制">
                        <i class="bi bi-files"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteRole(${role.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

/**
 * 渲染分页
 */
function renderPagination(pageData) {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';

    const totalPages = pageData.pages;
    const current = pageData.current;

    // 上一�?
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${current === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${current - 1})">上一�?/a>`;
    pagination.appendChild(prevLi);

    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(totalPages, current + 2);

    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === current ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    // 下一�?
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${current === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${current + 1})">下一�?/a>`;
    pagination.appendChild(nextLi);
}

/**
 * 切换页面
 */
function changePage(page) {
    if (page < 1) return;
    currentPage = page;
    loadRoleList();
}

/**
 * 更新选中的角�?
 */
function updateSelectedRoles() {
    const checkboxes = document.querySelectorAll('input[name="roleCheckbox"]:checked');
    selectedRoleIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
    
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    batchDeleteBtn.disabled = selectedRoleIds.length === 0;
}

/**
 * 显示创建角色模态框
 */
function showCreateModal() {
    document.getElementById('roleModalTitle').textContent = '新增角色';
    document.getElementById('roleForm').reset();
    document.getElementById('roleId').value = '';
    
    // 启用角色编码输入�?
    document.getElementById('roleCode').disabled = false;
    
    const modal = new bootstrap.Modal(document.getElementById('roleModal'));
    modal.show();
}

/**
 * 编辑角色
 */
function editRole(roleId) {
    fetch(`/wkg/api/system/role/${roleId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const role = data.data;
                document.getElementById('roleModalTitle').textContent = '编辑角色';
                document.getElementById('roleId').value = role.id;
                document.getElementById('roleName').value = role.roleName;
                document.getElementById('roleCode').value = role.roleCode;
                document.getElementById('status').value = role.status;
                document.getElementById('description').value = role.description || '';
                
                // 禁用角色编码输入框（编辑时不允许修改�?
                document.getElementById('roleCode').disabled = true;
                
                const modal = new bootstrap.Modal(document.getElementById('roleModal'));
                modal.show();
            } else {
                showToast('获取角色信息失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 保存角色
 */
async function saveRole() {
    const roleId = document.getElementById('roleId').value;
    const isEdit = !!roleId;

    // 表单验证
    if (!validateRoleForm()) {
        return;
    }

    const formData = {
        roleName: document.getElementById('roleName').value,
        roleCode: document.getElementById('roleCode').value,
        description: document.getElementById('description').value,
        status: parseInt(document.getElementById('status').value)
    };

    if (isEdit) {
        formData.id = parseInt(roleId);
    }

    try {
        const url = isEdit ? `/api/system/role/${roleId}` : '/api/system/role';
        const method = isEdit ? 'PUT' : 'POST';

        const data = await apiRequest(url, {
            method: method,
            body: JSON.stringify(formData)
        });

        if (data.code === 200) {
            showToast(isEdit ? '角色更新成功' : '角色创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('roleModal')).hide();
            loadRoleList();
        } else {
            showToast((isEdit ? '角色更新失败: ' : '角色创建失败: ') + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('保存失败: ' + error.message, 'danger');
    }
}

/**
 * 删除角色
 */
function deleteRole(roleId) {
    // 先检查是否可以删�?
    fetch(`/wkg/api/system/role/${roleId}/can-delete`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (!data.data) {
                    showToast('该角色下还有用户，无法删�?, 'warning');
                    return;
                }
                
                if (!confirm('确定要删除这个角色吗？此操作不可恢复�?)) {
                    return;
                }
                
                fetch(`/wkg/api/system/role/${roleId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('角色删除成功', 'success');
                        loadRoleList();
                    } else {
                        showToast('角色删除失败: ' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('网络错误，请稍后重试', 'danger');
                });
            } else {
                showToast('检查角色状态失�? ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 批量删除角色
 */
function batchDelete() {
    if (selectedRoleIds.length === 0) {
        showToast('请选择要删除的角色', 'warning');
        return;
    }
    
    if (!confirm(`确定要删除选中�?${selectedRoleIds.length} 个角色吗？此操作不可恢复。`)) {
        return;
    }
    
    fetch('/wkg/api/system/role/batch', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(selectedRoleIds)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('角色批量删除成功', 'success');
            selectedRoleIds = [];
            updateSelectedRoles();
            loadRoleList();
        } else {
            showToast('角色批量删除失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 表单验证
 */
function validateRoleForm() {
    const roleName = document.getElementById('roleName').value.trim();
    const roleCode = document.getElementById('roleCode').value.trim();
    
    if (!roleName) {
        showToast('请输入角色名�?, 'warning');
        return false;
    }
    
    if (!roleCode) {
        showToast('请输入角色编�?, 'warning');
        return false;
    }
    
    if (!/^[A-Z_]+$/.test(roleCode)) {
        showToast('角色编码只能包含大写字母和下划线', 'warning');
        return false;
    }
    
    return true;
}

/**
 * 重置搜索
 */
function resetSearch() {
    document.getElementById('searchForm').reset();
    currentPage = 1;
    loadRoleList();
}

/**
 * 格式化日期时�?
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

/**
 * 显示提示信息
 */
function showToast(message, type = 'info') {
    // 这里可以使用Toast或其他提示组�?
    alert(message);
}

/**
 * 显示权限分配模态框
 */
function showPermissionModal(roleId) {
    document.getElementById('permissionRoleId').value = roleId;

    // 加载权限�?
    fetch(`/wkg/api/system/role/permission-tree?roleId=${roleId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                permissionTree = data.data;
                renderPermissionTree(permissionTree);

                const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
                modal.show();
            } else {
                showToast('加载权限树失�? ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 渲染权限�?
 */
function renderPermissionTree(nodes, level = 0) {
    const container = document.getElementById('permissionTree');
    if (level === 0) {
        container.innerHTML = '';
    }

    nodes.forEach(node => {
        const div = document.createElement('div');
        div.className = `permission-node level-${level}`;

        const hasChildren = node.children && node.children.length > 0;
        const expandIcon = hasChildren ?
            `<i class="bi bi-chevron-right expand-icon me-1" onclick="toggleNode(this)" style="cursor: pointer;"></i>` :
            `<span class="me-3"></span>`;

        div.innerHTML = `
            ${expandIcon}
            <div class="form-check d-inline-block">
                <input class="form-check-input" type="checkbox" value="${node.id}"
                       id="perm_${node.id}" ${node.checked ? 'checked' : ''}
                       onchange="handlePermissionChange(this)">
                <label class="form-check-label" for="perm_${node.id}">
                    ${node.icon ? `<i class="${node.icon} me-1"></i>` : ''}
                    ${node.label}
                    <small class="text-muted">(${node.value})</small>
                </label>
            </div>
        `;

        container.appendChild(div);

        if (hasChildren) {
            const childContainer = document.createElement('div');
            childContainer.className = 'permission-children';
            childContainer.style.display = 'none';
            container.appendChild(childContainer);

            // 递归渲染子节�?
            renderPermissionTreeChildren(node.children, childContainer, level + 1);
        }
    });
}

/**
 * 渲染权限树子节点
 */
function renderPermissionTreeChildren(nodes, container, level) {
    nodes.forEach(node => {
        const div = document.createElement('div');
        div.className = `permission-node level-${level}`;

        const hasChildren = node.children && node.children.length > 0;
        const expandIcon = hasChildren ?
            `<i class="bi bi-chevron-right expand-icon me-1" onclick="toggleNode(this)" style="cursor: pointer;"></i>` :
            `<span class="me-3"></span>`;

        div.innerHTML = `
            ${expandIcon}
            <div class="form-check d-inline-block">
                <input class="form-check-input" type="checkbox" value="${node.id}"
                       id="perm_${node.id}" ${node.checked ? 'checked' : ''}
                       onchange="handlePermissionChange(this)">
                <label class="form-check-label" for="perm_${node.id}">
                    ${node.icon ? `<i class="${node.icon} me-1"></i>` : ''}
                    ${node.label}
                    <small class="text-muted">(${node.value})</small>
                </label>
            </div>
        `;

        container.appendChild(div);

        if (hasChildren) {
            const childContainer = document.createElement('div');
            childContainer.className = 'permission-children';
            childContainer.style.display = 'none';
            container.appendChild(childContainer);

            // 递归渲染子节�?
            renderPermissionTreeChildren(node.children, childContainer, level + 1);
        }
    });
}

/**
 * 切换节点展开/收起
 */
function toggleNode(icon) {
    const isExpanded = icon.classList.contains('bi-chevron-down');
    const childContainer = icon.parentElement.nextElementSibling;

    if (isExpanded) {
        icon.classList.remove('bi-chevron-down');
        icon.classList.add('bi-chevron-right');
        if (childContainer && childContainer.classList.contains('permission-children')) {
            childContainer.style.display = 'none';
        }
    } else {
        icon.classList.remove('bi-chevron-right');
        icon.classList.add('bi-chevron-down');
        if (childContainer && childContainer.classList.contains('permission-children')) {
            childContainer.style.display = 'block';
        }
    }
}

/**
 * 处理权限选择变化
 */
function handlePermissionChange(checkbox) {
    const isChecked = checkbox.checked;
    const permissionId = checkbox.value;

    // 如果选中，则选中所有父节点
    if (isChecked) {
        checkParentNodes(checkbox);
    }

    // 如果取消选中，则取消选中所有子节点
    if (!isChecked) {
        uncheckChildNodes(checkbox);
    }
}

/**
 * 选中父节�?
 */
function checkParentNodes(checkbox) {
    // 这里可以实现选中父节点的逻辑
    // 由于DOM结构的复杂性，这里简化处�?
}

/**
 * 取消选中子节�?
 */
function uncheckChildNodes(checkbox) {
    // 这里可以实现取消选中子节点的逻辑
    // 由于DOM结构的复杂性，这里简化处�?
}

/**
 * 展开全部节点
 */
function expandAll() {
    const expandIcons = document.querySelectorAll('.expand-icon.bi-chevron-right');
    expandIcons.forEach(icon => {
        toggleNode(icon);
    });
}

/**
 * 收起全部节点
 */
function collapseAll() {
    const expandIcons = document.querySelectorAll('.expand-icon.bi-chevron-down');
    expandIcons.forEach(icon => {
        toggleNode(icon);
    });
}

/**
 * 全选权�?
 */
function checkAll() {
    const checkboxes = document.querySelectorAll('#permissionTree input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

/**
 * 取消全选权�?
 */
function uncheckAll() {
    const checkboxes = document.querySelectorAll('#permissionTree input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

/**
 * 保存权限分配
 */
function savePermissions() {
    const roleId = document.getElementById('permissionRoleId').value;
    const checkboxes = document.querySelectorAll('#permissionTree input[type="checkbox"]:checked');
    const permissionIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

    fetch(`/wkg/api/system/role/${roleId}/permissions`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            roleId: parseInt(roleId),
            permissionIds: permissionIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('权限分配成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('permissionModal')).hide();
        } else {
            showToast('权限分配失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 显示复制角色模态框
 */
function showCopyModal(roleId) {
    document.getElementById('copySourceRoleId').value = roleId;
    document.getElementById('copyRoleForm').reset();

    const modal = new bootstrap.Modal(document.getElementById('copyRoleModal'));
    modal.show();
}

/**
 * 复制角色
 */
function copyRole() {
    const sourceRoleId = document.getElementById('copySourceRoleId').value;
    const newRoleName = document.getElementById('newRoleName').value.trim();
    const newRoleCode = document.getElementById('newRoleCode').value.trim();

    if (!newRoleName) {
        showToast('请输入新角色名称', 'warning');
        return;
    }

    if (!newRoleCode) {
        showToast('请输入新角色编码', 'warning');
        return;
    }

    if (!/^[A-Z_]+$/.test(newRoleCode)) {
        showToast('角色编码只能包含大写字母和下划线', 'warning');
        return;
    }

    fetch(`/wkg/api/system/role/${sourceRoleId}/copy?newRoleName=${encodeURIComponent(newRoleName)}&newRoleCode=${encodeURIComponent(newRoleCode)}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('角色复制成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('copyRoleModal')).hide();
            loadRoleList();
        } else {
            showToast('角色复制失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请稍后重试', 'danger');
    });
}
