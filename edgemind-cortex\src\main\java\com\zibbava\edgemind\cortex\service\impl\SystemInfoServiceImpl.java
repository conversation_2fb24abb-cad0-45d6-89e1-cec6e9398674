package com.zibbava.edgemind.cortex.service.impl;

import com.zibbava.edgemind.cortex.dto.SystemInfoResponse;
import com.zibbava.edgemind.cortex.service.SystemInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.GlobalMemory;
import oshi.hardware.GraphicsCard;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.FileSystem;
import oshi.software.os.OSFileStore;
import oshi.software.os.OperatingSystem;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 系统信息服务实现类
 * 用于获取本地系统硬件信息, 完全依赖OSHI库
 */
@Service
@Slf4j
public class SystemInfoServiceImpl implements SystemInfoService {

    private final SystemInfo systemInfo;
    private final HardwareAbstractionLayer hardwareAbstractionLayer;
    private final OperatingSystem operatingSystem;

    public SystemInfoServiceImpl() {
        log.info("Initializing SystemInfoService with OSHI.");
        this.systemInfo = new SystemInfo();
        this.hardwareAbstractionLayer = this.systemInfo.getHardware();
        this.operatingSystem = this.systemInfo.getOperatingSystem();
        log.info("OSHI Initialized. OS: {} {}", operatingSystem.getFamily(), operatingSystem.getVersionInfo().getVersion());
    }

    @Override
    public SystemInfoResponse getSystemInfo() {
        log.info("获取系统硬件信息 (OSHI)");
        try {
            SystemInfoResponse.CpuInfo cpuInfo = getCpuInfoInternal();
            SystemInfoResponse.MemoryInfo memoryInfo = getMemoryInfoInternal();
            SystemInfoResponse.GpuInfo gpuInfo = getGpuInfoInternal();
            SystemInfoResponse.StorageInfo storageInfo = getStorageInfoInternal();
            String recommendedModelSize = determineRecommendedModelSize(memoryInfo, gpuInfo);

            return SystemInfoResponse.builder()
                    .cpu(cpuInfo)
                    .memory(memoryInfo)
                    .gpu(gpuInfo)
                    .storage(storageInfo)
                    .recommendedModelSize(recommendedModelSize)
                    .build();
        } catch (Exception e) {
            log.error("使用OSHI获取系统信息失败", e);
            return getDefaultSystemInfo();
        }
    }

    /**
     * 获取CPU信息 (OSHI)
     */
    private SystemInfoResponse.CpuInfo getCpuInfoInternal() {
        try {
            CentralProcessor processor = hardwareAbstractionLayer.getProcessor();
            CentralProcessor.ProcessorIdentifier identifier = processor.getProcessorIdentifier();

            String cpuModel = identifier.getName().trim();
            // OSHI reports physical cores, which is usually what's meant by "cores"
            int cpuCores = processor.getPhysicalProcessorCount();
            // Logical processors are threads
            int cpuThreads = processor.getLogicalProcessorCount();

            // Fallback if OSHI returns invalid values (highly unlikely for these)
            if (cpuCores <= 0) {
                log.warn("OSHI returned {} physical cores, falling back to availableProcessors.", cpuCores);
                cpuCores = Runtime.getRuntime().availableProcessors();
            }
            if (cpuThreads <= 0) {
                log.warn("OSHI returned {} logical processors, falling back to availableProcessors.", cpuThreads);
                cpuThreads = Runtime.getRuntime().availableProcessors(); // May not be accurate for threads vs cores
            }


            log.info("CPU Info (OSHI): Model='{}', Cores={}, Threads={}", cpuModel, cpuCores, cpuThreads);

            return SystemInfoResponse.CpuInfo.builder()
                    .model(cpuModel)
                    .cores(cpuCores)
                    .threads(cpuThreads)
                    .build();
        } catch (Exception e) {
            log.warn("使用OSHI获取CPU信息失败，返回默认值", e);
            return SystemInfoResponse.CpuInfo.builder()
                    .model("未知 (OSHI Error)")
                    .cores(Runtime.getRuntime().availableProcessors())
                    .threads(Runtime.getRuntime().availableProcessors())
                    .build();
        }
    }

    /**
     * 获取内存信息 (OSHI)
     */
    private SystemInfoResponse.MemoryInfo getMemoryInfoInternal() {
        try {
            GlobalMemory memory = hardwareAbstractionLayer.getMemory();
            long totalMemoryBytes = memory.getTotal();
            long availableMemoryBytes = memory.getAvailable();

            double totalMemoryGB = totalMemoryBytes / (1024.0 * 1024.0 * 1024.0);
            double availableMemoryGB = availableMemoryBytes / (1024.0 * 1024.0 * 1024.0);

            String totalMemory = String.format("%.2f", totalMemoryGB);
            String availableMemory = String.format("%.2f", availableMemoryGB);

            log.info("内存信息 (OSHI): 总内存={}GB ({} bytes), 可用内存={}GB ({} bytes)",
                    totalMemory, totalMemoryBytes, availableMemory, availableMemoryBytes);

            return SystemInfoResponse.MemoryInfo.builder()
                    .totalBytes(totalMemoryBytes)
                    .availableBytes(availableMemoryBytes)
                    .total(totalMemory)
                    .available(availableMemory)
                    .build();
        } catch (Exception e) {
            log.warn("使用OSHI获取内存信息失败，返回默认值", e);
            return SystemInfoResponse.MemoryInfo.builder()
                    .totalBytes(8L * 1024 * 1024 * 1024)
                    .availableBytes(4L * 1024 * 1024 * 1024)
                    .total("8.00") // Consistent formatting
                    .available("4.00")
                    .build();
        }
    }

    /**
     * 获取GPU信息 (OSHI)
     */
    private SystemInfoResponse.GpuInfo getGpuInfoInternal() {
        String gpuModel = "未检测到GPU (OSHI)";
        long finalGpuMemoryBytes = 0;
        String finalGpuMemoryStr = "0.00";
        boolean gpuAvailable = false;

        try {
            log.info("==== OSHI GPU Detection START ====");
            List<GraphicsCard> graphicsCards = hardwareAbstractionLayer.getGraphicsCards();

            if (graphicsCards.isEmpty()) {
                log.info("OSHI未检测到任何显卡");
            } else {
                log.info("OSHI检测到 {} 个显卡:", graphicsCards.size());
                for (int i = 0; i < graphicsCards.size(); i++) {
                    GraphicsCard card = graphicsCards.get(i);
                    log.info("  Raw Card [{}]: Name='{}', Vendor='{}', VRAM (bytes)={}, DeviceID='{}', Version='{}'",
                            i, card.getName(), card.getVendor(), card.getVRam(), card.getDeviceId(), card.getVersionInfo());
                }

                GraphicsCard selectedGpu = null;

                for (GraphicsCard card : graphicsCards) {
                    String currentCardName = card.getName();
                    String currentCardVendor = card.getVendor().toLowerCase();
                    long currentCardVramBytes = card.getVRam();

                    boolean currentIsDiscrete = currentCardVendor.contains("nvidia") ||
                            currentCardVendor.contains("amd") || // Covers "amd" and "advanced micro devices"
                            currentCardVendor.contains("advanced micro devices");

                    log.info("  Evaluating Card: Name='{}', Vendor='{}', VRAM={} bytes, IsDiscrete={}",
                            currentCardName, currentCardVendor, currentCardVramBytes, currentIsDiscrete);

                    if (selectedGpu == null) {
                        selectedGpu = card;
                        log.info("    Selected initial GPU: '{}' (VRAM: {} bytes)", selectedGpu.getName(), selectedGpu.getVRam());
                    } else {
                        String selectedGpuVendor = selectedGpu.getVendor().toLowerCase();
                        boolean selectedIsDiscrete = selectedGpuVendor.contains("nvidia") ||
                                selectedGpuVendor.contains("amd") ||
                                selectedGpuVendor.contains("advanced micro devices");

                        if (currentIsDiscrete && !selectedIsDiscrete) {
                            // Current card is discrete, previously selected was not -> Prefer current
                            log.info("    Switching: Current discrete ('{}') preferred over previous integrated ('{}')", currentCardName, selectedGpu.getName());
                            selectedGpu = card;
                        } else if (currentIsDiscrete == selectedIsDiscrete) {
                            // Both are of the same type (both discrete or both integrated)
                            // Prefer the one with more VRAM
                            if (currentCardVramBytes > selectedGpu.getVRam()) {
                                log.info("    Switching: Current '{}' ({} bytes VRAM) preferred over previous '{}' ({} bytes VRAM) due to more VRAM (same type)",
                                        currentCardName, currentCardVramBytes, selectedGpu.getName(), selectedGpu.getVRam());
                                selectedGpu = card;
                            } else {
                                log.info("    Keeping: Previous '{}' ({} bytes VRAM) over current '{}' ({} bytes VRAM) (same type, less or equal VRAM)",
                                        selectedGpu.getName(), selectedGpu.getVRam(), currentCardName, currentCardVramBytes);
                            }
                        } else if (!currentIsDiscrete && selectedIsDiscrete) {
                            // Current card is integrated, previously selected was discrete -> Keep discrete
                            log.info("    Keeping: Previous discrete ('{}') preferred over current integrated ('{}')", selectedGpu.getName(), currentCardName);
                        }
                    }
                }

                if (selectedGpu != null) {
                    log.info("  Final Selected GPU (before adjustments): Name='{}', Vendor='{}', VRAM (bytes)={}",
                            selectedGpu.getName(), selectedGpu.getVendor(), selectedGpu.getVRam());

                    // --- 修改开始 ---
                    String rawCardName = selectedGpu.getName();
                    String cardVendor = selectedGpu.getVendor();

                    // 1. 清理 rawCardName 中可能存在的附加ID (以 '#' 开头的部分)
                    int hashIndex = rawCardName.indexOf('#');
                    if (hashIndex != -1) {
                        rawCardName = rawCardName.substring(0, hashIndex).trim();
                        log.info("    Cleaned card name (removed ID): '{}'", rawCardName);
                    }

                    // 2. 构建 gpuModel，避免重复 Vendor
                    // 如果清理后的 cardName 已经以 Vendor 开头 (不区分大小写)，则不再添加 Vendor
                    if (rawCardName.toLowerCase().startsWith(cardVendor.toLowerCase())) {
                        gpuModel = rawCardName;
                    } else {
                        gpuModel = (cardVendor + " " + rawCardName).trim();
                    }
                    // 移除可能由于trim产生的多余空格
                    gpuModel = gpuModel.replaceAll("\\s+", " ");

                    log.info("    Constructed GPU Model: '{}'", gpuModel);

                    finalGpuMemoryBytes = selectedGpu.getVRam(); // Get VRAM from the selected card

                    String selectedVendorLower = selectedGpu.getVendor().toLowerCase();

                    // Heuristics for Intel GPUs or GPUs reporting very high/low "dedicated" VRAM
                    if (selectedVendorLower.contains("intel")) {
                        if (finalGpuMemoryBytes == 0) {
                            log.warn("    Intel Adjustment: Selected Intel GPU '{}' reports 0 VRAM. Treating as minimal for LLM.", gpuModel);
                            // finalGpuMemoryBytes remains 0. gpuAvailable will be false later if finalGpuMemoryBytes <= threshold.
                        } else if (finalGpuMemoryBytes > (1L * 1024 * 1024 * 1024) && // e.g., > 1GB
                                finalGpuMemoryBytes >= (hardwareAbstractionLayer.getMemory().getTotal() / 4) && // and more than 1/4 of total system RAM
                                finalGpuMemoryBytes > (8L * 1024 * 1024 * 1024) ) { // and more than 8GB (absolute threshold for "very large shared")
                            log.warn("    Intel Adjustment: Selected Intel GPU '{}' reports {} bytes VRAM, which is large and likely shared system RAM. Effective VRAM for dedicated tasks might be lower. Keeping reported value for now.", gpuModel, finalGpuMemoryBytes);
                            // For now, we keep the reported value from OSHI, but the recommendation logic might need to be more nuanced for such cases.
                            // Or, you could cap it: e.g., finalGpuMemoryBytes = Math.min(finalGpuMemoryBytes, 2L * 1024 * 1024 * 1024); // Cap at 2GB "effective"
                        }
                    }
                    // For non-Intel discrete cards, we generally trust OSHI's VRAM unless it's 0.
                    else if (finalGpuMemoryBytes == 0 && (selectedVendorLower.contains("nvidia") || selectedVendorLower.contains("amd"))){
                        log.warn("    Discrete GPU '{}' reported 0 VRAM. This is unusual. OSHI might not be able to retrieve VRAM info correctly.", gpuModel);
                        // finalGpuMemoryBytes remains 0.
                    }


                    // Determine availability based on a more meaningful threshold for LLMs
                    // If an Intel GPU reports a very small dedicated amount (e.g. 128MB), it's not practically "available"
                    // For discrete GPUs, if OSHI reports 0, it's also not "available".
                    // Let's use a threshold like 1GB.
                    if (finalGpuMemoryBytes > (1024L * 1024L * 1024L)) { // Greater than 1 GB
                        gpuAvailable = true;
                    } else {
                        gpuAvailable = false;
                        // If not available and VRAM was small but non-zero, log it.
                        // If VRAM was already 0, this log is redundant but harmless.
                        if (finalGpuMemoryBytes > 0) {
                            log.info("    Availability Check: GPU '{}' with {} bytes VRAM (<= 1GB) is considered NOT practically available for demanding LLM tasks.", gpuModel, finalGpuMemoryBytes);
                        } else {
                            log.info("    Availability Check: GPU '{}' with 0 bytes VRAM is considered NOT available.", gpuModel);
                        }
                        // Optionally, if not available, you might want to reset finalGpuMemoryBytes to 0 for consistency in display,
                        // though the `gpuAvailable` flag is the primary indicator.
                        // finalGpuMemoryBytes = 0; // This would make the output show "0.00 GB" if not available.
                    }

                    double gpuMemoryGB = finalGpuMemoryBytes / (1024.0 * 1024.0 * 1024.0);
                    finalGpuMemoryStr = String.format("%.2f", gpuMemoryGB);

                    log.info("  Final GPU decision: Model='{}', VRAM={}GB ({} bytes), Available for LLM: {}",
                            gpuModel, finalGpuMemoryStr, finalGpuMemoryBytes, gpuAvailable);
                } else {
                    log.info("  No suitable GPU was selected after evaluation.");
                }
            }
        } catch (Exception e) {
            log.error("使用OSHI获取GPU信息失败，将使用默认值", e);
            // Ensure default values are consistent if an error occurs mid-process
            gpuModel = "未检测到GPU (OSHI Error)";
            finalGpuMemoryBytes = 0;
            finalGpuMemoryStr = "0.00";
            gpuAvailable = false;
        }
        log.info("==== OSHI GPU Detection END ====");

        return SystemInfoResponse.GpuInfo.builder()
                .model(gpuModel)
                .memoryBytes(finalGpuMemoryBytes)
                .memory(finalGpuMemoryStr)
                .available(gpuAvailable)
                .build();
    }


    /**
     * 获取存储信息 (OSHI)
     * This will report for the file store containing the application's current working directory.
     */
    private SystemInfoResponse.StorageInfo getStorageInfoInternal() {
        long totalSpaceBytes = 0;
        long usableSpaceBytes = 0;
        String mountPoint = "N/A";

        try {
            FileSystem fileSystem = operatingSystem.getFileSystem();
            Path userDirPath = Paths.get(System.getProperty("user.dir"));
            OSFileStore storeForUserDir = null;
            long maxMountPathLength = -1;

            // Find the most specific file store for the current user directory
            // OSHI's getFileStores(true) gets local file stores only
            for (OSFileStore fs : fileSystem.getFileStores(true)) {
                try {
                    Path fsMountPath = Paths.get(fs.getMount());
                    if (userDirPath.startsWith(fsMountPath)) {
                        if (fs.getMount().length() > maxMountPathLength) {
                            storeForUserDir = fs;
                            maxMountPathLength = fs.getMount().length();
                        }
                    }
                } catch (Exception e) {
                    log.warn("Error processing filestore mount point '{}': {}", fs.getMount(), e.getMessage());
                }
            }

            if (storeForUserDir == null) { // Fallback if path-specific store not found
                List<OSFileStore> localFileStores = fileSystem.getFileStores(true);
                if(!localFileStores.isEmpty()){
                    // Try to find the root filesystem or a primary one
                    for(OSFileStore fs : localFileStores) {
                        if (fs.getMount().equals("/") || fs.getMount().matches("[A-Za-z]:\\\\?")) { // Root in Unix or C:\ in Windows
                            storeForUserDir = fs;
                            break;
                        }
                    }
                    if(storeForUserDir == null) storeForUserDir = localFileStores.get(0); // Last resort: first local
                }
            }


            if (storeForUserDir != null) {
                totalSpaceBytes = storeForUserDir.getTotalSpace();
                usableSpaceBytes = storeForUserDir.getUsableSpace();
                mountPoint = storeForUserDir.getMount();
                log.info("存储信息 (OSHI) for mount '{}': 总空间={} bytes, 可用空间={} bytes",
                        mountPoint, totalSpaceBytes, usableSpaceBytes);
            } else {
                log.warn("OSHI未能确定当前工作目录的文件存储。将使用Java File API作为后备（可能不准确或不完整）。");
                // Fallback to Java's built-in if OSHI fails, though this is what we want to avoid.
                // For consistency, let's return 0 if OSHI can't find it, and rely on default values.
                // Or use the old method as a last resort:
                File currentDirRoot = Paths.get(System.getProperty("user.dir")).toFile();
                while (currentDirRoot.getParentFile() != null && currentDirRoot.getTotalSpace() == 0) { // Find actual root
                    currentDirRoot = currentDirRoot.getParentFile();
                }
                totalSpaceBytes = currentDirRoot.getTotalSpace();
                usableSpaceBytes = currentDirRoot.getUsableSpace();
                mountPoint = currentDirRoot.getAbsolutePath();
                log.info("存储信息 (Java Fallback) for path '{}': 总空间={} bytes, 可用空间={} bytes",
                        mountPoint, totalSpaceBytes, usableSpaceBytes);
            }

            double totalSpaceGB = totalSpaceBytes / (1024.0 * 1024.0 * 1024.0);
            double usableSpaceGB = usableSpaceBytes / (1024.0 * 1024.0 * 1024.0);

            return SystemInfoResponse.StorageInfo.builder()
                    .totalBytes(totalSpaceBytes)
                    .availableBytes(usableSpaceBytes)
                    .total(String.format("%.2f", totalSpaceGB))
                    .available(String.format("%.2f", usableSpaceGB))
                    .build();

        } catch (Exception e) {
            log.warn("使用OSHI获取存储信息失败，返回默认值", e);
            return SystemInfoResponse.StorageInfo.builder()
                    .totalBytes(100L * 1024 * 1024 * 1024)
                    .availableBytes(50L * 1024 * 1024 * 1024)
                    .total("100.00")
                    .available("50.00")
                    .build();
        }
    }

    /**
     * 基于系统配置确定推荐模型大小
     * This logic remains the same, but consumes OSHI-sourced data.
     */
    private String determineRecommendedModelSize(SystemInfoResponse.MemoryInfo memoryInfo, SystemInfoResponse.GpuInfo gpuInfo) {
        double availableMemoryGB = memoryInfo.getAvailableBytes() / (1024.0 * 1024.0 * 1024.0);
        double gpuMemoryGB = gpuInfo.getMemoryBytes() / (1024.0 * 1024.0 * 1024.0);

        log.info("Determining model size (Billion Params, Consumer Friendly, Optimized/Quantized Implied): Available RAM={}GB, GPU VRAM={}GB, GPU Available={}",
                String.format("%.2f", availableMemoryGB),
                String.format("%.2f", gpuMemoryGB),
                gpuInfo.isAvailable());

        if (gpuInfo.isAvailable() && gpuMemoryGB > 1.8) { // 略微提高GPU可用门槛
            if (gpuMemoryGB >= 20) { // 20GB+ (如 RTX 3090/4090, RX 7900XTX)
                return "30B 至 70B+ 参数的大型模型 (GPU)"; // 70B Q4约38GB，24GB卡可以跑，但可能需要进一步优化或offload
            } else if (gpuMemoryGB >= 14) { // 14GB-19GB (如 RTX 4080, RX 6800XT/6900XT)
                return "13B 至 30B 参数的中大型模型 (GPU)";
            } else if (gpuMemoryGB >= 9) { // 9GB-13GB (如 RTX 4070/Ti 12GB, 3080 10/12GB, 3060 12GB)
                // 12GB VRAM 跑 12B/13B 量化模型是可行的
                return "7B 至 13B 参数的中型模型 (GPU)";
            } else if (gpuMemoryGB >= 5.5) { // 5.5GB-8GB (如 RTX 4060/Ti 8GB, 3060 8GB)
                // 8GB VRAM 跑 7B 量化模型是可行的
                return "3B 至 7B 参数的小型模型 (GPU)";
            } else { // 1.8GB - 5.5GB VRAM
                return "1B 至 3B 参数的微型模型 (GPU, 性能可能受限)";
            }
        } else { // CPU 场景 或 GPU 显存过小
            log.info("GPU显存不足或不可用，考虑CPU运行 (模型在CPU上会较慢).");
            if (availableMemoryGB >= 48) { // 48GB+ RAM
                return "7B 至 13B 参数的中型模型 (CPU运行, 速度较慢)";
            } else if (availableMemoryGB >= 28) { // 内存32GB及以上
                return "3B 至 7B 参数的小型模型 (CPU运行, 速度会较慢)";
            } else if (availableMemoryGB >= 12) { // 内存16GB左右
                return "1B 至 3B 参数的微型模型 (CPU运行, 速度非常慢)";
            } else { // 内存不足16GB
                return "运行当前AI模型可能较为吃力 (建议升级配置)";
            }
        }
    }

    /**
     * 获取默认系统信息
     */
    private SystemInfoResponse getDefaultSystemInfo() {
        log.warn("返回默认系统信息，因为OSHI获取失败或发生其他错误。");
        SystemInfoResponse.CpuInfo cpuInfo = SystemInfoResponse.CpuInfo.builder()
                .model("未知处理器 (Default)")
                .cores(4)
                .threads(8)
                .build();

        SystemInfoResponse.MemoryInfo memoryInfo = SystemInfoResponse.MemoryInfo.builder()
                .totalBytes(8L * 1024 * 1024 * 1024)
                .availableBytes(4L * 1024 * 1024 * 1024)
                .total("8.00")
                .available("4.00")
                .build();

        SystemInfoResponse.GpuInfo gpuInfo = SystemInfoResponse.GpuInfo.builder()
                .model("未检测到GPU (Default)")
                .memoryBytes(0)
                .memory("0.00")
                .available(false)
                .build();

        SystemInfoResponse.StorageInfo storageInfo = SystemInfoResponse.StorageInfo.builder()
                .totalBytes(100L * 1024 * 1024 * 1024)
                .availableBytes(50L * 1024 * 1024 * 1024)
                .total("100.00")
                .available("50.00")
                .build();

        return SystemInfoResponse.builder()
                .cpu(cpuInfo)
                .memory(memoryInfo)
                .gpu(gpuInfo)
                .storage(storageInfo)
                .recommendedModelSize("小型 3-7B 模型 (Default Config)")
                .build();
    }
}