package com.zibbava.edgemind.server.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 许可证记录DTO
 */
@Data
public class LicenseRecordDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 许可证密钥
     */
    private String licenseKey;

    /**
     * 硬件指纹
     */
    private String hardwareFingerprint;

    /**
     * 系统标识符
     */
    private String systemIdentifier;

    /**
     * 许可证类型：1-免费试用，2-管理员生成
     */
    private Integer licenseType;

    /**
     * 许可证类型描述
     */
    private String licenseTypeDesc;

    /**
     * 状态：0-未激活，1-已激活，2-已过期，3-已禁用
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 激活时间
     */
    private LocalDateTime activatedTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 生成者
     */
    private String generatedBy;

    /**
     * 客户端信息
     */
    private String clientInfo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否即将过期（7天内）
     */
    private Boolean expiringSoon;

    /**
     * 剩余天数
     */
    private Long remainingDays;
}