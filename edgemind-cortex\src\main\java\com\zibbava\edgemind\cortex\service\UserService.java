package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.dto.LoginResponse;
import com.zibbava.edgemind.cortex.entity.User;

public interface UserService extends IService<User> {

    /**
     * 用户登录
     *
     * @param username 用户名
     * @param password 原始密码
     * @return LoginResponse 包含 Token 和用户信息
     */
    LoginResponse login(String username, String password);

    /**
     * 用户登出
     */
    void logout();

}