package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.dto.LoginResponse;
import com.zibbava.edgemind.cortex.entity.User;

public interface UserService extends IService<User> {

    /**
     * 用户登录
     *
     * @param username 用户名
     * @param password 原始密码
     * @return LoginResponse 包含 Token 和用户信息
     */
    LoginResponse login(String username, String password);

    /**
     * 用户登出
     */
    void logout();

    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    boolean isUsernameExists(String username, Long excludeUserId);

    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    boolean isEmailExists(String email, Long excludeUserId);

    /**
     * 检查手机号是否存在
     * @param phone 手机号
     * @param excludeUserId 排除的用户ID
     * @return 是否存在
     */
    boolean isPhoneExists(String phone, Long excludeUserId);

}