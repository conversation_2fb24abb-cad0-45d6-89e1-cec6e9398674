package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.entity.Permission;

import java.util.List;

public interface PermissionService extends IService<Permission> {

    /**
     * 根据角色ID列表查询权限列表
     * @param roleIds 角色ID列表
     * @return 权限列表
     */
    List<Permission> findPermissionsByRoleIds(List<Long> roleIds);

    /**
     * 获取权限树 (通常用于前端展示)
     * @return 权限树结构的列表
     */
    List<Permission> getPermissionTree();

    // 可根据需要添加其他方法，如根据用户ID查询菜单权限等
} 