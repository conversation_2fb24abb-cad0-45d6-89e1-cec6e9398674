package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.entity.Permission;

import java.util.List;

public interface PermissionService extends IService<Permission> {

    /**
     * 根据角色ID列表查询权限列表
     * @param roleIds 角色ID列表
     * @return 权限列表
     */
    List<Permission> findPermissionsByRoleIds(List<Long> roleIds);

    /**
     * 获取权限树 (通常用于前端展示)
     * @return 权限树结构的列表
     */
    List<Permission> getPermissionTree();

    /**
     * 获取用户权限列表
     * @param userId 用户ID
     * @return 权限编码列表
     */
    List<String> getUserPermissions(Long userId);

    /**
     * 检查用户是否有指定权限
     * @param userId 用户ID
     * @param permission 权限编码
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, String permission);

    /**
     * 清除用户权限缓存
     * @param userId 用户ID
     */
    void clearUserPermissionCache(Long userId);

    /**
     * 获取用户最后权限变更时间
     * @param userId 用户ID
     * @return 时间戳
     */
    Long getLastPermissionChangeTime(Long userId);

    // 可根据需要添加其他方法，如根据用户ID查询菜单权限等
}