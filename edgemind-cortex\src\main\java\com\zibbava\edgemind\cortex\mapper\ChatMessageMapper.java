package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.ChatMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 聊天消息Mapper接口
 */
@Mapper
public interface ChatMessageMapper extends BaseMapper<ChatMessage> {

    /**
     * 根据会话ID查询所有消息，按创建时间升序排列
     * 
     * @param conversationId 会话ID
     * @return 消息列表
     */
    @Select("SELECT * FROM chat_message WHERE conversation_id = #{conversationId} ORDER BY create_time ASC")
    List<ChatMessage> selectByConversationId(@Param("conversationId") Long conversationId);
    
    /**
     * 根据会话ID查询最近的N条消息，按创建时间升序排列
     * 
     * @param conversationId 会话ID
     * @param limit 限制条数
     * @return 消息列表
     */
    @Select("SELECT * FROM (SELECT * FROM chat_message WHERE conversation_id = #{conversationId} ORDER BY id DESC LIMIT #{limit}) sub ORDER BY id ASC")
    List<ChatMessage> selectRecentByConversationId(@Param("conversationId") Long conversationId, @Param("limit") Integer limit);

    /**
     * 分页查询会话的消息，固定按创建时间倒序排列（最新的消息在前）
     * 
     * @param conversationId 会话ID
     * @param offset 偏移量（跳过记录数）
     * @param limit 限制条数
     * @return 消息列表
     */
    @Select("SELECT * FROM chat_message WHERE conversation_id = #{conversationId} ORDER BY create_time DESC,id LIMIT #{limit} OFFSET #{offset}")
    List<ChatMessage> selectPagedByConversationIdDesc(
        @Param("conversationId") Long conversationId, 
        @Param("offset") Integer offset, 
        @Param("limit") Integer limit);

    /**
     * 获取会话消息的总数
     * 
     * @param conversationId 会话ID
     * @return 消息总数
     */
    @Select("SELECT COUNT(*) FROM chat_message WHERE conversation_id = #{conversationId}")
    int countByConversationId(@Param("conversationId") Long conversationId);
} 