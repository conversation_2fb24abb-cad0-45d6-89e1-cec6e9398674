package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@TableName("sys_department")
public class Department {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("dept_name")
    private String deptName; // 部门名称

    @TableField("dept_code")
    private String deptCode; // 部门编码

    @TableField("parent_id")
    private Long parentId; // 父部门ID

    @TableField("dept_level")
    private Integer deptLevel; // 部门层级

    @TableField("dept_path")
    private String deptPath; // 部门路径(用于快速查询)

    @TableField("manager_id")
    private Long managerId; // 部门负责人ID (关联 User 表)

    @TableField("sort_order")
    private Integer sortOrder; // 显示顺序

    @TableField("status")
    private Integer status; // 状态 (0: 禁用, 1: 启用)

    @TableField("description")
    private String description; // 部门描述/职责

    @TableField("contact_phone")
    private String contactPhone; // 部门联系电话

    @TableField("contact_email")
    private String contactEmail; // 部门联系邮箱

    @TableField("address")
    private String address; // 部门地址

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    // 添加软删除标记字段
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 删除时间 (软删除时记录删除时间)
    @TableField("delete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deleteTime;

    // 删除操作人ID
    @TableField("delete_user_id")
    private Long deleteUserId;

    // ----- 关联关系 -----

    // 子部门 (用于构建树形结构)
    @TableField(exist = false)
    private List<Department> children;

    // 部门负责人信息 (查询时填充)
    @TableField(exist = false)
    private User manager; // 对应 managerId

    // 上级部门名称 (查询时填充)
    @TableField(exist = false)
    private String parentName;
}