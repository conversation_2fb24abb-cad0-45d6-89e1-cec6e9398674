package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.cortex.common.constants.SettingKeys;
import com.zibbava.edgemind.cortex.common.enums.NodeType;
import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.dto.knowledgebase.KnowledgeNodeDto;
import com.zibbava.edgemind.cortex.dto.knowledgebase.NodeCreateRequest;
import com.zibbava.edgemind.cortex.entity.KnowledgeDocument;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import com.zibbava.edgemind.cortex.mapper.KnowledgeDocumentMapper;
import com.zibbava.edgemind.cortex.mapper.KnowledgeNodeMapper;
import com.zibbava.edgemind.cortex.mapper.KnowledgeSpaceMapper;
import com.zibbava.edgemind.cortex.mapper.SystemSettingsMapper;
import com.zibbava.edgemind.cortex.service.DocumentIndexingService;
import com.zibbava.edgemind.cortex.service.KnowledgeBaseService;
import com.zibbava.edgemind.cortex.service.KnowledgeNodeClosureService;
import com.zibbava.edgemind.cortex.service.SystemSettingsService;
import com.zibbava.edgemind.cortex.util.FileUtils;
import com.zibbava.edgemind.cortex.util.MediaUtils;
import com.zibbava.edgemind.cortex.util.MockMultipartFile;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 系统设置服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SystemSettingsServiceImpl implements SystemSettingsService {

    private final SystemSettingsMapper systemSettingsMapper;
    @Lazy
    private final KnowledgeBaseService knowledgeBaseService;
    private final DocumentIndexingService indexingService;
    private final KnowledgeSpaceMapper spaceMapper;
    private final KnowledgeNodeMapper nodeMapper;
    private final KnowledgeDocumentMapper documentMapper;
    private final KnowledgeNodeClosureService closureService;

    @Value("${run.env:dev}")
    private String runEnv;

    private final String defaultUploadDir = System.getProperty("user.dir") + "/knowledge_files";

    /**
     * 获取系统唯一标识
     * 如果不存在则生成一个新的
     *
     * @return 系统唯一标识
     */
    @Override
    public String getSystemIdentifier() {
        // 从数据库获取
        return getSettingValue(SettingKeys.System.IDENTIFIER);
    }

    /**
     * 获取知识库存储路径
     *
     * @return 知识库存储路径
     */
    @Override
    public String getKnowledgeStoragePath() {
        // 从数据库获取
        String storagePath = getSettingValue(SettingKeys.KnowledgeBase.STORAGE_PATH);
        if (StringUtils.hasText(storagePath)) {
            return storagePath;
        }

        // 如果数据库中没有，返回默认路径
        return Paths.get(defaultUploadDir, "team").toString();
    }

    /**
     * 获取个人库存储路径
     *
     * @return 个人库存储路径
     */
    @Override
    public String getPersonalKnowledgeStoragePath() {
        // 从数据库获取
        String storagePath = getSettingValue(SettingKeys.KnowledgeBase.PERSONAL_STORAGE_PATH);
        if (StringUtils.hasText(storagePath)) {
            return storagePath;
        }

        // 如果数据库中没有，返回默认路径
        return Paths.get(defaultUploadDir, "personal").toString();
    }

    /**
     * 验证知识库路径是否合法
     * 防止知识库和个人库路径冲突
     *
     * @param path       要验证的路径
     * @param isTeamPath 是否是知识库路径
     * @throws IllegalArgumentException 如果路径不合法
     */
    private void validateKnowledgeBasePath(String path, boolean isTeamPath) {
        if (!StringUtils.hasText(path)) {
            throw new IllegalArgumentException("存储路径不能为空");
        }

        // 获取当前的团队和个人库路径
        String currentTeamPath = getSettingValue(SettingKeys.KnowledgeBase.STORAGE_PATH);
        String currentPersonalPath = getSettingValue(SettingKeys.KnowledgeBase.PERSONAL_STORAGE_PATH);

        // 如果是知识库路径
        if (isTeamPath) {
            // 检查是否与个人库路径相同或是其子目录
            if (StringUtils.hasText(currentPersonalPath)) {
                Path personalPath = Paths.get(currentPersonalPath).normalize().toAbsolutePath();
                Path newPath = Paths.get(path).normalize().toAbsolutePath();

                if (newPath.equals(personalPath) || newPath.startsWith(personalPath)) {
                    throw new IllegalArgumentException("知识库路径不能设置为个人库路径或其子目录");
                }
            }
        } else {
            // 如果是个人库路径
            // 检查是否与知识库路径相同或是其子目录
            if (StringUtils.hasText(currentTeamPath)) {
                Path teamPath = Paths.get(currentTeamPath).normalize().toAbsolutePath();
                Path newPath = Paths.get(path).normalize().toAbsolutePath();

                if (newPath.equals(teamPath) || newPath.startsWith(teamPath)) {
                    throw new IllegalArgumentException("个人库路径不能设置为知识库路径或其子目录");
                }
            }
        }
    }

    /**
     * 更新知识库存储路径
     *
     * @param path 新的存储路径
     */
    @Override
    public void updateKnowledgeStoragePath(String path) {
        // 验证路径是否合法
        validateKnowledgeBasePath(path, true);

        // 验证路径是否有效
        Path storagePath = Paths.get(path);
        if (!Files.exists(storagePath)) {
            try {
                Files.createDirectories(storagePath);
                log.info("创建知识库存储目录: {}", storagePath);
            } catch (Exception e) {
                log.error("创建知识库存储目录失败: {}", e.getMessage());
                throw new RuntimeException("无法创建知识库存储目录: " + e.getMessage());
            }
        }

        log.info("更新知识库存储路径: {}", path);

        // 更新数据库中的路径
        updateSettingValue(SettingKeys.KnowledgeBase.STORAGE_PATH, path, "知识库存储路径");
    }

    /**
     * 更新个人库存储路径
     *
     * @param path 新的存储路径
     */
    @Override
    public void updatePersonalKnowledgeStoragePath(String path) {
        // 验证路径是否合法
        validateKnowledgeBasePath(path, false);

        // 验证路径是否有效
        Path storagePath = Paths.get(path);
        if (!Files.exists(storagePath)) {
            try {
                Files.createDirectories(storagePath);
                log.info("创建个人库存储目录: {}", storagePath);
            } catch (Exception e) {
                log.error("创建个人库存储目录失败: {}", e.getMessage());
                throw new RuntimeException("无法创建个人库存储目录: " + e.getMessage());
            }
        }

        log.info("更新个人库存储路径: {}", path);

        // 更新数据库中的路径
        updateSettingValue(SettingKeys.KnowledgeBase.PERSONAL_STORAGE_PATH, path, "个人库存储路径");
    }

    /**
     * 同步知识库
     * 从指定目录导入文件到知识库
     * 注意：只同步知识库，不同步个人库
     */
    @Override
    public void syncKnowledgeBase() {
        if ("demo".equals(runEnv)) {
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "演示环境不支持同步操作");
        }
        // 获取知识库存储路径
        String storagePath = getKnowledgeStoragePath();
        log.info("开始同步知识库，存储路径: {}", storagePath);

        // 获取个人库存储路径
        String personalStoragePath = getPersonalKnowledgeStoragePath();

        // 安全检查：确保知识库路径与个人库路径不冲突
        Path teamPath = Paths.get(storagePath).normalize().toAbsolutePath();
        Path personalPath = Paths.get(personalStoragePath).normalize().toAbsolutePath();

        if (teamPath.equals(personalPath) || teamPath.startsWith(personalPath) || personalPath.startsWith(teamPath)) {
            String errorMsg = "安全错误：知识库路径与个人库路径冲突。\n" +
                    "团队路径：" + teamPath + "\n" +
                    "私人路径：" + personalPath + "\n" +
                    "请在设置页面修改路径后再尝试同步操作。";
            log.error(errorMsg);
            updateSyncStatus(SettingKeys.SyncStatus.FAILED, "同步失败: 知识库路径与个人库路径冲突");
            throw new IllegalStateException(errorMsg);
        }

        // 确保知识库目录存在
        if (!Files.exists(teamPath)) {
            try {
                Files.createDirectories(teamPath);
                log.info("创建知识库目录: {}", teamPath);
            } catch (IOException e) {
                log.error("创建知识库目录失败: {}", e.getMessage());
                throw new RuntimeException("创建知识库目录失败", e);
            }
        }

        try {
            // 更新同步状态为同步中
            updateSyncStatus(SettingKeys.SyncStatus.SYNCING, "正在同步知识库...");

            // 1. 清空当前知识库数据
            clearTeamKnowledgeBase();
            log.info("知识库数据清空完成");
            updateSyncStatus(SettingKeys.SyncStatus.SYNCING, "知识库数据清空完成，开始扫描文件...");

            // 2. 扫描指定目录下的所有文件
            // 3. 将文件导入到知识库
            importFilesFromDirectory(storagePath);

            // 更新同步状态为同步完成
            updateSyncStatus(SettingKeys.SyncStatus.COMPLETED, "知识库同步完成");
            log.info("知识库同步完成");
        } catch (Exception e) {
            // 更新同步状态为同步失败
            updateSyncStatus(SettingKeys.SyncStatus.FAILED, "同步失败: " + e.getMessage());
            log.error("知识库同步失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 清空知识库数据，使用分页处理避免内存压力
     * 包括团队空间的节点、文档和向量数据
     */
    private void clearTeamKnowledgeBase() {
        log.info("开始清空知识库数据");

        try {
            // 获取团队知识空间
            KnowledgeSpace teamSpace = spaceMapper.selectOne(
                    new LambdaQueryWrapper<KnowledgeSpace>()
                            .eq(KnowledgeSpace::getIsPrivate, false)
            );

            if (teamSpace == null) {
                log.info("团队知识空间不存在，无需清空");
                return;
            }

            String teamSpaceId = teamSpace.getSpaceId();
            log.info("清空团队知识空间数据: {}", teamSpaceId);

            // 分页大小
            final int pageSize = 500;
            int currentPage = 1;
            List<String> teamNodeIds;
            int totalProcessed = 0;

            do {
                // 1. 分页获取团队空间中的节点ID
                Page<KnowledgeNode> page = new Page<>(currentPage, pageSize);
                IPage<KnowledgeNode> nodePage = nodeMapper.selectPage(page,
                        new LambdaQueryWrapper<KnowledgeNode>()
                                .eq(KnowledgeNode::getSpaceId, teamSpaceId)
                                .select(KnowledgeNode::getNodeId)
                );

                teamNodeIds = nodePage.getRecords().stream()
                        .map(KnowledgeNode::getNodeId)
                        .collect(Collectors.toList());

                if (teamNodeIds.isEmpty()) {
                    if (currentPage == 1) {
                        log.info("团队知识空间中没有节点，无需清空");
                        return;
                    }
                    break; // 没有更多数据了
                }

                log.info("正在处理第 {} 页数据，共 {} 条记录", currentPage, teamNodeIds.size());
                totalProcessed += teamNodeIds.size();

                // 2. 清空这些节点关联的向量数据
                for (String nodeId : teamNodeIds) {
                    try {
                        indexingService.deleteVectors(nodeId);
                    } catch (Exception e) {
                        log.warn("删除节点 {} 的向量数据时出错: {}", nodeId, e.getMessage());
                    }
                }

                // 3. 删除这些节点关联的文档记录
                if (!teamNodeIds.isEmpty()) {
                    documentMapper.delete(
                            new LambdaQueryWrapper<KnowledgeDocument>()
                                    .in(KnowledgeDocument::getNodeId, teamNodeIds)
                    );
                }

                // 4. 删除这些节点的闭包关系
                for (String nodeId : teamNodeIds) {
                    closureService.deleteNodeClosure(nodeId);
                }

                // 5. 删除这些节点
                if (!teamNodeIds.isEmpty()) {
                    nodeMapper.delete(
                            new LambdaQueryWrapper<KnowledgeNode>()
                                    .in(KnowledgeNode::getNodeId, teamNodeIds)
                    );
                }

                log.info("第 {} 页数据处理完成", currentPage);
                currentPage++;

            } while (!teamNodeIds.isEmpty());

            log.info("知识库数据清空完成，共处理 {} 条记录", totalProcessed);
        } catch (Exception e) {
            log.error("清空知识库数据时出错: {}", e.getMessage(), e);
            throw new RuntimeException("清空知识库数据失败", e);
        }
    }

    /**
     * 从指定目录导入文件到知识库
     *
     * @param directoryPath 目录路径
     */
    private void importFilesFromDirectory(String directoryPath) {
        log.info("开始从目录 {} 导入文件", directoryPath);

        // 检查目录是否存在
        Path directory = Paths.get(directoryPath);
        if (!Files.exists(directory) || !Files.isDirectory(directory)) {
            log.error("指定的目录不存在或不是目录: {}", directoryPath);
            throw new RuntimeException("指定的目录不存在或不是目录: " + directoryPath);
        }

        try {
            // 1. 获取或创建团队知识空间
            KnowledgeSpace teamSpace = knowledgeBaseService.getOrCreateKnowledgeSpace(1L, false);
            log.info("使用团队知识空间: {}", teamSpace.getSpaceId());

            // 2. 递归导入文件
            importFilesRecursively(directory, teamSpace.getSpaceId(), null);

            log.info("文件导入完成");
        } catch (Exception e) {
            log.error("导入文件时出错: {}", e.getMessage(), e);
            throw new RuntimeException("导入文件时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 递归导入目录中的文件
     *
     * @param directory    当前目录
     * @param spaceId      知识空间ID
     * @param parentNodeId 父节点ID，根目录为null
     */
    private void importFilesRecursively(Path directory, String spaceId, String parentNodeId) {
        try {
            // 更新同步状态
            updateSyncStatus(SettingKeys.SyncStatus.SYNCING, "正在导入目录: " + directory.getFileName());

            // 遍历目录中的所有文件和子目录
            Files.list(directory).forEach(path -> {
                try {
                    String fileName = path.getFileName().toString();

                    // 跳过隐藏文件和系统文件
                    if (fileName.startsWith(".") || fileName.startsWith("~$")) {
                        return;
                    }

                    if (Files.isDirectory(path)) {
                        // 创建文件夹节点
                        NodeCreateRequest folderRequest = new NodeCreateRequest();
                        folderRequest.setSpaceId(spaceId);
                        folderRequest.setParentNodeId(parentNodeId);
                        folderRequest.setName(fileName);
                        folderRequest.setType(NodeType.FOLDER);

                        KnowledgeNodeDto folderNode = knowledgeBaseService.createNode(folderRequest);
                        log.info("创建文件夹节点: {}, nodeId={}", fileName, folderNode.getNodeId());

                        // 递归处理子目录
                        importFilesRecursively(path, spaceId, folderNode.getNodeId());
                    } else {
                        // 检查是否是支持的文件类型
                        if (MediaUtils.isSupportedFileType(fileName)) {
                            // 创建文件节点
                            NodeCreateRequest fileRequest = new NodeCreateRequest();
                            fileRequest.setSpaceId(spaceId);
                            fileRequest.setParentNodeId(parentNodeId);
                            fileRequest.setName(fileName);
                            fileRequest.setType(NodeType.FILE);

                            KnowledgeNodeDto fileNode = knowledgeBaseService.createNode(fileRequest);
                            log.info("创建文件节点: {}, nodeId={}", fileName, fileNode.getNodeId());

                            // 上传文件内容
                            uploadFileContent(path, fileNode.getNodeId());
                        } else {
                            log.info("跳过不支持的文件类型: {}", fileName);
                        }
                    }
                } catch (Exception e) {
                    log.error("处理文件/目录时出错: {}, 错误: {}", path, e.getMessage(), e);
                    // 继续处理其他文件
                }
            });
        } catch (IOException e) {
            log.error("读取目录内容时出错: {}, 错误: {}", directory, e.getMessage(), e);
            throw new RuntimeException("读取目录内容时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 上传文件内容
     *
     * @param filePath 文件路径
     * @param nodeId   节点ID
     * @throws IOException 如果文件读取或上传失败
     */
    private void uploadFileContent(Path filePath, String nodeId) throws IOException {
        try {
            // 创建MultipartFile对象
            String contentType = Files.probeContentType(filePath);
            if (contentType == null) {
                // 根据文件扩展名推断内容类型
                String fileName = filePath.getFileName().toString();
                String extension = MediaUtils.getFileExtension(fileName).toLowerCase();
                contentType = MediaUtils.getMimeTypeFromExtension(extension);
            }

            // 读取文件内容
            byte[] content = Files.readAllBytes(filePath);

            // 创建MultipartFile
            MultipartFile multipartFile = new MockMultipartFile(
                    filePath.getFileName().toString(),
                    filePath.getFileName().toString(),
                    contentType,
                    content
            );

            // 上传文件内容
            knowledgeBaseService.uploadDocument(nodeId, multipartFile, true);
            log.info("文件内容上传成功: {}, nodeId={}", filePath.getFileName(), nodeId);
        } catch (Exception e) {
            log.error("上传文件内容时出错: {}, nodeId={}, 错误: {}", filePath, nodeId, e.getMessage(), e);
            throw new IOException("上传文件内容时出错: " + e.getMessage(), e);
        }
    }

    /**
     * 获取知识库同步状态
     *
     * @return 同步状态信息的Map，包含 syncStatus、syncMessage 和 lastSyncTime
     */
    @Override
    public Map<String, Object> getKnowledgeSyncStatus() {
        Map<String, Object> status = new HashMap<>();

        try {
            // 获取同步状态
            String syncStatusStr = getSettingValue(SettingKeys.KnowledgeBase.SYNC_STATUS, "0");
            int syncStatus = Integer.parseInt(syncStatusStr);
            status.put("syncStatus", syncStatus);

            // 获取同步消息
            String syncMessage = getSettingValue(SettingKeys.KnowledgeBase.SYNC_MESSAGE, "未同步");
            status.put("syncMessage", syncMessage);

            // 获取最后同步时间
            String lastSyncTimeStr = getSettingValue(SettingKeys.KnowledgeBase.LAST_SYNC_TIME);
            LocalDateTime lastSyncTime = null;
            if (StringUtils.hasText(lastSyncTimeStr)) {
                try {
                    lastSyncTime = LocalDateTime.parse(lastSyncTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                } catch (Exception e) {
                    log.warn("解析最后同步时间失败: {}", e.getMessage());
                }
            }
            status.put("lastSyncTime", lastSyncTime);
        } catch (Exception e) {
            log.error("获取同步状态失败: {}", e.getMessage());
            // 返回默认值
            status.put("syncStatus", 0);
            status.put("syncMessage", "未同步");
            status.put("lastSyncTime", null);
        }

        return status;
    }

    /**
     * 更新知识库同步状态
     *
     * @param syncStatus  同步状态
     * @param syncMessage 同步消息
     */
    @Override
    public void updateSyncStatus(Integer syncStatus, String syncMessage) {
        try {
            // 更新同步状态
            updateSettingValue(SettingKeys.KnowledgeBase.SYNC_STATUS, String.valueOf(syncStatus), "知识库同步状态");

            // 更新同步消息
            updateSettingValue(SettingKeys.KnowledgeBase.SYNC_MESSAGE, syncMessage, "知识库同步消息");

            // 更新最后同步时间
            LocalDateTime now = LocalDateTime.now();
            updateSettingValue(SettingKeys.KnowledgeBase.LAST_SYNC_TIME, now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME), "知识库最后同步时间");

            log.info("更新同步状态成功: status={}, message={}", syncStatus, syncMessage);
        } catch (Exception e) {
            log.error("更新同步状态失败: {}", e.getMessage());
            throw new RuntimeException("更新同步状态失败", e);
        }
    }

    /**
     * 初始化系统设置
     * 如果设置不存在，则创建新的设置
     */
    @Override
    public void initSystemSettings() {
        try {
            // 初始化系统版本
            if (!existsByKey(SettingKeys.System.VERSION)) {
                updateSettingValue(SettingKeys.System.VERSION, "1.0.0", "系统版本号");
            }

            // 初始化系统唯一标识
            if (!existsByKey(SettingKeys.System.IDENTIFIER)) {
                String systemId = generateSystemIdentifier();
                updateSettingValue(SettingKeys.System.IDENTIFIER, systemId, "系统唯一标识");
            }

            // 初始化知识库存储路径
            if (!existsByKey(SettingKeys.KnowledgeBase.STORAGE_PATH)) {
                // 知识库使用基础目录下的 team 子目录
                String storagePath = Paths.get(defaultUploadDir, "team").toString();

                // 确保存储路径存在
                try {
                    Path path = Paths.get(storagePath);
                    if (!Files.exists(path)) {
                        Files.createDirectories(path);
                        log.info("创建默认知识库存储路径: {}", path);
                    }
                } catch (Exception e) {
                    log.warn("创建默认知识库存储路径失败: {}", e.getMessage());
                }

                updateSettingValue(SettingKeys.KnowledgeBase.STORAGE_PATH, storagePath, "知识库存储路径");
            }

            // 初始化个人库存储路径
            if (!existsByKey(SettingKeys.KnowledgeBase.PERSONAL_STORAGE_PATH)) {
                // 个人库使用基础目录下的 personal 子目录
                String personalStoragePath = Paths.get(defaultUploadDir, "personal").toString();

                // 确保存储路径存在
                try {
                    Path path = Paths.get(personalStoragePath);
                    if (!Files.exists(path)) {
                        Files.createDirectories(path);
                        log.info("创建默认个人库存储路径: {}", path);
                    }
                } catch (Exception e) {
                    log.warn("创建默认个人库存储路径失败: {}", e.getMessage());
                }

                updateSettingValue(SettingKeys.KnowledgeBase.PERSONAL_STORAGE_PATH, personalStoragePath, "个人库存储路径");
            }

            // 初始化知识库同步状态
            if (!existsByKey(SettingKeys.KnowledgeBase.SYNC_STATUS)) {
                updateSettingValue(SettingKeys.KnowledgeBase.SYNC_STATUS, "0", "知识库同步状态");
            }

            // 初始化知识库同步消息
            if (!existsByKey(SettingKeys.KnowledgeBase.SYNC_MESSAGE)) {
                updateSettingValue(SettingKeys.KnowledgeBase.SYNC_MESSAGE, "未同步", "知识库同步消息");
            }

            // 初始化授权相关设置
            if (!existsByKey(SettingKeys.License.STATUS)) {
                updateSettingValue(SettingKeys.License.STATUS, "0", "授权状态");
            }

            if (!existsByKey(SettingKeys.License.TYPE)) {
                updateSettingValue(SettingKeys.License.TYPE, "trial", "授权类型");
            }

            log.info("初始化系统设置成功");
        } catch (Exception e) {
            log.error("初始化系统设置失败: {}", e.getMessage(), e);
            throw new RuntimeException("初始化系统设置失败", e);
        }
    }

    /**
     * 获取设置值
     *
     * @param key 设置键名
     * @return 设置值
     */
    @Override
    public String getSettingValue(String key) {
        try {
            return systemSettingsMapper.getSettingValueByKey(key);
        } catch (Exception e) {
            log.warn("获取设置值失败: key={}, error={}", key, e.getMessage());
            return null;
        }
    }

    /**
     * 获取设置值，如果不存在则返回默认值
     *
     * @param key          设置键名
     * @param defaultValue 默认值
     * @return 设置值
     */
    @Override
    public String getSettingValue(String key, String defaultValue) {
        String value = getSettingValue(key);
        return StringUtils.hasText(value) ? value : defaultValue;
    }

    /**
     * 更新设置值
     *
     * @param key         设置键名
     * @param value       设置值
     * @param description 描述
     */
    @Override
    public void updateSettingValue(String key, String value, String description) {
        try {
            systemSettingsMapper.insertOrUpdateSetting(key, value, description);
            log.debug("更新设置成功: key={}, value={}", key, value);
        } catch (Exception e) {
            log.error("更新设置失败: key={}, value={}, error={}", key, value, e.getMessage());
            throw new RuntimeException("更新设置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查设置是否存在
     *
     * @param key 设置键名
     * @return 存在返回 true，否则返回 false
     */
    private boolean existsByKey(String key) {
        try {
            return systemSettingsMapper.existsByKey(key);
        } catch (Exception e) {
            log.warn("检查设置是否存在失败: key={}, error={}", key, e.getMessage());
            return false;
        }
    }

    /**
     * 生成系统唯一标识
     *
     * @return 系统唯一标识
     */
    private String generateSystemIdentifier() {
        // 生成一个UUID，并去掉横线，取前16位作为标识
        String uuid = UUID.randomUUID().toString().replace("-", "");
        return "KB-" + uuid.substring(0, 16).toUpperCase();
    }


}
