// auth-modal.js
import { showToast } from '../../shared/components/modal-component.js'; // Assume showToast is exported here
import { checkLoginStatus, clearLoginStatusCache } from '../../shared/services/auth-service.js';

// --- DOM Elements ---
const authStatusDiv = document.getElementById('auth-status');
// const authModal = document.getElementById('auth-modal'); //获取改为在需要时获取
const loginForm = document.getElementById('login-form');
const registerForm = document.getElementById('register-form');
// const loginTab = document.getElementById('login-tab'); // 改为在 switchTab 中获取
// const registerTab = document.getElementById('register-tab'); // 改为在 switchTab 中获取
const loginErrorDiv = document.getElementById('login-error');
const registerErrorDiv = document.getElementById('register-error');

// --- Constants ---
const CONTEXT_PATH = window.location.pathname.startsWith('/wkg') ? '/wkg' : '';
const TOKEN_KEY = 'saToken'; // 保持与后端配置一致

// --- State Flags ---
let isSubmittingLogin = false;
let isSubmittingRegister = false;

// --- Initialization Function (Exported) ---
function initialize() {

    // Attach form submit listeners
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            if (!isSubmittingLogin) submitLogin();
        });
    }
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            if (!isSubmittingRegister) submitRegister();
        });
    }

    // Check initial login status
    checkLoginStatusAndUpdateUI();

    // Setup listeners for static elements in the modal
    document.getElementById('login-tab')?.addEventListener('click', () => switchTab('login'));
    document.getElementById('register-tab')?.addEventListener('click', () => switchTab('register'));
    // Add listener for the main login/register button if it exists
    authStatusDiv?.addEventListener('click', (event) => {
        if (event.target && event.target.id === 'login-register-button') {
            openAuthModal('login');
        }
        if (event.target && event.target.id === 'logout-button') {
            handleLogout();
        }
    });


}

// --- Token Helper Functions ---
function getToken() { return localStorage.getItem(TOKEN_KEY); }
function saveToken(token) { localStorage.setItem(TOKEN_KEY, token); }
function removeToken() { localStorage.removeItem(TOKEN_KEY); }
function getConfiguredTokenName() { return 'satoken'; }

// --- Modal Control Functions ---
let authModalInstance = null;

function openAuthModal(initialTab = 'login') {
    const authModalElement = document.getElementById('authModal');
    if (!authModalElement) {
        console.error("Auth Modal element '#authModal' not found!");
        return;
    }

    // Reset errors
    if (loginErrorDiv) {
        loginErrorDiv.textContent = '';
        loginErrorDiv.style.display = 'none';
    }
    if (registerErrorDiv) {
        registerErrorDiv.textContent = '';
        registerErrorDiv.style.display = 'none';
    }
    loginForm?.classList.remove('was-validated');
    registerForm?.classList.remove('was-validated');
    loginForm?.querySelectorAll('.form-control').forEach(el => el.classList.remove('is-invalid'));
    registerForm?.querySelectorAll('.form-control').forEach(el => el.classList.remove('is-invalid'));

    // Switch to initial tab
    switchTab(initialTab);

    // Get or create Bootstrap Modal instance
    if (!authModalInstance || authModalInstance._element !== authModalElement) {
         const existingInstance = bootstrap.Modal.getInstance(authModalElement);
         if (existingInstance) existingInstance.dispose();
        authModalInstance = new bootstrap.Modal(authModalElement, { keyboard: false });
    }

    authModalInstance.show();
}

function closeAuthModal() {
    if (authModalInstance) {
        authModalInstance.hide();
    } else {
         const authModalElement = document.getElementById('authModal');
         if (authModalElement) {
              authModalElement.classList.remove('show');
            authModalElement.style.display = 'none';
              document.body.classList.remove('modal-open');
            document.querySelector('.modal-backdrop')?.remove();
         }
    }
}

function switchTab(tabName) {
    const loginPanel = document.getElementById('login-panel');
    const registerPanel = document.getElementById('register-panel');
    const loginTabButton = document.getElementById('login-tab');
    const registerTabButton = document.getElementById('register-tab');

    if (!loginPanel || !registerPanel || !loginTabButton || !registerTabButton) return;

    const isLogin = tabName === 'login';

    loginPanel.classList.toggle('show', isLogin);
    loginPanel.classList.toggle('active', isLogin);
    registerPanel.classList.toggle('show', !isLogin);
    registerPanel.classList.toggle('active', !isLogin);

    loginTabButton.classList.toggle('active', isLogin);
    loginTabButton.setAttribute('aria-selected', isLogin);
    registerTabButton.classList.toggle('active', !isLogin);
    registerTabButton.setAttribute('aria-selected', !isLogin);

    // Clear errors and validation states when switching
    if (loginErrorDiv) loginErrorDiv.textContent = '';
    if (registerErrorDiv) registerErrorDiv.textContent = '';
    loginForm?.classList.remove('was-validated');
    registerForm?.classList.remove('was-validated');
    loginForm?.querySelectorAll('.form-control.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    registerForm?.querySelectorAll('.form-control.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    }

// --- Authentication Status Update ---
function updateAuthStatus(isLogin, userData) {
    if (!authStatusDiv) return;
    if (isLogin && userData) {
        const displayName = userData.username || `User ${userData.userId}`;
        authStatusDiv.innerHTML = `
            <span class="me-2">${escapeHtml(displayName)}</span>
            <button id="logout-button" class="btn btn-outline-primary btn-sm">退出</button>
        `;
        document.getElementById('logout-button')?.addEventListener('click', handleLogout);
    } else {
        authStatusDiv.innerHTML = `
            <button id="login-register-button" class="btn btn-primary btn-sm">登录</button>
        `;
        document.getElementById('login-register-button')?.addEventListener('click', () => openAuthModal('login'));
    }
}

// --- Form Submission Handlers ---
function submitLogin() {
    if (isSubmittingLogin || !loginForm || !loginErrorDiv) return;

    const usernameInput = document.getElementById('login-username');
    const passwordInput = document.getElementById('login-password');
    const username = usernameInput?.value.trim() || '';
    const password = passwordInput?.value.trim() || '';

    // Reset previous errors/validation
    loginErrorDiv.textContent = '';
    loginErrorDiv.style.display = 'none';
    loginForm.classList.remove('was-validated');
    usernameInput?.classList.remove('is-invalid');
    passwordInput?.classList.remove('is-invalid');

    let isValid = true;
    if (!username) {
        usernameInput?.classList.add('is-invalid');
        isValid = false;
    }
    if (!password) {
        passwordInput?.classList.add('is-invalid');
        isValid = false;
    }

    if (!isValid) {
        loginErrorDiv.textContent = '请输入用户名和密码。';
        loginErrorDiv.style.display = 'block';
        loginForm.classList.add('was-validated');
        return;
    }

    isSubmittingLogin = true;
    const loginButton = document.getElementById('login-submit-button');
    if (loginButton) {
        loginButton.disabled = true;
        loginButton.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>登录中...';
    }

    handleLogin(username, password)
        .finally(() => {
            isSubmittingLogin = false;
            if (loginButton) {
                loginButton.disabled = false;
                loginButton.innerHTML = '<i class="bi bi-box-arrow-in-right me-1"></i>登录'; // Use Bootstrap icon
            }
        });
}

function submitRegister() {
    if (isSubmittingRegister || !registerForm || !registerErrorDiv) return;

    const usernameInput = document.getElementById('register-username');
    const passwordInput = document.getElementById('register-password');
    const confirmInput = document.getElementById('register-confirm-password');
    const username = usernameInput?.value.trim() || '';
    const password = passwordInput?.value.trim() || '';
    const confirmPassword = confirmInput?.value.trim() || '';

    // Reset previous errors/validation
    registerErrorDiv.textContent = '';
    registerForm.classList.remove('was-validated');
    usernameInput?.classList.remove('is-invalid');
    passwordInput?.classList.remove('is-invalid');
    confirmInput?.classList.remove('is-invalid');

    let isValid = true;
    let errorMsg = '';

    if (!username) {
        usernameInput?.classList.add('is-invalid');
        errorMsg = '请输入用户名。';
        isValid = false;
    }
    if (!password) {
        passwordInput?.classList.add('is-invalid');
        errorMsg = errorMsg || '请输入密码。';
        isValid = false;
    }
    if (!confirmPassword) {
        confirmInput?.classList.add('is-invalid');
        errorMsg = errorMsg || '请确认密码。';
        isValid = false;
    }
    if (password && password.length < 6) {
        passwordInput?.classList.add('is-invalid');
        errorMsg = errorMsg || '密码长度至少需要6位。';
        isValid = false;
    }
    if (password && confirmPassword && password !== confirmPassword) {
        confirmInput?.classList.add('is-invalid');
        errorMsg = errorMsg || '两次输入的密码不一致。';
        isValid = false;
    }

    if (!isValid) {
        registerErrorDiv.textContent = errorMsg;
        registerForm.classList.add('was-validated');
        return;
    }

    isSubmittingRegister = true;
    const registerButton = document.getElementById('register-submit-button');
    if (registerButton) {
        registerButton.disabled = true;
        registerButton.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>注册中...';
    }

    handleRegister(username, password)
        .finally(() => {
            isSubmittingRegister = false;
            if (registerButton) {
                registerButton.disabled = false;
                registerButton.innerHTML = '注册';
            }
        });
}

// --- API Call Handlers (Use imported showToast) ---
async function handleRegister(username, password) {
    if (!registerErrorDiv) return;
    try {
        registerErrorDiv.textContent = '';
        registerErrorDiv.className = 'error-message mb-3'; // Reset class

        const response = await fetch(`${CONTEXT_PATH}/auth/register`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password })
        });
        const result = await response.json();

        if (result.code === 200) {
            showToast(`用户 ${username} 注册成功，请登录。`, 'success');
            setTimeout(() => {
                switchTab('login');
                const loginUsernameInput = document.getElementById('login-username');
                    const loginPasswordInput = document.getElementById('login-password');
                if (loginUsernameInput) loginUsernameInput.value = username;
                loginPasswordInput?.focus();
            }, 1000); // Slightly faster switch
        } else {
            registerErrorDiv.textContent = result.message || '注册失败，请稍后再试';
        }
    } catch (error) {
        console.error('注册请求失败:', error);
        registerErrorDiv.textContent = '注册请求异常，请检查网络或联系管理员';
    }
}

async function handleLogin(username, password) {
    if (!loginErrorDiv) return;
    try {
        loginErrorDiv.textContent = '';
        loginErrorDiv.style.display = 'none';
        const response = await fetch(`${CONTEXT_PATH}/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password })
        });
        const result = await response.json();

        if (result.code === 200 && result.data?.token) {
            saveToken(result.data.token);
            closeAuthModal();
            // 移除登录成功提示

            // 清除登录状态缓存
            clearLoginStatusCache();

            // 清除sessionStorage中的登录状态缓存
            try {
                sessionStorage.removeItem('userLoginState');
            } catch (e) {
                console.warn('[AuthModal] 清除会话存储缓存失败:', e);
            }

            // 更新UI
            checkLoginStatusAndUpdateUI();

            // Reload after a short delay to allow UI updates
            setTimeout(() => location.reload(), 500);
        } else {
            removeToken();
            loginErrorDiv.textContent = result.message || '登录失败，请检查用户名或密码';
            loginErrorDiv.style.display = 'block';
            document.getElementById('login-password')?.classList.add('is-invalid');
        }
    } catch (error) {
        console.error('登录请求失败:', error);
        loginErrorDiv.textContent = '登录请求异常，请检查网络或联系管理员';
        loginErrorDiv.style.display = 'block';
        removeToken();
    }
}

async function handleLogout() {
    try {
        const response = await fetch(`${CONTEXT_PATH}/auth/logout`, { method: 'POST' });

        // 不管服务器返回什么，都清除前端状态
        removeToken();

        // 清除登录状态缓存
        clearLoginStatusCache();

        // 清除sessionStorage中的登录状态缓存
        try {
            sessionStorage.removeItem('userLoginState');
        } catch (e) {
            console.warn('[AuthModal] 清除会话存储缓存失败:', e);
        }

        // 更新显示状态
        updateAuthStatus(false);

        // 移除退出成功提示

        // 延迟刷新页面
        setTimeout(() => location.reload(), 500);
    } catch (error) {
        console.error('登出请求失败:', error);
        // 移除登出失败提示

        // 即使请求失败，也清除前端状态
        removeToken();
        clearLoginStatusCache();

        // 清除sessionStorage中的登录状态缓存
        try {
            sessionStorage.removeItem('userLoginState');
        } catch (e) {}

        updateAuthStatus(false);

        // 延迟刷新页面
        setTimeout(() => location.reload(), 500);
    }
}

// --- Initial Check & Event Listeners Setup ---
async function checkLoginStatusAndUpdateUI() {
    if (!authStatusDiv) return;
    try {
        // 使用共享服务检查登录状态
        const result = await checkLoginStatus();
        updateAuthStatus(result.isLoggedIn, result.userData);

        // Ensure token consistency
        if (!result.isLoggedIn && getToken()) {
            removeToken();
        }
    } catch (error) {
        console.error('[AuthModal] 检查登录状态失败:', error);
        updateAuthStatus(false);
        removeToken();
    }
}

// Helper for HTML escaping
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') return '';
    return unsafe
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}

// Export the initialize function
export { initialize };