package com.zibbava.edgemind.cortex.service.impl;

import com.onlyoffice.manager.document.DocumentManager;
import com.onlyoffice.manager.security.JwtManager;
import com.onlyoffice.manager.settings.SettingsManager;
import com.onlyoffice.manager.url.UrlManager;
import com.onlyoffice.model.documenteditor.Config;
import com.onlyoffice.model.documenteditor.config.document.Permissions;
import com.onlyoffice.model.documenteditor.config.document.Type;
import com.onlyoffice.model.documenteditor.config.editorconfig.Mode;
import com.onlyoffice.service.documenteditor.config.DefaultConfigService;
import com.zibbava.edgemind.cortex.common.enums.NodeType;
import com.zibbava.edgemind.cortex.common.enums.ResultCode;
import com.zibbava.edgemind.cortex.common.exception.BusinessException;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.service.KnowledgeBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * ONLYOFFICE编辑器配置服务实现
 */
@Component
@Slf4j
public class ConfigServiceImpl extends DefaultConfigService {

    private final KnowledgeBaseService knowledgeBaseService;

    public ConfigServiceImpl(
            final DocumentManager documentManager,
            final UrlManager urlManager,
            final JwtManager jwtManager,
            final SettingsManager settingsManager,
            final KnowledgeBaseService knowledgeBaseService) {
        super(documentManager, urlManager, jwtManager, settingsManager);
        this.knowledgeBaseService = knowledgeBaseService;
    }

    /**
     * 获取文档的权限设置
     */
    @Override
    public Permissions getPermissions(final String nodeId) {
        try {
            KnowledgeNode node = knowledgeBaseService.findNodeById(nodeId);
            if (node == null) {
                log.warn("获取权限时找不到节点: {}", nodeId);
                throw new BusinessException(ResultCode.RESOURCE_NOT_FOUND, "节点不存在");
            }
            
            if (node.getType() != NodeType.FILE) {
                log.warn("节点 {} 不是文件类型", nodeId);
                throw new BusinessException(ResultCode.NODE_TYPE_MISMATCH, "节点类型错误");
            }
            
            // 默认授予编辑、评论、下载和打印权限
            return Permissions.builder()
                    .edit(true)
                    .comment(true)
                    .download(true)
                    .print(true)
                    .build();
        } catch (Exception e) {
            log.error("获取文档权限失败: {}", e.getMessage(), e);
            // 回退到默认权限
            return Permissions.builder()
                    .edit(true)
                    .comment(false)
                    .download(true)
                    .print(true)
                    .build();
        }
    }
    
    /**
     * 重写创建配置方法，添加中文语言设置
     */
    @Override
    public Config createConfig(final String nodeId, final Mode mode, final Type type) {
        // 调用父类的创建配置方法
        Config config = super.createConfig(nodeId, mode, type);
        
        // 设置界面语言为中文
        if (config != null && config.getEditorConfig() != null) {
            config.getEditorConfig().setLang("zh-CN");
            
            // 设置纯预览模式，优化显示效果
            if (config.getEditorConfig().getCustomization() == null) {
                config.getEditorConfig().setCustomization(new com.onlyoffice.model.documenteditor.config.editorconfig.Customization());
            }
            
            // 禁用所有工具栏和界面元素，仅保留文档查看
            // 注意：部分API可能在当前SDK版本不可用，根据实际情况调整
            try {
                // 这些是核心设置，通常都支持
                config.getEditorConfig().getCustomization().setToolbarNoTabs(true); // 禁用工具栏选项卡 
                config.getEditorConfig().getCustomization().setChat(false); // 禁用聊天
                config.getEditorConfig().getCustomization().setComments(false); // 禁用评论
                config.getEditorConfig().getCustomization().setCompactToolbar(true); // 使用紧凑工具栏
                config.getEditorConfig().getCustomization().setFeedback(false); // 禁用反馈
                config.getEditorConfig().getCustomization().setHelp(false); // 禁用帮助
                
                // 这些设置可能不是所有SDK版本都支持
                try { config.getEditorConfig().getCustomization().setHideRightMenu(true); } catch (Exception e) { log.debug("setHideRightMenu不受支持"); }
                try { config.getEditorConfig().getCustomization().setAutosave(false); } catch (Exception e) { log.debug("setAutosave不受支持"); }
                try { config.getEditorConfig().getCustomization().setForcesave(false); } catch (Exception e) { log.debug("setForcesave不受支持"); }
                try { config.getEditorConfig().getCustomization().setShowReviewChanges(false); } catch (Exception e) { log.debug("setShowReviewChanges不受支持"); }
                // 设置默认缩放为适应宽度，优化文档显示
                try { config.getEditorConfig().getCustomization().setZoom(-2); } catch (Exception e) { log.debug("setZoom不受支持"); }
            } catch (Exception e) {
                log.warn("配置ONLYOFFICE自定义设置时发生错误: {}", e.getMessage());
            }
        }
        
        return config;
    }
}