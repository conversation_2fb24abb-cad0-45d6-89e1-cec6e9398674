package com.zibbava.edgemind.cortex.manager;

import com.onlyoffice.manager.settings.SettingsManager;
import com.onlyoffice.manager.url.DefaultUrlManager;
import com.zibbava.edgemind.cortex.config.OnlyOfficeConfig;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * URL管理器实现
 * 负责生成ONLYOFFICE相关的URL
 */
@Component
@Slf4j
public class UrlManagerImpl extends DefaultUrlManager {
    
    @Autowired
    private HttpServletRequest request;
    
    private final OnlyOfficeConfig onlyOfficeConfig;

    public UrlManagerImpl(final SettingsManager settingsManager, OnlyOfficeConfig onlyOfficeConfig) {
        super(settingsManager);
        this.onlyOfficeConfig = onlyOfficeConfig;
    }

    /**
     * 获取文档API的URL
     */
    @Override
    public String getDocumentServerApiUrl() {
        return onlyOfficeConfig.getApiJsUrl();
    }

    /**
     * 获取文件下载URL
     */
    @Override
    public String getFileUrl(final String nodeId) {
        return getServerUrl() + "/api/knowledgebase/files/" + nodeId + "/download";
    }

    /**
     * 获取回调URL
     */
    @Override
    public String getCallbackUrl(final String nodeId) {
        return getServerUrl() + "/api/onlyoffice/callback/" + nodeId;
    }

    /**
     * 获取服务器URL
     */
    private String getServerUrl() {
        try {
            String baseUrl = "http://host.docker.internal:8080/wkg";
            log.debug("构建服务器URL: {}", baseUrl);
            return baseUrl;
        } catch (Exception e) {
            log.error("构建服务器URL失败", e);
            // 回退到基本方式
            return request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + request.getContextPath();
        }
    }
} 