/* 加载动画样式 */

/* 消息加载动画容器 */
.ai-loading-container {
    display: flex;
    align-items: center;
    padding: 16px;
    margin-bottom: 10px;
    animation: fadeIn 0.3s ease-in-out;
}

/* 加载动画效果 */
.ai-loading-animation {
    display: flex;
    align-items: center;
}

/* 加载动画点 */
.ai-loading-dot {
    width: 8px;
    height: 8px;
    margin: 0 3px;
    background-color: #4285f4;
    border-radius: 50%;
    display: inline-block;
    animation: dotPulse 1.5s infinite ease-in-out;
}

/* 三个点的延迟动画 */
.ai-loading-dot:nth-child(1) {
    animation-delay: 0s;
}

.ai-loading-dot:nth-child(2) {
    animation-delay: 0.3s;
}

.ai-loading-dot:nth-child(3) {
    animation-delay: 0.6s;
}

/* 点的脉冲动画 */
@keyframes dotPulse {
    0%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 自定义滚动条样式 */
/* 整体滚动条 */
pre::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

/* 滚动条轨道 */
pre::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

/* 滚动条滑块 */
pre::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    transition: background 0.3s ease;
}

/* 鼠标悬停在滚动条上的滑块样式 */
pre::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}

/* 滚动条角落 */
pre::-webkit-scrollbar-corner {
    background: transparent;
}

/* 确保在Firefox中也有类似的滚动条样式 */
pre {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.05);
}
