package com.zibbava.edgemind.cortex.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zibbava.edgemind.cortex.dto.ollama.LangchainFunctionCall;
import com.zibbava.edgemind.cortex.dto.ollama.LangchainToolCall;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaChatRequest;
import com.zibbava.edgemind.cortex.dto.ollama.OllamaMessage;
import com.zibbava.edgemind.cortex.model.ollama.OllamaModel;
import com.zibbava.edgemind.cortex.model.ollama.OllamaModelDetails;
import com.zibbava.edgemind.cortex.util.ollama.TriConsumer;
import dev.langchain4j.agent.tool.ToolExecutionRequest;
import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.service.tool.ToolExecutor;
import dev.langchain4j.service.tool.ToolProviderRequest;
import dev.langchain4j.service.tool.ToolProviderResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Ollama 流式对话工具类
 * 提供与本地 Ollama 服务的流式交互功能
 * 支持 LangChain4j ToolProvider 集成，可与 MCP 和 Function Calling 协同工作
 */
@Slf4j
public class OllamaUtils {

    private static final String DEFAULT_BASE_URL = "http://localhost:11434";
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final RestTemplate restTemplate = new RestTemplate();

    // Ollama API 端点
    private static final String API_LIST_MODELS = "/api/tags";
    private static final String API_CHAT = "/api/chat";
    private static final String API_PULL = "/api/pull";
    private static final String API_DELETE = "/api/delete";

    // 存储活跃的流式对话会话
    private static final ConcurrentHashMap<String, StreamChatSession> activeStreamSessions = new ConcurrentHashMap<>();

    /**
     * 流式对话会话管理
     */
    private static class StreamChatSession {
        private final String sessionId;
        private final AtomicBoolean cancelled = new AtomicBoolean(false);
        private final AtomicReference<CloseableHttpClient> httpClient = new AtomicReference<>();
        private final AtomicReference<CloseableHttpResponse> httpResponse = new AtomicReference<>();
        private final ToolProviderResult toolProviderResult;

        public StreamChatSession(String sessionId, ToolProviderResult toolProviderResult) {
            this.sessionId = sessionId;
            this.toolProviderResult = toolProviderResult;
        }

        public void setHttpClient(CloseableHttpClient client) {
            this.httpClient.set(client);
        }

        public void setHttpResponse(CloseableHttpResponse response) {
            this.httpResponse.set(response);
        }

        public boolean isCancelled() {
            return cancelled.get();
        }

        public ToolProviderResult getToolProviderResult() {
            return toolProviderResult;
        }

        public void cancel() {
            if (cancelled.compareAndSet(false, true)) {
                try {
                    CloseableHttpResponse response = httpResponse.get();
                    if (response != null) {
                        response.close();
                    }
                    CloseableHttpClient client = httpClient.get();
                    if (client != null) {
                        client.close();
                    }
                } catch (Exception e) {
                    log.warn("关闭HTTP连接时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 流式聊天对话方法
     */
    public static Flux<String> streamChat(OllamaChatRequest request) {
        return Flux.create(sink -> {
            // 预先获取工具列表
            ToolProviderResult toolProviderResult = null;
            if (request.getToolProvider() != null) {
                try {
                    ToolProviderRequest toolRequest = new ToolProviderRequest(
                            "default",
                            extractUserMessage(request.getMessages())
                    );
                    toolProviderResult = request.getToolProvider().provideTools(toolRequest);
                } catch (Exception e) {
                    log.warn("获取工具时出错: {}", e.getMessage(), e);
                }
            }

            StreamChatSession session = new StreamChatSession(request.getSessionId(), toolProviderResult);
            activeStreamSessions.put(request.getSessionId(), session);
            
            Mono.fromCallable(() -> {
                try {
                    return processStreamChatRequest(request, session, sink);
                } catch (Exception e) {
                    log.error("流式聊天请求处理异常", e);
                    if (!sink.isCancelled()) {
                        sink.error(e);
                    }
                    return null;
                }
            })
            .subscribeOn(Schedulers.boundedElastic())
            .doFinally(signalType -> {
                activeStreamSessions.remove(request.getSessionId());
                session.cancel();
                if (!sink.isCancelled()) {
                    sink.complete();
                }
            })
            .subscribe();
            
            sink.onCancel(() -> {
                log.info("流式聊天会话被取消: {}", request.getSessionId());
                session.cancel();
                activeStreamSessions.remove(request.getSessionId());
            });
            
        }, FluxSink.OverflowStrategy.BUFFER);
    }

    /**
     * 处理流式聊天请求的核心方法
     */
    private static String processStreamChatRequest(OllamaChatRequest request, 
                                                  StreamChatSession session, 
                                                  FluxSink<String> sink) throws Exception {
        String url = request.getBaseUrl() + API_CHAT;
        
        CloseableHttpClient httpClient = HttpClients.createDefault();
        session.setHttpClient(httpClient);
        
        try {
            Map<String, Object> requestBody = buildChatRequestBody(request);
            
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Accept", "application/json");
            
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            StringEntity entity = new StringEntity(jsonBody, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            
            log.debug("发送流式聊天请求到: {}, 会话ID: {}", url, request.getSessionId());
            
            CloseableHttpResponse response = httpClient.execute(httpPost);
            session.setHttpResponse(response);
            
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode >= 200 && statusCode < 300) {
                processStreamResponse(response, session, sink, request);
            } else {
                // 读取完整的错误响应内容
                String responseBody = "";
                try {
                    org.apache.http.HttpEntity responseEntity = response.getEntity();
                    if (responseEntity != null) {
                        responseBody = EntityUtils.toString(responseEntity, "UTF-8");
                    }
                } catch (Exception e) {
                    log.warn("读取错误响应内容失败: {}", e.getMessage());
                }
                
                String errorMsg = "聊天请求失败, 状态码: " + statusCode;
                log.error("{}. 完整响应内容: {}", errorMsg, responseBody);
                if (!sink.isCancelled()) {
                    sink.error(new RuntimeException(errorMsg));
                }
            }
            
        } finally {
            try {
                httpClient.close();
            } catch (Exception e) {
                log.warn("关闭HTTP客户端时出错: {}", e.getMessage());
            }
        }
        
        return null;
    }

    /**
     * 构建聊天请求体，集成ToolProvider支持
     */
    private static Map<String, Object> buildChatRequestBody(OllamaChatRequest request) {
        Map<String, Object> requestBody = new HashMap<>();
        
        requestBody.put("model", request.getModel());
        requestBody.put("messages", convertMessagesToMap(request.getMessages()));
        requestBody.put("stream", request.getStream() != null ? request.getStream() : true);
        
        if (request.getFormat() != null) {
            requestBody.put("format", request.getFormat().toRequestFormat());
        }
        
        if (request.getKeepAlive() != null) {
            requestBody.put("keep_alive", request.getKeepAlive());
        }
        
        // 集成LangChain4j ToolProvider
        if (request.getToolProvider() != null) {
            try {
                ToolProviderRequest toolRequest = new ToolProviderRequest(
                        "default",
                        extractUserMessage(request.getMessages())
                );
                
                ToolProviderResult toolResult = request.getToolProvider().provideTools(toolRequest);
                if (toolResult != null && !toolResult.tools().isEmpty()) {
                    requestBody.put("tools", convertLangChainToolsToOllamaFormat(toolResult));
                }
            } catch (Exception e) {
                log.warn("获取工具时出错: {}", e.getMessage(), e);
            }
        }
        
        if (request.getOptions() != null) {
            Map<String, Object> optionsMap = request.getOptions().toMap();
            if (!optionsMap.isEmpty()) {
                requestBody.put("options", optionsMap);
            }
        }
        
        // 处理思考功能设置 - 支持 Ollama v0.9.0
        if (request.getThink() != null) {
            requestBody.put("think", request.getThink());
            log.info("🧠 设置思考功能: {} (Ollama v0.9.0+)", request.getThink() ? "启用" : "禁用");
        } else {
            log.debug("🧠 未指定思考功能设置，使用模型默认行为");
        }
        
        return requestBody;
    }

    /**
     * 从消息列表中提取用户消息
     */
    private static dev.langchain4j.data.message.UserMessage extractUserMessage(List<OllamaMessage> messages) {
        for (OllamaMessage msg : messages) {
            if ("user".equals(msg.getRole())) {
                return dev.langchain4j.data.message.UserMessage.from(msg.getContent());
            }
        }
        return dev.langchain4j.data.message.UserMessage.from("");
    }

    /**
     * 将LangChain4j工具格式转换为Ollama格式
     */
    private static List<Map<String, Object>> convertLangChainToolsToOllamaFormat(ToolProviderResult toolResult) {
        List<Map<String, Object>> ollamaTools = new ArrayList<>();
        
        for (Map.Entry<ToolSpecification, ToolExecutor> entry : toolResult.tools().entrySet()) {
            ToolSpecification spec = entry.getKey();
            
            Map<String, Object> ollamaTool = new HashMap<>();
            ollamaTool.put("type", "function");
            
            Map<String, Object> function = new HashMap<>();
            function.put("name", spec.name());
            function.put("description", spec.description());
            
            if (spec.parameters() != null) {
                function.put("parameters", convertParametersToOllamaFormat(spec.parameters()));
            }
            
            ollamaTool.put("function", function);
            ollamaTools.add(ollamaTool);
        }
        
        return ollamaTools;
    }

    /**
     * 转换参数规范为Ollama格式
     */
    private static Map<String, Object> convertParametersToOllamaFormat(Object parameters) {
        if (parameters instanceof Map) {
            return (Map<String, Object>) parameters;
        }
        return new HashMap<>();
    }

    /**
     * 转换消息列表为Map格式 - 支持 Ollama v0.9.0 新的思考字段格式
     */
    private static List<Map<String, Object>> convertMessagesToMap(List<OllamaMessage> messages) {
        List<Map<String, Object>> messageList = new ArrayList<>();
        
        for (OllamaMessage msg : messages) {
            Map<String, Object> messageMap = new HashMap<>();
            messageMap.put("role", msg.getRole());
            messageMap.put("content", msg.getContent());
            
            // 处理思考字段 - 遵循 v0.9.0 格式
            if (msg.getThinking() != null && !msg.getThinking().isEmpty()) {
                messageMap.put("thinking", msg.getThinking());
                log.debug("💭 消息包含思考内容，长度: {}", msg.getThinking().length());
            }
            
            if (msg.getImages() != null && !msg.getImages().isEmpty()) {
                messageMap.put("images", msg.getImages());
            }
            
            if (msg.getToolCalls() != null && !msg.getToolCalls().isEmpty()) {
                messageMap.put("tool_calls", convertLangchainToolCallsToMap(msg.getToolCalls()));
            }
            
            if (msg.getToolCallId() != null) {
                messageMap.put("tool_call_id", msg.getToolCallId());
            }
            
            messageList.add(messageMap);
        }
        
        return messageList;
    }

    /**
     * 转换LangChain4j格式的工具调用列表为Map格式
     */
    private static List<Map<String, Object>> convertLangchainToolCallsToMap(List<LangchainToolCall> toolCalls) {
        List<Map<String, Object>> toolCallList = new ArrayList<>();
        
        for (LangchainToolCall toolCall : toolCalls) {
            Map<String, Object> toolCallMap = new HashMap<>();
            if (toolCall.getId() != null) {
                toolCallMap.put("id", toolCall.getId());
            }
            if (toolCall.getType() != null) {
                toolCallMap.put("type", toolCall.getType());
            }
            
            if (toolCall.getFunction() != null) {
                Map<String, Object> functionMap = new HashMap<>();
                functionMap.put("name", toolCall.getFunction().getName());
                if (toolCall.getFunction().getArguments() != null) {
                    functionMap.put("arguments", toolCall.getFunction().getArguments());
                }
                toolCallMap.put("function", functionMap);
            }
            
            toolCallList.add(toolCallMap);
        }
        
        return toolCallList;
    }

    /**
     * 处理流式响应 - 直接支持 Ollama v0.9.0 字段分离格式
     * 不再使用XML标签，思考和内容完全分离
     */
    private static void processStreamResponse(CloseableHttpResponse response, 
                                            StreamChatSession session, 
                                            FluxSink<String> sink,
                                            OllamaChatRequest originalRequest) throws Exception {
        org.apache.http.HttpEntity responseEntity = response.getEntity();
        
        if (responseEntity != null) {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(responseEntity.getContent(), StandardCharsets.UTF_8))) {
                String line;
                List<Map<String, Object>> pendingToolCalls = new ArrayList<>();
                
                while ((line = reader.readLine()) != null && !session.isCancelled() && !sink.isCancelled()) {
                    if (line.trim().isEmpty()) continue;
                    
                    try {
                        JsonNode jsonNode = objectMapper.readTree(line);
                        
                        boolean done = jsonNode.has("done") && jsonNode.get("done").asBoolean();
                        
                        if (jsonNode.has("message")) {
                            JsonNode messageNode = jsonNode.get("message");
                            
                            // 处理思考内容 - v0.9.0 直接字段传输
                            if (messageNode.has("thinking")) {
                                String thinking = messageNode.get("thinking").asText();
                                if (!thinking.isEmpty() && !session.isCancelled() && !sink.isCancelled()) {
                                    // 直接发送思考内容，使用特殊前缀标识
                                    sink.next("THINKING:" + thinking);
                                    log.debug("💭 发送思考内容: {} 字符", thinking.length());
                                }
                            }
                            
                            // 处理普通文本内容
                            if (messageNode.has("content")) {
                                String content = messageNode.get("content").asText();
                                if (!content.isEmpty() && !session.isCancelled() && !sink.isCancelled()) {
                                    // 直接发送内容，使用特殊前缀标识
                                    sink.next("CONTENT:" + content);
                                    log.debug("📝 发送内容: {} 字符", content.length());
                                }
                            }
                            
                            // 处理工具调用
                            if (messageNode.has("tool_calls")) {
                                JsonNode toolCallsNode = messageNode.get("tool_calls");
                                log.debug("🔧 收到工具调用: {}", toolCallsNode.toString());
                                
                                // 累积工具调用
                                for (JsonNode toolCallNode : toolCallsNode) {
                                    Map<String, Object> toolCallMap = objectMapper.convertValue(toolCallNode, Map.class);
                                    pendingToolCalls.add(toolCallMap);
                                }
                            }
                        }
                        
                        if (jsonNode.has("error")) {
                            String error = jsonNode.get("error").asText();
                            log.error("❌ Ollama返回错误: {}", error);
                            if (!sink.isCancelled()) {
                                sink.error(new RuntimeException("Ollama错误: " + error));
                            }
                            break;
                        }
                        
                        if (done) {
                            log.debug("✅ 流式聊天完成");
                            
                            // 发送完成标识
                            sink.next("DONE:");
                            
                            // 执行累积的工具调用
                            if (!pendingToolCalls.isEmpty() && session.getToolProviderResult() != null) {
                                executeToolCallsAndContinueChat(pendingToolCalls, session, sink, originalRequest);
                            }
                            break;
                        }
                        
                    } catch (Exception e) {
                        log.warn("⚠️ 解析响应行时出错: {} - {}", line, e.getMessage());
                    }
                }
            }
        }
    }

    /**
     * 执行工具调用并继续对话
     */
    private static void executeToolCallsAndContinueChat(List<Map<String, Object>> toolCalls,
                                                       StreamChatSession session,
                                                       FluxSink<String> sink,
                                                       OllamaChatRequest originalRequest) {
        try {
            List<OllamaMessage> updatedMessages = new ArrayList<>(originalRequest.getMessages());
            
            // 添加助手的工具调用消息
            List<LangchainToolCall> assistantToolCalls = new ArrayList<>();
            for (Map<String, Object> toolCall : toolCalls) {
                String id = (String) toolCall.get("id");
                String type = (String) toolCall.get("type");
                Map<String, Object> function = (Map<String, Object>) toolCall.get("function");
                String name = (String) function.get("name");
                String arguments = (String) function.get("arguments");
                
                // 解析arguments字符串为Map
                Map<String, Object> argumentsMap;
                try {
                    argumentsMap = objectMapper.readValue(arguments, Map.class);
                } catch (Exception e) {
                    log.warn("解析工具参数失败: {}", arguments, e);
                    argumentsMap = new HashMap<>();
                }
                
                LangchainToolCall langchainToolCall = LangchainToolCall.builder()
                        .id(id)
                        .type(type)
                        .function(LangchainFunctionCall.builder()
                                .name(name)
                                .arguments(argumentsMap)
                                .build())
                        .build();
                
                assistantToolCalls.add(langchainToolCall);
            }
            
            // 添加助手消息（包含工具调用）
            OllamaMessage assistantMessage = OllamaMessage.builder()
                    .role("assistant")
                    .content("")
                    .toolCalls(assistantToolCalls)
                    .build();
            updatedMessages.add(assistantMessage);
            
            // 执行每个工具调用并添加结果消息
            for (Map<String, Object> toolCall : toolCalls) {
                String id = (String) toolCall.get("id");
                Map<String, Object> function = (Map<String, Object>) toolCall.get("function");
                String name = (String) function.get("name");
                String arguments = (String) function.get("arguments");
                
                try {
                    // 执行工具
                    ToolExecutor executor = session.getToolProviderResult().toolExecutorByName(name);
                    if (executor != null) {
                        ToolExecutionRequest execRequest = ToolExecutionRequest.builder()
                                .name(name)
                                .arguments(arguments)
                                .build();
                        
                        sink.next("\n[执行工具: " + name + "]");
                        String result = executor.execute(execRequest, null);
                        sink.next("\n[工具结果: " + result + "]\n");
                        
                        // 添加工具结果消息
                        OllamaMessage toolResultMessage = OllamaMessage.builder()
                                .role("tool")
                                .content(result)
                                .toolCallId(id)
                                .build();
                        updatedMessages.add(toolResultMessage);
                    } else {
                        log.warn("找不到工具执行器: {}", name);
                        // 添加错误结果
                        OllamaMessage errorMessage = OllamaMessage.builder()
                                .role("tool")
                                .content("错误: 找不到工具 " + name)
                                .toolCallId(id)
                                .build();
                        updatedMessages.add(errorMessage);
                    }
                } catch (Exception e) {
                    log.error("执行工具 {} 时出错: {}", name, e.getMessage(), e);
                    // 添加错误结果
                    OllamaMessage errorMessage = OllamaMessage.builder()
                            .role("tool")
                            .content("错误: " + e.getMessage())
                            .toolCallId(id)
                            .build();
                    updatedMessages.add(errorMessage);
                }
            }
            
            // 创建新的请求继续对话
            OllamaChatRequest continueRequest = OllamaChatRequest.builder()
                    .baseUrl(originalRequest.getBaseUrl())
                    .model(originalRequest.getModel())
                    .messages(updatedMessages)
                    .options(originalRequest.getOptions())
                    .format(originalRequest.getFormat())
                    .keepAlive(originalRequest.getKeepAlive())
                    .sessionId(originalRequest.getSessionId() + "_continue")
                    .toolProvider(originalRequest.getToolProvider())
                    .build();
            
            // 递归继续对话
            sink.next("\n[继续对话...]\n");
            processStreamChatRequest(continueRequest, session, sink);
            
        } catch (Exception e) {
            log.error("执行工具调用时出错: {}", e.getMessage(), e);
            if (!sink.isCancelled()) {
                sink.error(e);
            }
        }
    }

    /**
     * 中断指定会话的流式对话
     */
    public static boolean cancelStreamChat(String sessionId) {
        StreamChatSession session = activeStreamSessions.get(sessionId);
        if (session != null) {
            log.info("中断流式聊天会话: {}", sessionId);
            session.cancel();
            activeStreamSessions.remove(sessionId);
            return true;
        }
        return false;
    }

    /**
     * 获取当前活跃的流式会话数量
     */
    public static int getActiveStreamSessionCount() {
        return activeStreamSessions.size();
    }

    /**
     * 获取所有活跃的会话ID列表
     */
    public static List<String> getActiveStreamSessionIds() {
        return new ArrayList<>(activeStreamSessions.keySet());
    }

    /**
     * 中断所有活跃的流式对话会话
     */
    public static void cancelAllStreamChats() {
        log.info("中断所有活跃的流式聊天会话，共{}个", activeStreamSessions.size());
        activeStreamSessions.values().forEach(StreamChatSession::cancel);
        activeStreamSessions.clear();
    }

    /**
     * 获取本地可用模型列表（简化版，只返回基本信息）
     */
    public static List<OllamaModel> getLocalModels() {
        return getLocalModels(DEFAULT_BASE_URL);
    }

    public static List<OllamaModel> getLocalModels(String baseUrl) {
        List<OllamaModel> models = new ArrayList<>();

        try {
            String url = baseUrl + API_LIST_MODELS;
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode root = objectMapper.readTree(response.getBody());
                JsonNode modelsNode = root.get("models");

                if (modelsNode != null && modelsNode.isArray()) {
                    for (JsonNode modelNode : modelsNode) {
                        String name = modelNode.get("name").asText();
                        String modifiedAt = modelNode.get("modified_at").asText();
                        long size = modelNode.get("size").asLong();
                        String digest = modelNode.get("digest").asText();

                        // 根据模型名称判断模型类型
                        OllamaModelDetails.ModelType modelType = determineModelType(name);

                        models.add(OllamaModel.builder()
                                .name(name)
                                .modifiedAt(modifiedAt)
                                .size(size)
                                .digest(digest)
                                .modelType(modelType)
                                .build());
                    }
                }
            } else {
                log.error("获取Ollama模型列表失败，状态码: {}", response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("获取Ollama模型列表异常", e);
        }

        return models;
    }

    /**
     * 检查Ollama服务是否可用
     */
    public static boolean isOllamaServiceAvailable() {
        return isOllamaServiceAvailable(DEFAULT_BASE_URL);
    }

    public static boolean isOllamaServiceAvailable(String baseUrl) {
        try {
            String url = baseUrl + API_LIST_MODELS;
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            log.debug("Ollama服务不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取本地可用的大语言模型(LLM)列表
     */
    public static List<OllamaModel> getLocalLLMs() {
        return getLocalLLMs(DEFAULT_BASE_URL, false);
    }

    public static List<OllamaModel> getLocalLLMs(String baseUrl) {
        return getLocalLLMs(baseUrl, false);
    }

    public static List<OllamaModel> getLocalLLMs(boolean fetchDetails) {
        return getLocalLLMs(DEFAULT_BASE_URL, fetchDetails);
    }

    public static List<OllamaModel> getLocalLLMs(String baseUrl, boolean fetchDetails) {
        List<OllamaModel> allModels = getLocalModels(baseUrl);
        return allModels.stream()
                .filter(model -> model.getModelType() == OllamaModelDetails.ModelType.LLM)
                .collect(Collectors.toList());
    }

    /**
     * 拉取（下载）模型，使用流式请求直接获取进度
     */
    public static boolean pullModelWithStream(String modelName, TriConsumer<Long, Long, String> progressCallback) {
        return pullModelWithStream(DEFAULT_BASE_URL, modelName, progressCallback);
    }

    public static boolean pullModelWithStream(String baseUrl, String modelName, TriConsumer<Long, Long, String> progressCallback) {
        try {
            String url = baseUrl + API_PULL;
            log.info("开始流式下载模型: {}", modelName);

            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);

            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Accept", "application/json");

            String jsonBody = "{\"name\":\"" + modelName + "\"}";
            StringEntity entity = new StringEntity(jsonBody, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);

            CloseableHttpResponse response = httpClient.execute(httpPost);

            try {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode >= 200 && statusCode < 300) {
                    org.apache.http.HttpEntity responseEntity = response.getEntity();

                    if (responseEntity != null) {
                        try (BufferedReader reader = new BufferedReader(new InputStreamReader(responseEntity.getContent(), StandardCharsets.UTF_8))) {
                            String line;
                            while ((line = reader.readLine()) != null) {
                                if (line.trim().isEmpty()) continue;

                                try {
                                    JsonNode jsonNode = objectMapper.readTree(line);

                                    String status = jsonNode.has("status") ? jsonNode.get("status").asText() : "";

                                    if ("downloading".equals(status)) {
                                        status = "正在下载";
                                    } else if ("processing".equals(status)) {
                                        status = "正在处理";
                                    } else if ("verifying".equals(status)) {
                                        status = "正在验证";
                                    } else if ("preparing".equals(status)) {
                                        status = "准备中";
                                    } else if ("pulling".equals(status)) {
                                        status = "获取中";
                                    } else if (status.contains("pulling")) {
                                        status = "获取中";
                                    }  else if ("success".equals(status) || "done".equals(status) || "complete".equals(status)) {
                                        status = "完成";
                                    } else if ("failed".equals(status)) {
                                        status = "失败";
                                    }

                                    if (jsonNode.has("completed") && jsonNode.has("total")) {
                                        Long completed = jsonNode.get("completed").asLong();
                                        Long total = jsonNode.get("total").asLong();
                                        progressCallback.accept(completed, total, status);
                                    } else if (!status.isEmpty()) {
                                        progressCallback.accept(0L, 0L, status);
                                    }

                                    if ("success".equals(status) || "done".equals(status) || "complete".equals(status)) {
                                        break;
                                    }
                                } catch (Exception e) {
                                    log.warn("解析响应行时出错: {} - {}", line, e.getMessage());
                                }
                            }
                        }
                        return true;
                    }
                } else {
                    log.error("下载模型请求失败, 状态码: {}", statusCode);
                    progressCallback.accept(0L, 0L, "错误: HTTP " + statusCode);
                    return false;
                }
            } finally {
                response.close();
                httpClient.close();
            }
        } catch (Exception e) {
            log.error("流式下载模型时发生异常: {}", e.getMessage(), e);
            progressCallback.accept(0L, 0L, "错误: " + e.getMessage());
            return false;
        }
        return false;
    }

    /**
     * 删除模型
     */
    public static boolean deleteModel(String modelName) {
        return deleteModel(DEFAULT_BASE_URL, modelName);
    }

    public static boolean deleteModel(String baseUrl, String modelName) {
        try {
            String url = baseUrl + API_DELETE;

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("model", modelName);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<>(objectMapper.writeValueAsString(requestBody), headers);

            log.info("发送删除请求到Ollama API: {}, 模型: {}", url, modelName);

            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.DELETE,
                    requestEntity,
                    String.class
            );

            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            log.error("删除Ollama模型异常", e);
            return false;
        }
    }

    /**
     * 根据模型名称判断模型类型
     */
    private static OllamaModelDetails.ModelType determineModelType(String modelName) {
        if (modelName == null) {
            return OllamaModelDetails.ModelType.UNKNOWN;
        }
        
        String name = modelName.toLowerCase();
        
        // 嵌入模型特征
        if (name.contains("bge") || name.contains("embed") || name.contains("vector") || 
            name.contains("nomic-embed") || name.contains("m3") || (name.contains("large") && name.contains("bge"))) {
            return OllamaModelDetails.ModelType.EMBEDDING;
        }
        
        // 其他特殊模型类型暂时归类为UNKNOWN
        if (name.contains("vision") || name.contains("llava") || name.contains("minicpm") || name.contains("moondream")) {
            return OllamaModelDetails.ModelType.UNKNOWN;
        }
        
        // 默认为LLM（包括qwen、llama、deepseek、gemma、phi等主流对话模型）
        return OllamaModelDetails.ModelType.LLM;
    }
} 