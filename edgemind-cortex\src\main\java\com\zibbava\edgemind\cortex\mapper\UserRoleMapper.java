package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.UserRole;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户角色关联 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRole> {

    /**
     * 根据用户ID查询角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    @Select("SELECT role_id FROM sys_user_role WHERE user_id = #{userId}")
    List<Long> selectRoleIdsByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查询用户ID列表
     *
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    @Select("SELECT user_id FROM sys_user_role WHERE role_id = #{roleId}")
    List<Long> selectUserIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID列表查询角色ID列表
     *
     * @param userIds 用户ID列表
     * @return 角色ID列表
     */
    @Select("<script>" +
            "SELECT DISTINCT role_id FROM sys_user_role " +
            "WHERE user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</script>")
    List<Long> selectRoleIdsByUserIds(@Param("userIds") List<Long> userIds);

    /**
     * 统计角色下的用户数量
     *
     * @param roleId 角色ID
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM sys_user_role WHERE role_id = #{roleId}")
    Long countUsersByRoleId(@Param("roleId") Long roleId);

    /**
     * 统计用户的角色数量
     *
     * @param userId 用户ID
     * @return 角色数量
     */
    @Select("SELECT COUNT(*) FROM sys_user_role WHERE user_id = #{userId}")
    Long countRolesByUserId(@Param("userId") Long userId);

    /**
     * 批量插入用户角色关联
     *
     * @param userRoles 用户角色关联列表
     * @return 插入数量
     */
    @Insert("<script>" +
            "INSERT INTO sys_user_role (user_id, role_id) VALUES " +
            "<foreach collection='userRoles' item='ur' separator=','>" +
            "(#{ur.userId}, #{ur.roleId})" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("userRoles") List<UserRole> userRoles);
}