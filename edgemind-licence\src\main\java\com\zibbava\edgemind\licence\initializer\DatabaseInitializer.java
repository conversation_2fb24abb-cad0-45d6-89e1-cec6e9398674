package com.zibbava.edgemind.licence.initializer;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.*;

/**
 * 极简数据库初始化器
 * 功能：检测MySQL -> 设置密码 -> 创建数据库
 */
@Component
@Order(1)
public class DatabaseInitializer implements CommandLineRunner {

    @Value("${spring.datasource.url}")
    private String datasourceUrl;

    @Value("${spring.datasource.username}")
    private String username;

    @Value("${spring.datasource.password:}")
    private String password;

    @Override
    public void run(String... args) {
        try {
            // 检查并设置密码
            if (!isPasswordSet()) {
                trySetPassword();
            } else {
            }

            createDatabase();

            // 检查并初始化表
            if (!isTablesExist()) {
                initializeTables();
            } else {
            }

        } catch (Exception e) {
        }
    }

    private boolean isPasswordSet() {
        try (Connection conn = DriverManager.getConnection(datasourceUrl, username, password)) {
            // 如果能用配置的密码连接成功，说明密码已设置
            return true;
        } catch (SQLException e) {
            // 连接失败，可能密码未设置或密码错误
            return false;
        }
    }

    private void trySetPassword() {
        try (Connection conn = DriverManager.getConnection(datasourceUrl, username, "wkg123456");
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(String.format("ALTER USER '%s'@'%%' IDENTIFIED BY '%s'", username, password));
        } catch (SQLException e) {
        }
    }

    private void createDatabase() throws SQLException {
        String dbName = datasourceUrl.split("/")[3].split("\\?")[0];

        try (Connection conn = DriverManager.getConnection(datasourceUrl, username, password);
             Statement stmt = conn.createStatement()) {
            stmt.executeUpdate(String.format("CREATE DATABASE IF NOT EXISTS `%s` CHARACTER SET utf8mb4", dbName));
        }
    }

    private void initializeTables() {
        try {
            // 执行建表脚本
            executeSchemaScript();
        } catch (Exception e) {
        }
    }

    private boolean isTablesExist() throws SQLException {
        try (Connection conn = DriverManager.getConnection(datasourceUrl, username, password);
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SHOW TABLES LIKE 'sys_user'")) {
            return rs.next();
        }
    }

    private void executeSchemaScript() throws Exception {
        ClassPathResource resource = new ClassPathResource("sql/schema.sql");

        // 构建支持多语句执行的数据库连接URL
        String multiStatementUrl = datasourceUrl;
        if (!multiStatementUrl.contains("allowMultiQueries")) {
            multiStatementUrl += (multiStatementUrl.contains("?") ? "&" : "?") + "allowMultiQueries=true";
        }

        try (InputStream inputStream = resource.getInputStream();
             Connection conn = DriverManager.getConnection(multiStatementUrl, username, password)) {

            // 读取整个SQL文件内容，保持原样不做任何修改
            String sqlContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);

            // 一次性执行整个SQL脚本文件
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(sqlContent);
            } catch (SQLException e) {
                // 如果一次性执行失败，记录错误但不中断程序
            }
        }
    }
} 