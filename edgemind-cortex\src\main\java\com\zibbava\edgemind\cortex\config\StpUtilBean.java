package com.zibbava.edgemind.cortex.config;

import cn.dev33.satoken.stp.StpUtil;
import org.springframework.stereotype.Component;

/**
 * StpUtil工具类的Spring Bean包装器
 * 用于在Thymeleaf模板中调用Sa-Token的权限检查方法
 * 
 * <AUTHOR>
 */
@Component("stpUtil")
public class StpUtilBean {

    /**
     * 检查当前用户是否拥有指定权限
     * 
     * @param permission 权限标识
     * @return 是否拥有权限
     */
    public boolean hasPermission(String permission) {
        try {
            // 如果未登录，直接返回false
            if (!StpUtil.isLogin()) {
                return false;
            }
            return StpUtil.hasPermission(permission);
        } catch (Exception e) {
            // 发生异常时返回false，避免模板渲染错误
            return false;
        }
    }

    /**
     * 检查当前用户是否拥有指定角色
     * 
     * @param role 角色标识
     * @return 是否拥有角色
     */
    public boolean hasRole(String role) {
        try {
            if (!StpUtil.isLogin()) {
                return false;
            }
            return StpUtil.hasRole(role);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查当前用户是否已登录
     * 
     * @return 是否已登录
     */
    public boolean isLogin() {
        try {
            return StpUtil.isLogin();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取当前登录用户ID
     * 
     * @return 用户ID，未登录返回null
     */
    public Object getLoginId() {
        try {
            if (!StpUtil.isLogin()) {
                return null;
            }
            return StpUtil.getLoginId();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前登录用户ID（Long类型）
     * 
     * @return 用户ID，未登录返回null
     */
    public Long getLoginIdAsLong() {
        try {
            if (!StpUtil.isLogin()) {
                return null;
            }
            return StpUtil.getLoginIdAsLong();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前登录用户的用户名
     * 
     * @return 用户名，未登录返回null
     */
    public String getUsername() {
        try {
            if (!StpUtil.isLogin()) {
                return null;
            }
            Object username = StpUtil.getSession().get("username");
            return username != null ? username.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取当前登录用户的昵称
     * 
     * @return 昵称，未登录返回null
     */
    public String getNickname() {
        try {
            if (!StpUtil.isLogin()) {
                return null;
            }
            Object nickname = StpUtil.getSession().get("nickname");
            return nickname != null ? nickname.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }
}
