package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zibbava.edgemind.cortex.annotation.OperationLog;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.entity.OperationLog.OperationType;
import com.zibbava.edgemind.cortex.entity.SystemConfig;
import com.zibbava.edgemind.cortex.service.SystemConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 系统配置控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/config")
@RequiredArgsConstructor
@Validated
public class SystemConfigController {

    private final SystemConfigService systemConfigService;

    /**
     * 获取所有配置
     */
    @GetMapping("/list")
    @SaCheckPermission("system:config:list")
    @OperationLog(operationType = OperationType.QUERY, module = "系统配置", description = "查询配置列表")
    public ResponseEntity<ApiResponse<List<SystemConfig>>> getAllConfigs() {
        List<SystemConfig> result = systemConfigService.getAllEnabledConfigs();
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 根据分类获取配置
     */
    @GetMapping("/category/{category}")
    @SaCheckPermission("system:config:list")
    @OperationLog(operationType = OperationType.QUERY, module = "系统配置", description = "按分类查询配置")
    public ResponseEntity<ApiResponse<List<SystemConfig>>> getConfigsByCategory(@PathVariable String category) {
        List<SystemConfig> result = systemConfigService.getConfigsByCategory(category);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 获取配置分类列表
     */
    @GetMapping("/categories")
    @SaCheckPermission("system:config:list")
    public ResponseEntity<ApiResponse<List<String>>> getConfigCategories() {
        List<String> result = systemConfigService.getConfigCategories();
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 根据配置键获取配置值
     */
    @GetMapping("/value/{configKey}")
    @SaCheckPermission("system:config:list")
    public ResponseEntity<ApiResponse<String>> getConfigValue(@PathVariable String configKey) {
        String result = systemConfigService.getConfigValue(configKey);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 设置配置值
     */
    @PostMapping("/value")
    @SaCheckPermission("system:config:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "系统配置", description = "设置配置值")
    public ResponseEntity<ApiResponse<Void>> setConfigValue(@RequestParam String configKey,
                                                           @RequestParam String configValue,
                                                           @RequestParam(required = false) String description) {
        systemConfigService.setConfigValue(configKey, configValue, description);
        return ResponseEntity.ok(ApiResponse.success("配置设置成功"));
    }

    /**
     * 批量设置配置
     */
    @PostMapping("/batch")
    @SaCheckPermission("system:config:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "系统配置", description = "批量设置配置")
    public ResponseEntity<ApiResponse<Void>> batchSetConfig(@RequestBody Map<String, String> configs) {
        systemConfigService.batchSetConfig(configs);
        return ResponseEntity.ok(ApiResponse.success("配置批量设置成功"));
    }

    /**
     * 创建配置
     */
    @PostMapping
    @SaCheckPermission("system:config:update")
    @OperationLog(operationType = OperationType.CREATE, module = "系统配置", description = "创建配置")
    public ResponseEntity<ApiResponse<Void>> createConfig(@Valid @RequestBody SystemConfig config) {
        systemConfigService.save(config);
        return ResponseEntity.ok(ApiResponse.success("配置创建成功"));
    }

    /**
     * 更新配置
     */
    @PutMapping("/{configId}")
    @SaCheckPermission("system:config:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "系统配置", description = "更新配置")
    public ResponseEntity<ApiResponse<Void>> updateConfig(@PathVariable Long configId,
                                                         @Valid @RequestBody SystemConfig config) {
        config.setId(configId);
        systemConfigService.updateById(config);
        return ResponseEntity.ok(ApiResponse.success("配置更新成功"));
    }

    /**
     * 删除配置
     */
    @DeleteMapping("/{configKey}")
    @SaCheckPermission("system:config:update")
    @OperationLog(operationType = OperationType.DELETE, module = "系统配置", description = "删除配置")
    public ResponseEntity<ApiResponse<Void>> deleteConfig(@PathVariable String configKey) {
        systemConfigService.deleteConfig(configKey);
        return ResponseEntity.ok(ApiResponse.success("配置删除成功"));
    }

    /**
     * 刷新配置缓存
     */
    @PostMapping("/refresh-cache")
    @SaCheckPermission("system:config:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "系统配置", description = "刷新配置缓存")
    public ResponseEntity<ApiResponse<Void>> refreshCache() {
        systemConfigService.refreshCache();
        return ResponseEntity.ok(ApiResponse.success("配置缓存刷新成功"));
    }

    /**
     * 导出配置
     */
    @PostMapping("/export")
    @SaCheckPermission("system:config:list")
    @OperationLog(operationType = OperationType.EXPORT, module = "系统配置", description = "导出配置")
    public ResponseEntity<ApiResponse<List<SystemConfig>>> exportConfigs(@RequestParam(required = false) String category) {
        List<SystemConfig> result = systemConfigService.exportConfigs(category);
        return ResponseEntity.ok(ApiResponse.success("导出成功", result));
    }

    /**
     * 导入配置
     */
    @PostMapping("/import")
    @SaCheckPermission("system:config:update")
    @OperationLog(operationType = OperationType.IMPORT, module = "系统配置", description = "导入配置")
    public ResponseEntity<ApiResponse<SystemConfigService.ImportResult>> importConfigs(
            @RequestBody List<SystemConfig> configs,
            @RequestParam(defaultValue = "false") boolean overwrite) {
        SystemConfigService.ImportResult result = systemConfigService.importConfigs(configs, overwrite);
        return ResponseEntity.ok(ApiResponse.success("导入完成", result));
    }
}
