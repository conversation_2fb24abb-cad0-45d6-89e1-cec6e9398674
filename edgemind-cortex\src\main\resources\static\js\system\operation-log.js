/**
 * 操作日志页面JavaScript - 使用EdgeMind组件系统
 */

// 全局变量
let currentPage = 1;
let pageSize = 10;

// 获取上下文路径
const CONTEXT_PATH = window.location.pathname.startsWith('/wkg') ? '/wkg' : '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPage();
});

/**
 * 初始化页面
 */
function initPage() {
    loadLogList();
    loadStatistics();
    bindEvents();
    setDefaultTimeRange();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 搜索表单提交
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadLogList();
    });

    // 保留天数输入框变化
    document.getElementById('retentionDays').addEventListener('input', function() {
        const days = this.value;
        document.querySelector('#cleanLogModal .form-text span').textContent = days;
    });
}

/**
 * 统一的API请求方法 - 使用EdgeMind标准
 */
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include'
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(`${CONTEXT_PATH}${url}`, finalOptions);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.code !== 200) {
            throw new Error(data.message || '操作失败');
        }

        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

/**
 * 显示Toast提示 - 使用EdgeMind组件
 */
function showToast(message, type = 'info') {
    // 检查是否有EdgeMind的showToast函数
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        // 回退到简单的alert
        alert(`${type.toUpperCase()}: ${message}`);
    }
}

/**
 * 设置默认时间范围（最近7天）
 */
function setDefaultTimeRange() {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('searchStartTime').value = formatDateTimeForInput(weekAgo);
    document.getElementById('searchEndTime').value = formatDateTimeForInput(now);
}

/**
 * 加载操作日志列表
 */
async function loadLogList() {
    try {
        const searchParams = {
            pageNum: currentPage,
            pageSize: pageSize,
            // 注意：后端API使用userId，这里暂时保留username字段用于前端显示
            // 实际应该根据username查询userId，或者修改后端API支持username查询
            operationType: document.getElementById('searchOperationType').value,
            module: document.getElementById('searchModule').value,
            startTime: document.getElementById('searchStartTime').value,
            endTime: document.getElementById('searchEndTime').value
        };

        // 如果有用户名搜索，需要先根据用户名查询用户ID
        const username = document.getElementById('searchUsername').value;
        if (username) {
            // 这里应该调用用户查询API获取userId，暂时跳过用户名搜索
            console.log('用户名搜索功能需要额外的API支持');
        }

        // 移除空值参数
        Object.keys(searchParams).forEach(key => {
            if (!searchParams[key]) {
                delete searchParams[key];
            }
        });

        // 构建查询字符串
        const queryString = new URLSearchParams(searchParams).toString();
        const data = await apiRequest(`/api/system/operation-log/list?${queryString}`);

        renderLogTable(data.data.records || []);
        renderPagination(data.data);
    } catch (error) {
        showToast('加载操作日志失败: ' + error.message, 'danger');
    }
}

/**
 * 渲染日志表格
 */
function renderLogTable(logs) {
    const tbody = document.getElementById('logTableBody');
    tbody.innerHTML = '';

    logs.forEach(log => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatDateTime(log.createTime)}</td>
            <td>${log.username || '-'}</td>
            <td>
                <span class="badge operation-type-badge ${getOperationTypeBadgeClass(log.operationType)}">
                    ${getOperationTypeText(log.operationType)}
                </span>
            </td>
            <td>${log.module}</td>
            <td class="log-detail" title="${log.description}">${log.description}</td>
            <td>${log.ipAddress || '-'}</td>
            <td>${log.executionTime ? log.executionTime + 'ms' : '-'}</td>
            <td>
                <span class="badge status-badge ${log.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${log.status === 1 ? '成功' : '失败'}
                </span>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-info" onclick="showLogDetail(${log.id})" title="查看详情">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

/**
 * 渲染分页组件
 */
function renderPagination(pageData) {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;

    pagination.innerHTML = '';

    const totalPages = pageData.pages || 1;
    const current = pageData.current || 1;
    const total = pageData.total || 0;

    // 如果没有数据或只有一页，不显示分页
    if (totalPages <= 1) {
        if (total === 0) {
            pagination.innerHTML = '<div class="text-center text-muted">暂无数据</div>';
        }
        return;
    }

    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${current === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" data-page="${current - 1}">上一页</a>`;
    pagination.appendChild(prevLi);

    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(totalPages, current + 2);

    // 如果起始页大于1，显示第一页和省略号
    if (startPage > 1) {
        const firstLi = document.createElement('li');
        firstLi.className = 'page-item';
        firstLi.innerHTML = '<a class="page-link" href="#" data-page="1">1</a>';
        pagination.appendChild(firstLi);

        if (startPage > 2) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            ellipsisLi.innerHTML = '<span class="page-link">...</span>';
            pagination.appendChild(ellipsisLi);
        }
    }

    // 显示页码
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === current ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        pagination.appendChild(li);
    }

    // 如果结束页小于总页数，显示省略号和最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            ellipsisLi.innerHTML = '<span class="page-link">...</span>';
            pagination.appendChild(ellipsisLi);
        }

        const lastLi = document.createElement('li');
        lastLi.className = 'page-item';
        lastLi.innerHTML = `<a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a>`;
        pagination.appendChild(lastLi);
    }

    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${current === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" data-page="${current + 1}">下一页</a>`;
    pagination.appendChild(nextLi);

    // 绑定点击事件
    pagination.addEventListener('click', function(e) {
        e.preventDefault();
        if (e.target.tagName === 'A' && !e.target.closest('.disabled')) {
            const page = parseInt(e.target.getAttribute('data-page'));
            if (page > 0 && page <= totalPages) {
                changePage(page);
            }
        }
    });

    // 显示分页信息
    const start = (current - 1) * pageSize + 1;
    const end = Math.min(current * pageSize, total);
    const infoDiv = document.createElement('div');
    infoDiv.className = 'text-center mt-3 text-muted small';
    infoDiv.innerHTML = `显示第 ${start}-${end} 条，共 ${total} 条记录`;
    pagination.appendChild(infoDiv);
}

/**
 * 切换页面
 */
function changePage(page) {
    if (page < 1) return;
    currentPage = page;
    loadLogList();
}

/**
 * 加载统计数据
 */
async function loadStatistics() {
    try {
        const data = await apiRequest('/api/system/operation-log/statistics?days=7');
        const stats = data.data;

        document.getElementById('totalOperations').textContent = stats.totalOperations || 0;
        document.getElementById('todayOperations').textContent = stats.todayOperations || 0;

        // 计算成功率
        const successRate = stats.totalOperations > 0 ?
            Math.round((stats.successOperations / stats.totalOperations) * 100) : 0;
        document.getElementById('successRate').textContent = successRate + '%';

        // 活跃用户数
        document.getElementById('activeUsers').textContent = stats.activeUsers || '-';
    } catch (error) {
        console.error('Error loading statistics:', error);
        // 设置默认值
        document.getElementById('totalOperations').textContent = '-';
        document.getElementById('todayOperations').textContent = '-';
        document.getElementById('successRate').textContent = '-';
        document.getElementById('activeUsers').textContent = '-';
    }
}

/**
 * 刷新统计数据
 */
function refreshStatistics() {
    loadStatistics();
    showToast('统计数据已刷新', 'success');
}

/**
 * 重置搜索条件
 */
function resetSearch() {
    document.getElementById('searchForm').reset();
    setDefaultTimeRange();
    currentPage = 1;
    loadLogList();
}

/**
 * 设置快速时间范围
 */
function setQuickTimeRange(range) {
    const now = new Date();
    const startTime = new Date();

    switch(range) {
        case 'today':
            startTime.setHours(0, 0, 0, 0);
            break;
        case 'week':
            startTime.setDate(now.getDate() - 7);
            break;
        case 'month':
            startTime.setMonth(now.getMonth() - 1);
            break;
    }

    document.getElementById('searchStartTime').value = formatDateTimeForInput(startTime);
    document.getElementById('searchEndTime').value = formatDateTimeForInput(now);

    // 自动搜索
    currentPage = 1;
    loadLogList();
}

/**
 * 显示日志详情
 */
function showLogDetail(logId) {
    // 从当前表格中找到对应的日志记录
    const logs = Array.from(document.querySelectorAll('#logTableBody tr')).map(row => {
        const cells = row.cells;
        return {
            createTime: cells[0].textContent,
            username: cells[1].textContent,
            operationType: cells[2].textContent.trim(),
            module: cells[3].textContent,
            description: cells[4].getAttribute('title'),
            ipAddress: cells[5].textContent,
            executionTime: cells[6].textContent,
            status: cells[7].textContent.trim()
        };
    });
    
    // 这里应该通过API获取完整的日志详情
    // 为了演示，我们使用模拟数据
    const log = {
        username: '演示用户',
        createTime: new Date().toLocaleString(),
        operationType: 'CREATE',
        module: '用户管理',
        description: '创建用户',
        requestUrl: '/api/system/user',
        requestMethod: 'POST',
        ipAddress: '*************',
        executionTime: '125ms',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        requestParams: '{"username":"testuser","email":"<EMAIL>"}',
        responseResult: '{"success":true,"data":{"id":123}}',
        errorMessage: null
    };
    
    // 填充模态框数据
    document.getElementById('detailUsername').textContent = log.username || '-';
    document.getElementById('detailCreateTime').textContent = log.createTime || '-';
    document.getElementById('detailOperationType').textContent = getOperationTypeText(log.operationType);
    document.getElementById('detailModule').textContent = log.module || '-';
    document.getElementById('detailDescription').textContent = log.description || '-';
    document.getElementById('detailRequestUrl').textContent = log.requestUrl || '-';
    document.getElementById('detailRequestMethod').textContent = log.requestMethod || '-';
    document.getElementById('detailIpAddress').textContent = log.ipAddress || '-';
    document.getElementById('detailExecutionTime').textContent = log.executionTime || '-';
    document.getElementById('detailUserAgent').textContent = log.userAgent || '-';
    
    // 处理可选字段
    const requestParamsSection = document.getElementById('requestParamsSection');
    const responseResultSection = document.getElementById('responseResultSection');
    const errorMessageSection = document.getElementById('errorMessageSection');
    
    if (log.requestParams) {
        requestParamsSection.style.display = 'block';
        document.getElementById('detailRequestParams').textContent = log.requestParams;
    } else {
        requestParamsSection.style.display = 'none';
    }
    
    if (log.responseResult) {
        responseResultSection.style.display = 'block';
        document.getElementById('detailResponseResult').textContent = log.responseResult;
    } else {
        responseResultSection.style.display = 'none';
    }
    
    if (log.errorMessage) {
        errorMessageSection.style.display = 'block';
        document.getElementById('detailErrorMessage').textContent = log.errorMessage;
    } else {
        errorMessageSection.style.display = 'none';
    }
    
    const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
    modal.show();
}

/**
 * 设置快速时间范围
 */
function setQuickTimeRange(range) {
    const now = new Date();
    let startTime;
    
    switch (range) {
        case 'today':
            startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
        case 'week':
            startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        case 'month':
            startTime = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
        default:
            return;
    }
    
    document.getElementById('searchStartTime').value = formatDateTimeForInput(startTime);
    document.getElementById('searchEndTime').value = formatDateTimeForInput(now);
    
    // 自动搜索
    currentPage = 1;
    loadLogList();
}

/**
 * 重置搜索
 */
function resetSearch() {
    document.getElementById('searchForm').reset();
    setDefaultTimeRange();
    currentPage = 1;
    loadLogList();
}

/**
 * 导出日志
 */
async function exportLogs() {
    try {
        const searchParams = {
            username: document.getElementById('searchUsername').value,
            operationType: document.getElementById('searchOperationType').value,
            module: document.getElementById('searchModule').value,
            status: document.getElementById('searchStatus').value,
            startTime: document.getElementById('searchStartTime').value,
            endTime: document.getElementById('searchEndTime').value
        };

        // 移除空值参数
        Object.keys(searchParams).forEach(key => {
            if (!searchParams[key]) {
                delete searchParams[key];
            }
        });

        const queryString = new URLSearchParams(searchParams).toString();
        const data = await apiRequest(`/api/system/operation-log/export?${queryString}`, {
            method: 'POST'
        });

        if (data.code === 200) {
            showToast('日志导出成功，共 ' + (data.data?.length || 0) + ' 条记录', 'success');
            // 这里可以实现实际的文件下载逻辑
        } else {
            showToast('导出失败: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('导出失败: ' + error.message, 'danger');
    }
}

/**
 * 显示清理日志模态框
 */
function showCleanModal() {
    const modal = new bootstrap.Modal(document.getElementById('cleanLogModal'));
    modal.show();
}

/**
 * 清理日志
 */
async function cleanLogs() {
    const retentionDays = document.getElementById('retentionDays').value;

    if (!retentionDays || retentionDays < 1) {
        showToast('请输入有效的保留天数', 'warning');
        return;
    }

    if (!confirm(`确定要删除 ${retentionDays} 天之前的所有日志吗？此操作不可恢复！`)) {
        return;
    }

    try {
        const data = await apiRequest(`/api/system/operation-log/clean?retentionDays=${retentionDays}`, {
            method: 'POST'
        });

        if (data.code === 200) {
            showToast(`日志清理完成，共清理 ${data.data} 条记录`, 'success');
            bootstrap.Modal.getInstance(document.getElementById('cleanLogModal')).hide();
            loadLogList();
            loadStatistics();
        } else {
            showToast('日志清理失败: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('清理失败: ' + error.message, 'danger');
    }
}

/**
 * 获取操作类型对应的徽章样式
 */
function getOperationTypeBadgeClass(operationType) {
    const typeMap = {
        'CREATE': 'bg-success',
        'UPDATE': 'bg-primary',
        'DELETE': 'bg-danger',
        'LOGIN': 'bg-info',
        'LOGOUT': 'bg-secondary',
        'QUERY': 'bg-light text-dark',
        'EXPORT': 'bg-warning',
        'IMPORT': 'bg-warning',
        'RESET_PASSWORD': 'bg-danger',
        'ASSIGN_ROLE': 'bg-primary',
        'GRANT_PERMISSION': 'bg-primary'
    };
    return typeMap[operationType] || 'bg-secondary';
}

/**
 * 获取操作类型文本
 */
function getOperationTypeText(operationType) {
    const typeMap = {
        'CREATE': '新增',
        'UPDATE': '修改',
        'DELETE': '删除',
        'LOGIN': '登录',
        'LOGOUT': '登出',
        'QUERY': '查询',
        'EXPORT': '导出',
        'IMPORT': '导入',
        'RESET_PASSWORD': '重置密码',
        'ASSIGN_ROLE': '分配角色',
        'GRANT_PERMISSION': '授权'
    };
    return typeMap[operationType] || operationType;
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

/**
 * 格式化日期时间为输入框格式
 */
function formatDateTimeForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// Toast提示功能已移至通用库中
