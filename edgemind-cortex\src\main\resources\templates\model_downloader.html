<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型中心</title>
    <!-- Vendor CSS -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css" />
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css" />
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-select.min.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/wkg/css/features/model-downloader/model-downloader.css" />
    <link rel="stylesheet" href="/wkg/css/components/scrollbar-styles.css" />
</head>
<body>
<!-- 主要内容区 -->
<div class="container-fluid mt-4 mb-4">
    <div class="row">
        <!-- 左侧栏：系统信息和过滤器 -->
        <div class="col-lg-3 col-md-4 sidebar-column">
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">系统概览</h5>
                </div>
                <div class="card-body">
                    <div id="system-info">
                        <div class="loader-sm"></div>
                        <p class="text-muted text-center mt-2 small">正在检测系统信息...</p>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">筛选模型</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="size-filter" class="form-label">模型大小</label>
                        <select class="form-select" id="size-filter">
                            <option value="all" selected>所有大小</option>
                            <option value="small">小型 (< 5GB)</option>
                            <option value="medium">中型 (5-10GB)</option>
                            <option value="large">大型 (> 10GB)</option>
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="show-recommended">
                        <label class="form-check-label" for="show-recommended">
                            优先推荐
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容：模型列表 -->
        <div class="col-lg-9 col-md-8 main-content-column">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center flex-wrap">
                    <h5 class="card-title mb-0 me-3">可用模型</h5>
                    <div class="input-group input-group-sm search-bar mt-2 mt-md-0">
                        <input type="text" class="form-control" id="search-model" placeholder="搜索名称或ID...">
                        <button class="btn btn-outline-secondary" type="button" id="refresh-btn" title="刷新列表">
                            <i class="bi bi-arrow-repeat"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="model-list-container">
                        <div class="text-center my-5" id="loading-container">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在获取模型列表...</p>
                        </div>
                        <div id="model-grid">
                            <!-- 模型卡片将由JS动态生成 -->
                        </div>
                        <div class="alert alert-light text-center mt-3 d-none" id="no-models-message">
                            <i class="bi bi-database-slash fs-3 d-block mb-2"></i>
                            没有找到符合条件的模型
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模型详情模态框 -->
<div class="modal fade" id="model-details-modal" tabindex="-1" aria-labelledby="model-details-label" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="model-details-label">模型详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6 modal-info-section">
                        <h6 class="modal-section-title">基本信息</h6>
                        <div class="modal-info-item">
                            <span class="modal-info-key">名称:</span>
                            <span class="modal-info-value" id="modal-model-name"></span>
                        </div>
                        <div class="modal-info-item">
                            <span class="modal-info-key">大小:</span>
                            <span class="modal-info-value" id="modal-model-size"></span>
                        </div>
                        <div class="modal-info-item">
                            <span class="modal-info-key">类型:</span>
                            <span class="modal-info-value" id="modal-model-type"></span>
                        </div>
                        <div class="modal-info-item">
                            <span class="modal-info-key">推荐度:</span>
                            <span class="modal-info-value" id="modal-model-compatibility"></span>
                        </div>
                    </div>
                    <div class="col-md-6 modal-info-section">
                        <h6 class="modal-section-title">推荐配置</h6>
                        <div class="modal-info-item">
                            <span class="modal-info-key">最低内存:</span>
                            <span class="modal-info-value" id="modal-min-ram"></span>
                        </div>
                        <div class="modal-info-item">
                            <span class="modal-info-key">显存要求:</span>
                            <span class="modal-info-value" id="modal-gpu-req"></span>
                        </div>
                        <div class="modal-info-item">
                            <span class="modal-info-key">磁盘空间:</span>
                            <span class="modal-info-value" id="modal-disk-space"></span>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <h6 class="modal-section-title">模型描述</h6>
                    <p id="modal-model-description" class="modal-description-text"></p>
                </div>
                <div id="download-progress-container" class="d-none mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <h6 class="modal-section-title mb-0">下载进度</h6>
                        <small id="download-status" class="text-muted modal-progress-status">准备下载中...</small>
                    </div>
                    <div class="progress modal-progress" style="height: 8px;">
                        <div id="download-progress-bar" class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="download-model-btn">下载模型</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1100">
    <!-- Toasts will be appended here -->
</div>

<!-- Vendor JS -->
<script src="/wkg/js/vendor/jquery-3.7.1.min.js"></script>
<script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
<script src="/wkg/js/vendor/bootstrap-select.min.js"></script>
<!-- Custom JS -->
<script src="/wkg/js/features/model-downloader/model-downloader.js"></script>
</body>
</html>