package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.entity.OperationLog;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志服务接口
 * 
 * <AUTHOR>
 */
public interface OperationLogService extends IService<OperationLog> {

    /**
     * 记录操作日志
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param operationType 操作类型
     * @param module 操作模块
     * @param description 操作描述
     * @param requestUrl 请求URL
     * @param requestMethod 请求方法
     * @param requestParams 请求参数
     * @param responseResult 响应结果
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @param executionTime 执行时间
     * @param status 操作状态
     * @param errorMessage 错误信息
     */
    void recordLog(Long userId, String username, OperationLog.OperationType operationType,
                   String module, String description, String requestUrl, String requestMethod,
                   String requestParams, String responseResult, String ipAddress,
                   String userAgent, Long executionTime, Integer status, String errorMessage);

    /**
     * 记录成功操作日志
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param operationType 操作类型
     * @param module 操作模块
     * @param description 操作描述
     */
    void recordSuccessLog(Long userId, String username, OperationLog.OperationType operationType,
                         String module, String description);

    /**
     * 记录失败操作日志
     * 
     * @param userId 用户ID
     * @param username 用户名
     * @param operationType 操作类型
     * @param module 操作模块
     * @param description 操作描述
     * @param errorMessage 错误信息
     */
    void recordFailureLog(Long userId, String username, OperationLog.OperationType operationType,
                         String module, String description, String errorMessage);

    /**
     * 分页查询操作日志
     * 
     * @param pageNum 页码
     * @param pageSize 页面大小
     * @param userId 用户ID
     * @param operationType 操作类型
     * @param module 操作模块
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页结果
     */
    IPage<OperationLog> getLogPage(Integer pageNum, Integer pageSize, Long userId,
                                   String operationType, String module,
                                   LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取用户最近的操作日志
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 操作日志列表
     */
    List<OperationLog> getRecentLogsByUserId(Long userId, int limit);

    /**
     * 统计指定时间范围内的操作次数
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作次数
     */
    Long countOperationsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计各模块的操作次数
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    List<Map<String, Object>> countOperationsByModule(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 清理过期日志
     * 
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    int cleanExpiredLogs(int retentionDays);

    /**
     * 导出操作日志
     * 
     * @param userId 用户ID
     * @param operationType 操作类型
     * @param module 操作模块
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 日志列表
     */
    List<OperationLog> exportLogs(Long userId, String operationType, String module,
                                 LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取操作统计数据（用于仪表板）
     * 
     * @param days 统计天数
     * @return 统计数据
     */
    Map<String, Object> getOperationStatistics(int days);
}
