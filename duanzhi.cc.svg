<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <defs>
    <!-- 背景渐变 -->
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </radialGradient>
    
    <!-- 文字渐变 -->
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f0f9ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e0f2fe;stop-opacity:1" />
    </linearGradient>
    
    <!-- 文字阴影滤镜 -->
    <filter id="textShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="1" dy="2" stdDeviation="2" flood-color="rgba(0,0,0,0.3)"/>
    </filter>
  </defs>
  
  <!-- 外圆环装饰 -->
  <circle cx="100" cy="100" r="98" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
  
  <!-- 圆形背景 -->
  <circle cx="100" cy="100" r="95" fill="url(#bgGradient)" stroke="#ffffff" stroke-width="3"/>
  
  <!-- 内圆环装饰 -->
  <circle cx="100" cy="100" r="85" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1" stroke-dasharray="5,5"/>
  
  <!-- 文字 duanzhi.cc -->
  <text x="100" y="110" font-family="'SF Pro Display', 'Helvetica Neue', Arial, sans-serif" font-size="32" font-weight="800" fill="url(#textGradient)" text-anchor="middle" dominant-baseline="middle" filter="url(#textShadow)" letter-spacing="-0.5px">duanzhi.cc</text>
</svg>