package com.zibbava.edgemind.cortex.demo.util;

import io.weaviate.client.v1.misc.model.StopwordConfig;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 停用词加载工具类
 * 从文件中读取停用词并转换为Weaviate配置
 */
public class StopWordsLoader {

    private static final String DEFAULT_STOPWORDS_FILE = "chinese_stopwords.txt";
    private static Set<String> stopWordsCache = null;

    /**
     * 从默认文件加载停用词
     */
    public static Set<String> loadStopWords() {
        return loadStopWords(DEFAULT_STOPWORDS_FILE);
    }

    /**
     * 从指定文件加载停用词
     */
    public static Set<String> loadStopWords(String fileName) {
        if (stopWordsCache != null) {
            return stopWordsCache;
        }

        Set<String> stopWords = new HashSet<>();
        
        try (InputStream inputStream = StopWordsLoader.class.getClassLoader().getResourceAsStream(fileName);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            
            if (inputStream == null) {
                System.err.println("停用词文件未找到: " + fileName);
                return stopWords;
            }

            String line;
            int lineNumber = 0;
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                line = line.trim();
                
                // 跳过空行和注释行
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }
                
                stopWords.add(line);
            }
            
            stopWordsCache = stopWords;
            System.out.println("成功加载停用词: " + stopWords.size() + " 个，来源文件: " + fileName);
            
        } catch (IOException e) {
            System.err.println("加载停用词文件失败: " + e.getMessage());
        }
        
        return stopWords;
    }

    /**
     * 创建Weaviate停用词配置
     * 结合中文预设和自定义停用词文件
     */
    public static StopwordConfig createStopwordConfig() {
        return createStopwordConfig(DEFAULT_STOPWORDS_FILE);
    }

    /**
     * 创建Weaviate停用词配置
     * 使用自定义停用词文件（Weaviate不支持"zh"预设，使用"none"预设+自定义词汇）
     */
    public static StopwordConfig createStopwordConfig(String fileName) {
        Set<String> customStopWords = loadStopWords(fileName);
        
        // 转换为数组
        String[] additions = customStopWords.toArray(new String[0]);
        
        return StopwordConfig.builder()
                .preset("none") // 使用空预设，因为Weaviate不支持"zh"预设
                .additions(additions) // 添加自定义中文停用词
                .build();
    }

    /**
     * 获取停用词统计信息
     */
    public static StopWordsInfo getStopWordsInfo() {
        return getStopWordsInfo(DEFAULT_STOPWORDS_FILE);
    }

    /**
     * 获取停用词统计信息
     */
    public static StopWordsInfo getStopWordsInfo(String fileName) {
        Set<String> stopWords = loadStopWords(fileName);
        
        // 按类别统计（基于注释）
        int basicWords = 0;
        int pronouns = 0;
        int particles = 0;
        int timeWords = 0;
        int adverbs = 0;
        int conjunctions = 0;
        int prepositions = 0;
        int quantifiers = 0;
        int internetSlang = 0;
        int colloquial = 0;
        int others = 0;

        try (InputStream inputStream = StopWordsLoader.class.getClassLoader().getResourceAsStream(fileName);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            
            if (inputStream == null) {
                return new StopWordsInfo(stopWords.size(), 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, fileName);
            }

            String line;
            String currentCategory = "其他";
            
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                
                if (line.startsWith("# 基础停用词")) {
                    currentCategory = "基础";
                } else if (line.startsWith("# 代词")) {
                    currentCategory = "代词";
                } else if (line.startsWith("# 助词和语气词")) {
                    currentCategory = "助词";
                } else if (line.startsWith("# 时间词")) {
                    currentCategory = "时间";
                } else if (line.startsWith("# 程度副词")) {
                    currentCategory = "副词";
                } else if (line.startsWith("# 连词")) {
                    currentCategory = "连词";
                } else if (line.startsWith("# 介词")) {
                    currentCategory = "介词";
                } else if (line.startsWith("# 量词")) {
                    currentCategory = "量词";
                } else if (line.startsWith("# 网络用语")) {
                    currentCategory = "网络";
                } else if (line.startsWith("# 口语化表达")) {
                    currentCategory = "口语";
                } else if (!line.isEmpty() && !line.startsWith("#")) {
                    // 统计词汇
                    switch (currentCategory) {
                        case "基础": basicWords++; break;
                        case "代词": pronouns++; break;
                        case "助词": particles++; break;
                        case "时间": timeWords++; break;
                        case "副词": adverbs++; break;
                        case "连词": conjunctions++; break;
                        case "介词": prepositions++; break;
                        case "量词": quantifiers++; break;
                        case "网络": internetSlang++; break;
                        case "口语": colloquial++; break;
                        default: others++; break;
                    }
                }
            }
            
        } catch (IOException e) {
            System.err.println("读取停用词统计信息失败: " + e.getMessage());
        }

        return new StopWordsInfo(stopWords.size(), basicWords, pronouns, particles, timeWords, 
                                adverbs, conjunctions, prepositions, quantifiers, internetSlang, 
                                colloquial + others, fileName);
    }

    /**
     * 添加新的停用词到文件
     * 注意：这个方法只是示例，实际使用时需要考虑文件写入权限
     */
    public static void addStopWords(List<String> newWords, String category) {
        System.out.println("建议将以下停用词添加到 " + DEFAULT_STOPWORDS_FILE + " 文件的 " + category + " 分类中：");
        for (String word : newWords) {
            System.out.println("+ " + word);
        }
        System.out.println("请手动编辑文件以添加这些词汇。");
    }

    /**
     * 清除缓存，强制重新加载
     */
    public static void clearCache() {
        stopWordsCache = null;
    }

    /**
     * 停用词信息统计类
     */
    public static class StopWordsInfo {
        private final int totalCount;
        private final int basicWords;
        private final int pronouns;
        private final int particles;
        private final int timeWords;
        private final int adverbs;
        private final int conjunctions;
        private final int prepositions;
        private final int quantifiers;
        private final int internetSlang;
        private final int others;
        private final String fileName;

        public StopWordsInfo(int totalCount, int basicWords, int pronouns, int particles, 
                           int timeWords, int adverbs, int conjunctions, int prepositions, 
                           int quantifiers, int internetSlang, int others, String fileName) {
            this.totalCount = totalCount;
            this.basicWords = basicWords;
            this.pronouns = pronouns;
            this.particles = particles;
            this.timeWords = timeWords;
            this.adverbs = adverbs;
            this.conjunctions = conjunctions;
            this.prepositions = prepositions;
            this.quantifiers = quantifiers;
            this.internetSlang = internetSlang;
            this.others = others;
            this.fileName = fileName;
        }

        @Override
        public String toString() {
            return String.format(
                "停用词统计信息 [文件: %s]\n" +
                "总计: %d 个\n" +
                "- 基础停用词: %d\n" +
                "- 代词: %d\n" +
                "- 助词和语气词: %d\n" +
                "- 时间词: %d\n" +
                "- 程度副词: %d\n" +
                "- 连词: %d\n" +
                "- 介词: %d\n" +
                "- 量词: %d\n" +
                "- 网络用语: %d\n" +
                "- 其他: %d",
                fileName, totalCount, basicWords, pronouns, particles, timeWords,
                adverbs, conjunctions, prepositions, quantifiers, internetSlang, others
            );
        }

        // Getters
        public int getTotalCount() { return totalCount; }
        public int getBasicWords() { return basicWords; }
        public int getPronouns() { return pronouns; }
        public int getParticles() { return particles; }
        public int getTimeWords() { return timeWords; }
        public int getAdverbs() { return adverbs; }
        public int getConjunctions() { return conjunctions; }
        public int getPrepositions() { return prepositions; }
        public int getQuantifiers() { return quantifiers; }
        public int getInternetSlang() { return internetSlang; }
        public int getOthers() { return others; }
        public String getFileName() { return fileName; }
    }
} 