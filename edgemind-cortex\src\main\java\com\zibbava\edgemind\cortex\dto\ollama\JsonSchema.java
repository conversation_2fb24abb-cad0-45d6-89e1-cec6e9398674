package com.zibbava.edgemind.cortex.dto.ollama;

import lombok.Builder;
import lombok.Data;

import java.util.*;

/**
 * JSON Schema格式定义类
 */
@Data
@Builder
public class JsonSchema {
    private String type;
    private Map<String, JsonSchemaProperty> properties;
    private List<String> required;
    private String description;

    public static JsonSchema createObject() {
        return JsonSchema.builder()
                .type("object")
                .properties(new HashMap<>())
                .required(new ArrayList<>())
                .build();
    }

    public JsonSchema addProperty(String name, JsonSchemaProperty property) {
        if (this.properties == null) {
            this.properties = new HashMap<>();
        }
        this.properties.put(name, property);
        return this;
    }

    public JsonSchema addRequired(String... fields) {
        if (this.required == null) {
            this.required = new ArrayList<>();
        }
        this.required.addAll(Arrays.asList(fields));
        return this;
    }
} 