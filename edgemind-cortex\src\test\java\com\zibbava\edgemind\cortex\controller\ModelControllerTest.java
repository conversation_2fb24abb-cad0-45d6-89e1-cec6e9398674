package com.zibbava.edgemind.cortex.controller;

import com.zibbava.edgemind.cortex.base.BaseControllerTest;
import com.zibbava.edgemind.cortex.service.ModelService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ModelController 单元测试
 * 测试模型相关接口功能
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
@DisplayName("模型控制器测试")
class ModelControllerTest extends BaseControllerTest {

    @MockBean
    private ModelService modelService;

    private List<Map<String, String>> modelList;

    @Override
    protected void setUp() {
        Map<String, String> model1 = new HashMap<>();
        model1.put("id", "llama3.2");
        model1.put("name", "Llama 3.2");

        Map<String, String> model2 = new HashMap<>();
        model2.put("id", "qwen2.5");
        model2.put("name", "Qwen 2.5");

        modelList = Arrays.asList(model1, model2);
    }

    @Test
    @DisplayName("获取模型列表 - 成功")
    void getModels_Success() throws Exception {
        // Given
        when(modelService.getModelsForFrontend()).thenReturn(modelList);

        // When & Then
        mockMvc.perform(withAuth(get("/api/models")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").value("llama3.2"))
                .andExpect(jsonPath("$[0].name").value("Llama 3.2"))
                .andExpect(jsonPath("$[1].id").value("qwen2.5"))
                .andExpect(jsonPath("$[1].name").value("Qwen 2.5"));

        verify(modelService).getModelsForFrontend();
    }

    @Test
    @DisplayName("获取模型列表 - 空列表")
    void getModels_EmptyList() throws Exception {
        // Given
        when(modelService.getModelsForFrontend()).thenReturn(Arrays.asList());

        // When & Then
        mockMvc.perform(withAuth(get("/api/models")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isEmpty());

        verify(modelService).getModelsForFrontend();
    }

    @Test
    @DisplayName("获取模型列表 - 服务异常")
    void getModels_ServiceException() throws Exception {
        // Given
        when(modelService.getModelsForFrontend()).thenThrow(new RuntimeException("服务异常"));

        // When & Then
        mockMvc.perform(withAuth(get("/api/models")))
                .andExpect(status().isInternalServerError());

        verify(modelService).getModelsForFrontend();
    }
} 