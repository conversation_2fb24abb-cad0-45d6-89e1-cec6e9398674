package com.zibbava.edgemind.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 免费试用记录实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("free_trial_record")
public class FreeTrialRecord {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 硬件指纹
     */
    @TableField("hardware_fingerprint")
    private String hardwareFingerprint;
    
    /**
     * 系统标识符
     */
    @TableField("system_identifier")
    private String systemIdentifier;
    
    /**
     * 许可证密钥
     */
    @TableField("license_key")
    private String licenseKey;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 过期时间
     */
    @TableField("expire_time")
    private LocalDateTime expireTime;
}