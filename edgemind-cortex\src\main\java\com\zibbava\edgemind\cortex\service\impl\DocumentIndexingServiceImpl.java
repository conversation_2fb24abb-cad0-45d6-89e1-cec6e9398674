package com.zibbava.edgemind.cortex.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zibbava.edgemind.cortex.common.enums.VectorStatus;
import com.zibbava.edgemind.cortex.entity.DocumentChunk;
import com.zibbava.edgemind.cortex.entity.KnowledgeDocument;
import com.zibbava.edgemind.cortex.entity.KnowledgeNode;
import com.zibbava.edgemind.cortex.entity.KnowledgeSpace;
import com.zibbava.edgemind.cortex.mapper.DocumentChunkMapper;
import com.zibbava.edgemind.cortex.mapper.KnowledgeDocumentMapper;
import com.zibbava.edgemind.cortex.mapper.KnowledgeNodeMapper;
import com.zibbava.edgemind.cortex.mapper.KnowledgeSpaceMapper;
import com.zibbava.edgemind.cortex.service.DocumentIndexingService;
import com.zibbava.edgemind.cortex.strategy.DocumentParsingStrategyFactory;
import com.zibbava.edgemind.cortex.util.FileUtils;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingStore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 异步文档索引服务实现类。
 * 负责处理文档内容的向量化、存储到向量数据库以及从向量数据库删除。
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentIndexingServiceImpl implements DocumentIndexingService {

    private final KnowledgeDocumentMapper documentMapper;
    private final KnowledgeNodeMapper nodeMapper;
    private final KnowledgeSpaceMapper spaceMapper;
    private final DocumentChunkMapper chunkMapper;
    private final EmbeddingModel embeddingModel;
    private final EmbeddingStore<TextSegment> embeddingStore;
    @Lazy
    private final FileUtils fileUtils;
    private final DocumentParsingStrategyFactory parsingStrategyFactory;

    @Value("${rag.splitter.chunkSize:400}")
    private int chunkSize; // 文档分块大小
    @Value("${rag.splitter.chunkOverlap:100}")
    private int chunkOverlap; // 文档分块重叠大小

    /**
     * 异步执行文档索引。
     * 包括：读取文件、解析内容、分块、生成向量、存入向量库、更新数据库状态。
     * 在存储前会先尝试删除该文档已存在的旧向量数据。
     */
    @Async("asyncTaskExecutor")
    @Override
    public void startIndexing(String documentId) {
        log.info("[ASYNC] 开始处理文档索引任务: documentId={}", documentId);
        KnowledgeDocument document = documentMapper.selectById(documentId);
        if (document == null) {
            log.error("[ASYNC] 文档记录未找到，无法索引: documentId={}", documentId);
            return;
        }
        KnowledgeNode node = nodeMapper.selectById(document.getNodeId());
        if (node == null) {
            log.error("[ASYNC] 关联节点未找到，无法索引: documentId={}, nodeId={}", documentId, document.getNodeId());
            updateDocumentStatus(documentId, VectorStatus.FAILED, "关联节点未找到");
            return;
        }
        KnowledgeSpace space = spaceMapper.selectById(node.getSpaceId());
        if (space == null) {
            log.error("[ASYNC] 关联空间未找到，无法索引: documentId={}, spaceId={}", documentId, node.getSpaceId());
            updateDocumentStatus(documentId, VectorStatus.FAILED, "关联空间未找到");
            return;
        }

        // 1. 更新状态为处理中
        updateDocumentStatus(documentId, VectorStatus.PROCESSING, null);

        try {
            // 2. 加载和解析文档
            // 使用节点ID而不是filePath来定位文件
            byte[] fileContent = fileUtils.readFile(document.getNodeId());
            
            // 使用策略模式解析文档
            Document langDocument = null;
            try {
                // 根据文件名选择合适的解析策略
                var parsingStrategy = parsingStrategyFactory.getStrategy(node.getName());
                langDocument = parsingStrategy.parseDocument(fileContent, node.getName());
                
            } catch (Exception e) {
                log.error("[ASYNC] 解析文档失败: documentId={}, fileName={}", documentId, node.getName(), e);
                updateDocumentStatus(documentId, VectorStatus.FAILED, "解析文档失败: " + e.getMessage());
                return;
            }
            if (langDocument == null) {
                log.error("[ASYNC] 解析文档失败: documentId={}", documentId);
                updateDocumentStatus(documentId, VectorStatus.FAILED, "解析文档失败");
                return;
            }

            // 3. 丰富文档元数据 (用于存储到 Weaviate)
            enrichDocumentMetadata(langDocument, node, document, space);

            // 4. 先删除该节点已存在的向量数据和分片记录
            deleteExistingVectorsInternal(node.getNodeId());
            deleteExistingChunksInternal(documentId);

            // 5. 配置分块器和分块
            DocumentSplitter splitter = DocumentSplitters.recursive(chunkSize, chunkOverlap);
            log.info("[ASYNC] 开始对文档进行分块、嵌入和存储: documentId={}", documentId);
            List<TextSegment> segments = splitter.split(langDocument);

            // 6. 手动处理每个文本片段的向量化和存储，同时记录分片信息

            for (int i = 0; i < segments.size(); i++) {
                TextSegment segment = segments.get(i);

                // 确保每个segment都包含完整的元数据，特别是索引字段
                // 从langDocument的元数据中获取已设置的node_path，避免重复计算
                TextSegment enrichedSegment = TextSegment.from(
                    segment.text(),
                    segment.metadata().copy()
                        .put("node_id", langDocument.metadata().getString("node_id"))
                        .put("space_id", langDocument.metadata().getString("space_id"))
                        .put("file_name", langDocument.metadata().getString("file_name"))
                        .put("user_id", langDocument.metadata().getString("user_id"))
                        .put("mime_type", langDocument.metadata().getString("mime_type"))
                        .put("created_time", langDocument.metadata().getString("created_time"))
                        .put("updated_time", langDocument.metadata().getString("updated_time"))
                        .put("chunk_index", String.valueOf(i))
                        .put("chunk_size", String.valueOf(segment.text().length()))
                        .put("document_id", documentId)
                        .put("node_path", langDocument.metadata().getString("node_path"))
                );

                // 生成分片ID
                String chunkId = UUID.randomUUID().toString();

                // 生成并存储向量
                Embedding embedding = embeddingModel.embed(enrichedSegment).content();
                String vectorId = embeddingStore.add(embedding, enrichedSegment);

                // 创建分片记录
                DocumentChunk chunkRecord = new DocumentChunk();
                chunkRecord.setDocumentId(documentId);
                chunkRecord.setNodeId(node.getNodeId());
                chunkRecord.setChunkId(vectorId != null ? vectorId : chunkId);
                chunkRecord.setChunkIndex(i);
                chunkRecord.setChunkSize(segment.text().length());

                chunkMapper.insert(chunkRecord);
            }

            log.info("[ASYNC] 文档嵌入和存储完成: documentId={}", documentId);

            // 7. 更新状态为已索引
            updateDocumentStatus(documentId, VectorStatus.INDEXED, null);
            log.info("[ASYNC] 文档索引成功完成: documentId={}", documentId);

        } catch (Exception e) {
            log.error("[ASYNC] 文档索引过程中发生错误: documentId={}. Error: {}", documentId, e.getMessage(), e);
            // 8. 更新状态为失败
            updateDocumentStatus(documentId, VectorStatus.FAILED, e.getMessage());
        }
    }

    /**
     * 异步清理指定节点关联的所有向量数据。
     */
    @Async("asyncTaskExecutor")
    @Override
    public void deleteVectors(String nodeId) {
        log.info("[ASYNC] 开始处理节点向量删除任务: nodeId={}", nodeId);
        try {
            // 先获取该节点下所有分片ID
            deleteExistingVectorsInternal(nodeId);

            // 最后删除分片记录
            LambdaQueryWrapper<DocumentChunk> wrapper = new LambdaQueryWrapper<DocumentChunk>()
                    .eq(DocumentChunk::getNodeId, nodeId);
            int count = chunkMapper.delete(wrapper);
            log.info("[ASYNC] 已删除节点{}关联的{}条分片记录", nodeId, count);

            log.info("[ASYNC] 节点向量删除任务完成: nodeId={}", nodeId);
        } catch (Exception e) {
            log.error("[ASYNC] 删除节点向量时出错: nodeId={}. Error: {}", nodeId, e.getMessage(), e);
            // 注意：这里的异常仅被记录，可根据需要添加重试或失败处理逻辑
        }
    }

    /**
     * 清空所有向量数据和分片记录
     * 在同步知识库或重置知识库时使用
     */
    @Override
    public void clearAllVectors() {
        log.info("开始清空所有向量数据和分片记录");
        try {
            embeddingStore.removeAll();
            int deletedCount = chunkMapper.delete(new LambdaQueryWrapper<>());
            log.info("成功删除 {} 条分片记录", deletedCount);

            log.info("清空向量数据和分片记录完成");
        } catch (Exception e) {
            log.error("清空向量数据和分片记录时出错: {}", e.getMessage(), e);
            throw new RuntimeException("清空向量数据失败", e);
        }
    }


    /**
     * 内部方法：删除指定节点 ID 关联的所有向量。
     * 基于分片映射表记录，逐个删除向量。
     */
    private void deleteExistingVectorsInternal(String nodeId) {
        log.info("删除节点 {} 关联的向量数据", nodeId);
        try {
            // 先获取该节点下所有分片ID
            List<String> chunkIds = chunkMapper.findChunkIdsByNodeId(nodeId);

            if (chunkIds != null && !chunkIds.isEmpty()) {
                // 逐个删除向量
                int successCount = 0;
                for (String chunkId : chunkIds) {
                    try {
                        embeddingStore.remove(chunkId);
                        successCount++;
                    } catch (Exception e) {
                        log.warn("删除向量ID={}时出错: {}", chunkId, e.getMessage());
                    }
                }
                log.info("成功删除节点 {} 关联的 {} 个向量，总计 {} 个", nodeId, successCount, chunkIds.size());
            } else {
                log.info("节点 {} 没有找到关联的分片记录", nodeId);
            }
        } catch (Exception e) {
            log.error("删除节点 {} 关联向量时失败: {}", nodeId, e.getMessage(), e);
            throw new RuntimeException("删除向量数据失败", e);
        }
    }

    /**
     * 内部方法：删除指定文档ID关联的所有分片记录
     */
    private void deleteExistingChunksInternal(String documentId) {
        log.info("删除文档 {} 关联的所有分片记录", documentId);
        try {
            LambdaQueryWrapper<DocumentChunk> wrapper = new LambdaQueryWrapper<DocumentChunk>()
                    .eq(DocumentChunk::getDocumentId, documentId);
            int count = chunkMapper.delete(wrapper);
            log.info("成功删除文档 {} 关联的 {} 条分片记录", documentId, count);
        } catch (Exception e) {
            log.error("删除文档 {} 分片记录时失败: {}", documentId, e.getMessage(), e);
            throw new RuntimeException("删除分片记录失败", e);
        }
    }

    /**
     * 内部方法：更新数据库中文档的向量化状态和错误信息。
     */
    private void updateDocumentStatus(String documentId, VectorStatus status, String errorMessage) {
        try {
            KnowledgeDocument update = new KnowledgeDocument();
            update.setDocumentId(documentId);
            update.setVectorStatus(status);
            // 截断错误信息以适应数据库字段长度
            update.setVectorErrorMessage(errorMessage != null ? errorMessage.substring(0, Math.min(errorMessage.length(), 2000)) : null);
            if (status == VectorStatus.INDEXED) {
                update.setLastIndexedTime(LocalDateTime.now());
            } else if (status == VectorStatus.FAILED) {
                update.setLastIndexedTime(null); // 失败时清除索引时间
            }
            documentMapper.updateById(update);
            log.debug("更新文档状态: documentId={}, status={}, error={}", documentId, status, errorMessage != null);
        } catch (Exception e) {
            log.error("更新文档数据库状态时失败: documentId={}, status={}, Error: {}", documentId, status, e.getMessage(), e);
            // 数据库更新失败是严重问题，需要关注
        }
    }

    /**
     * 内部方法：为 Langchain4j Document 对象填充元数据。
     */
    private void enrichDocumentMetadata(Document document, KnowledgeNode node, KnowledgeDocument docEntity, KnowledgeSpace space) {
        document.metadata().put("node_id", node.getNodeId());
        document.metadata().put("space_id", node.getSpaceId());
        document.metadata().put("file_name", node.getName());
        document.metadata().put("user_id", String.valueOf(node.getCreateBy()));
        document.metadata().put("mime_type", docEntity.getMimeType());
        
        // 添加完整节点ID路径
        try {
            List<KnowledgeNode> nodePath = fileUtils.getNodePath(node.getNodeId());
            String fullPath = "/" + nodePath.stream()
                    .map(KnowledgeNode::getNodeId)
                    .collect(java.util.stream.Collectors.joining("/"));
            document.metadata().put("node_path", fullPath);
            log.debug("节点 {} 的完整ID路径: {}", node.getNodeId(), fullPath);
        } catch (Exception e) {
            log.warn("获取节点 {} 完整ID路径失败: {}", node.getNodeId(), e.getMessage());
            // 如果获取路径失败，至少保存节点ID
            document.metadata().put("node_path", "/" + node.getNodeId());
        }
        
        // filePath字段已移除，不再添加到元数据中
        if (node.getCreateTime() != null) {
            document.metadata().put("created_time", node.getCreateTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toString());
        }
        if (node.getUpdateTime() != null) {
            document.metadata().put("updated_time", node.getUpdateTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toString());
        }
        // 注意：chunk_index 通常由 DocumentSplitter 自动添加，无需手动设置
        log.debug("已为文档 {} 填充元数据", docEntity.getDocumentId());
    }
}