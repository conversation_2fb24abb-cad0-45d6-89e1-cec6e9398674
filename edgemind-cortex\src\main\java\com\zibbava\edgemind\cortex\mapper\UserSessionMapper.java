package com.zibbava.edgemind.cortex.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zibbava.edgemind.cortex.entity.UserSession;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户会话 Mapper 接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface UserSessionMapper extends BaseMapper<UserSession> {

    /**
     * 根据用户ID查询活跃会话
     * 
     * @param userId 用户ID
     * @return 活跃会话列表
     */
    @Select("SELECT * FROM sys_user_session WHERE user_id = #{userId} AND status = 1 AND expire_time > NOW()")
    List<UserSession> selectActiveSessionsByUserId(@Param("userId") Long userId);

    /**
     * 根据Token查询会话
     * 
     * @param token 访问令牌
     * @return 用户会话
     */
    @Select("SELECT * FROM sys_user_session WHERE token = #{token}")
    UserSession selectByToken(@Param("token") String token);

    /**
     * 根据会话ID查询会话
     * 
     * @param sessionId 会话ID
     * @return 用户会话
     */
    @Select("SELECT * FROM sys_user_session WHERE session_id = #{sessionId}")
    UserSession selectBySessionId(@Param("sessionId") String sessionId);

    /**
     * 更新会话最后访问时间
     * 
     * @param sessionId 会话ID
     * @param lastAccessTime 最后访问时间
     * @return 更新行数
     */
    @Update("UPDATE sys_user_session SET last_access_time = #{lastAccessTime} WHERE session_id = #{sessionId}")
    int updateLastAccessTime(@Param("sessionId") String sessionId, @Param("lastAccessTime") LocalDateTime lastAccessTime);

    /**
     * 使指定用户的所有会话失效
     * 
     * @param userId 用户ID
     * @return 更新行数
     */
    @Update("UPDATE sys_user_session SET status = 0 WHERE user_id = #{userId}")
    int invalidateUserSessions(@Param("userId") Long userId);

    /**
     * 使指定会话失效
     * 
     * @param sessionId 会话ID
     * @return 更新行数
     */
    @Update("UPDATE sys_user_session SET status = 0 WHERE session_id = #{sessionId}")
    int invalidateSession(@Param("sessionId") String sessionId);

    /**
     * 清理过期会话
     * 
     * @return 清理的会话数量
     */
    @Update("DELETE FROM sys_user_session WHERE expire_time < NOW() OR status = 0")
    int cleanExpiredSessions();

    /**
     * 统计在线用户数
     * 
     * @return 在线用户数
     */
    @Select("SELECT COUNT(DISTINCT user_id) FROM sys_user_session WHERE status = 1 AND expire_time > NOW()")
    Long countOnlineUsers();
}
