package com.zibbava.edgemind.cortex.dto.ollama;

import lombok.Builder;
import lombok.Data;

/**
 * Ollama聊天响应类
 */
@Data
@Builder
public class OllamaChatResponse {
    private String model;
    private String createdAt;
    private OllamaMessage message;
    private Boolean done;
    private String doneReason;
    
    // 性能统计信息（仅在done=true时返回）
    private Long totalDuration;
    private Long loadDuration;
    private Integer promptEvalCount;
    private Long promptEvalDuration;
    private Integer evalCount;
    private Long evalDuration;
} 