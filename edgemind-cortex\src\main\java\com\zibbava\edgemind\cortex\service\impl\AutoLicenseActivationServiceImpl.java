package com.zibbava.edgemind.cortex.service.impl;

import com.zibbava.edgemind.cortex.service.AutoLicenseActivationService;
import com.zibbava.edgemind.cortex.service.LicenseService;
import com.zibbava.edgemind.cortex.service.SystemSettingsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 自动许可证激活服务实现类
 */
@Service
@Slf4j
public class AutoLicenseActivationServiceImpl implements AutoLicenseActivationService {

    @Autowired
    private LicenseService licenseService;

    @Autowired
    private SystemSettingsService systemSettingsService;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${app.license.server.url}")
    private String licenseServerUrl;

    @Value("${app.version}")
    private String appVersion;

    /**
     * 检查并自动激活免费试用许可证
     */
    @Override
    public void checkAndActivateFreeTrial() {
        if (!needsAutoActivation()) {
            log.debug("系统已激活或不需要自动激活");
            return;
        }

        try {
            log.info("开始自动申请免费试用许可证");
            
            // 获取硬件指纹和系统标识符
            String hardwareFingerprint = licenseService.getHardwareFingerprint();
            String systemIdentifier = systemSettingsService.getSystemIdentifier();
            
            if (hardwareFingerprint == null || systemIdentifier == null) {
                log.error("无法获取硬件指纹或系统标识符，跳过自动激活");
                return;
            }

            // 调用后台API申请免费试用许可证
            String licenseKey = requestFreeTrialLicense(hardwareFingerprint, systemIdentifier);
            
            if (licenseKey != null) {
                // 激活许可证
                boolean activated = licenseService.activateLicense(licenseKey);
                if (activated) {
                    log.info("免费试用许可证自动激活成功");
                } else {
                    log.error("免费试用许可证激活失败");
                }
            } else {
                log.warn("免费试用许可证申请失败，可能该硬件指纹已使用过免费额度");
            }
        } catch (Exception e) {
            log.error("自动激活免费试用许可证时发生异常", e);
        }
    }

    /**
     * 检查系统是否需要自动激活
     */
    @Override
    public boolean needsAutoActivation() {
        try {
            // 检查系统是否已经激活
            boolean isLicensed = licenseService.isLicensed();
            if (isLicensed) {
                log.debug("系统已激活，无需自动激活");
                return false;
            }

            // 检查是否已经尝试过自动激活（避免重复尝试）
            String autoActivationFlag = systemSettingsService.getSettingValue("auto_activation_attempted", "0");
            if ("1".equals(autoActivationFlag)) {
                log.debug("已尝试过自动激活，跳过");
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("检查自动激活需求时发生异常", e);
            return false;
        }
    }

    /**
     * 向后台服务器请求免费试用许可证
     */
    private String requestFreeTrialLicense(String hardwareFingerprint, String systemIdentifier) {
        try {
            String url = licenseServerUrl + "/aistudio/api/license-records/free-trial/generate";
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("hardwareFingerprint", hardwareFingerprint);
            requestBody.put("systemIdentifier", systemIdentifier);
            requestBody.put("clientVersion", appVersion);
            requestBody.put("token", "wkg-token-3321");
            requestBody.put("remark", "自动激活免费试用许可证");

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            // 发送请求
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                Integer code = (Integer) responseBody.get("code");
                
                if (code != null && code == 200) {
                    String licenseKey = (String) responseBody.get("data");
                    log.info("成功获取免费试用许可证");
                    
                    // 标记已尝试过自动激活
                    systemSettingsService.updateSettingValue("auto_activation_attempted", "1", "自动激活尝试标记");
                    
                    return licenseKey;
                } else {
                    String message = (String) responseBody.get("message");
                    log.warn("申请免费试用许可证失败: {}", message);
                }
            } else {
                log.error("申请免费试用许可证请求失败，状态码: {}", response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("请求免费试用许可证时发生异常", e);
        }
        
        // 即使失败也标记已尝试过，避免重复尝试
        try {
            systemSettingsService.updateSettingValue("auto_activation_attempted", "1", "自动激活尝试标记");
        } catch (Exception e) {
            log.error("标记自动激活尝试状态失败", e);
        }
        
        return null;
    }
}