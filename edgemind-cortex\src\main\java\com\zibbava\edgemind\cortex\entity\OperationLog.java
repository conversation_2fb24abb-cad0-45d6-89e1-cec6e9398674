package com.zibbava.edgemind.cortex.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 系统操作日志实体类
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_operation_log")
public class OperationLog {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("username")
    private String username;

    @TableField("operation_type")
    private OperationType operationType;

    @TableField("module")
    private String module;

    @TableField("description")
    private String description;

    @TableField("request_url")
    private String requestUrl;

    @TableField("method")
    private String requestMethod;

    @TableField("request_params")
    private String requestParams;

    @TableField("response_result")
    private String responseResult;

    @TableField("ip_address")
    private String ipAddress;

    @TableField("user_agent")
    private String userAgent;

    @TableField("execution_time")
    private Long executionTime;

    @TableField("status")
    private Integer status; // 0:失败, 1:成功

    @TableField("error_message")
    private String errorMessage;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        CREATE("新增"),
        UPDATE("修改"),
        DELETE("删除"),
        LOGIN("登录"),
        LOGOUT("登出"),
        QUERY("查询"),
        EXPORT("导出"),
        IMPORT("导入"),
        UPLOAD("上传"),
        DOWNLOAD("下载"),
        RESET_PASSWORD("重置密码"),
        ASSIGN_ROLE("分配角色"),
        GRANT_PERMISSION("授权"),
        OTHER("其他");

        private final String description;

        OperationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 操作状态常量
     */
    public static class Status {
        public static final int FAILED = 0;
        public static final int SUCCESS = 1;
    }
}
