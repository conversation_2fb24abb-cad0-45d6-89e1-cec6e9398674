package com.zibbava.edgemind.cortex.service.strategy;

import com.zibbava.edgemind.cortex.dto.ChatContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 聊天策略管理器 - 负责选择合适的策略处理请求
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ChatStrategyManager {
    
    private final List<ChatStrategy> strategies;
    
    /**
     * 根据上下文选择合适的策略
     * @param context 聊天上下文
     * @return 适用的策略
     */
    public ChatStrategy getStrategy(ChatContext context) {
        return strategies.stream()
                .filter(strategy -> strategy.isApplicable(context))
                .findFirst()
                .orElseThrow(() -> {
                    log.error("没有找到适用的聊天策略: {}", context);
                    return new IllegalArgumentException("没有找到适用的聊天策略");
                });
    }
}
