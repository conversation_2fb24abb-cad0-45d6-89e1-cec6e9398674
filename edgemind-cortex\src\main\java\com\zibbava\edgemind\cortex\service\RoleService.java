package com.zibbava.edgemind.cortex.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zibbava.edgemind.cortex.entity.Role;

import java.util.List;

public interface RoleService extends IService<Role> {

    /**
     * 根据用户ID查询角色列表
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> findRolesByUserId(Long userId);
    
    /**
     * 根据用户ID列表查询相关的所有角色列表 (去重)
     * @param userIds 用户ID列表
     * @return 相关角色列表
     */
    List<Role> findRolesByUserIds(List<Long> userIds);

    /**
     * 给角色分配权限
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    void assignPermissionsToRole(Long roleId, List<Long> permissionIds);

    /**
     * 根据角色ID获取权限ID列表
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> getPermissionIdsByRoleId(Long roleId);

    /**
     * 根据角色编码查询角色
     * @param roleCode 角色编码
     * @return 角色
     */
    Role findByRoleCode(String roleCode);

    /**
     * 检查角色编码是否存在
     * @param roleCode 角色编码
     * @param excludeRoleId 排除的角色ID
     * @return 是否存在
     */
    boolean isRoleCodeExists(String roleCode, Long excludeRoleId);

    /**
     * 检查角色名称是否存在
     * @param roleName 角色名称
     * @param excludeRoleId 排除的角色ID
     * @return 是否存在
     */
    boolean isRoleNameExists(String roleName, Long excludeRoleId);

    /**
     * 获取所有启用的角色
     * @return 角色列表
     */
    List<Role> getAllEnabledRoles();

    /**
     * 获取用户角色编码列表
     * @param userId 用户ID
     * @return 角色编码列表
     */
    List<String> getUserRoles(Long userId);

    /**
     * 检查用户是否有指定角色
     * @param userId 用户ID
     * @param roleCode 角色编码
     * @return 是否有角色
     */
    boolean hasRole(Long userId, String roleCode);
}