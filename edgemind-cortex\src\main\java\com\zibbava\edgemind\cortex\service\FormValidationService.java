package com.zibbava.edgemind.cortex.service;

import com.zibbava.edgemind.cortex.dto.ValidationRequest;
import com.zibbava.edgemind.cortex.dto.ValidationResponse;
import java.util.Map;

/**
 * 表单验证服务接口
 * 提供统一的表单验证功能
 */
public interface FormValidationService {
    
    /**
     * 验证表单数据
     * @param request 验证请求
     * @return 验证结果
     */
    ValidationResponse validateForm(ValidationRequest request);
    
    /**
     * 验证用户表单
     * @param formData 表单数据
     * @param mode 验证模式 (create/update)
     * @param excludeUserId 排除的用户ID (更新时使用)
     * @return 验证结果
     */
    ValidationResponse validateUserForm(Map<String, Object> formData, String mode, Long excludeUserId);
    
    /**
     * 验证角色表单
     * @param formData 表单数据
     * @param mode 验证模式 (create/update)
     * @param excludeRoleId 排除的角色ID (更新时使用)
     * @return 验证结果
     */
    ValidationResponse validateRoleForm(Map<String, Object> formData, String mode, Long excludeRoleId);

    /**
     * 验证部门表单
     * @param formData 表单数据
     * @param mode 验证模式 (create/update)
     * @param excludeDeptId 排除的部门ID (更新时使用)
     * @return 验证结果
     */
    ValidationResponse validateDepartmentForm(Map<String, Object> formData, String mode, Long excludeDeptId);

    /**
     * 验证权限表单
     * @param formData 表单数据
     * @param mode 验证模式 (create/update)
     * @param excludePermissionId 排除的权限ID (更新时使用)
     * @return 验证结果
     */
    ValidationResponse validatePermissionForm(Map<String, Object> formData, String mode, Long excludePermissionId);
    
    /**
     * 验证字段唯一性
     * @param tableName 表名
     * @param fieldName 字段名
     * @param fieldValue 字段值
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    boolean isFieldUnique(String tableName, String fieldName, Object fieldValue, Long excludeId);
}
